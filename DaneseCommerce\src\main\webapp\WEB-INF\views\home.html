<!DOCTYPE html>
<html th:with="activePage=Home" 
	th:lang="${#locale.language}"
	xmlns:th="http://www.thymeleaf.org" 
	xmlns:yada="http://www.yadaframework.net">
	<head>
	   <meta charset="UTF-8"> <!--/* This is needed so that Eclipse sets the correct character set */-->
		<th:block th:insert="~{/header :: head}"/>
	</head>
	<body>
		<!-- Header/menu per fare replace dalla pagina del header -->
		<th:block th:insert="~{/header :: body}"/>
		
		<div id="idNewsletter" class="hidden">
			<button type="button" class="close" aria-label="Close"><i class="icon-x"></i></button>
			<a th:href="@{/newsletter}" class="noLoader">
				<span th:utext="#{home.join.newsletter.title}">UNISCITI ALLA NOSTRA NEWSLETTER</span><br>
				<br class="visible-xs">
				<span th:utext="#{home.join.newsletter.subtitle}">ISCRIVITI E RICEVI TUTTI GLI AGGIORNAMENTI.</span><br>
				<span class="icon-forward"></span>
		   </a>
	</div>
		
		
		<div class="page-containar home-video-div" style="visibility:hidden;">
			<div class="carousel-inner" role="listbox">
				<div >
				   <video muted autoplay loop style="width:100%;" class="video-content">
						<source th:src="@{/contents/media/Composizione 2025.mp4}" type="video/mp4">
					</video>   
										
					 <!--/* Immagine fissa lunga per cover - Enzo Mari
					<img class="visible-xs homeCoverImage video-content" src="/contents/media/Enzo-Mari-Danese_3_web.jpg" alt="Enzo Mari Danese" width="1600" height="400">
					<img class="hidden-xs homeCoverImage video-content" src="/contents/media/Enzo-Mari-Danese_Desktop.jpg" alt="Enzo Mari Danese" width="1600" height="400">
					*/-->
					
					<div class="video-content-over" style="pointer-events: none;">
						<div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 video-content-block">        
							
						</div>
						<div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 video-content-block">        
							
						</div>
						<div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 video-content-block">        
							
						</div>
						<div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 video-content-block last">        
							
						</div>
					</div>
				</div>
				
			<!--/* Diviso in 2 immagine a sinistra e scritta a destra
				<div class="carousel-inner homePage videoEventContainer" role="listbox">
				<div class="col-xs-12 col-md-6 videoContainer">
				<a href="https://www.danesemilano.com/it/news">
					<video muted autoplay loop class="video-content hidden-xs" style="width: inherit;">
						<source src="/contents/media/logo_animato.mp4" type="video/mp4" >
					</video>
					<picture class="visible-xs">
						<img src="/contents/media/danese.19.jpg">
					</picture>
				</a>
				</div>
				<div class="homePage eventContainer col-xs-12 col-md-6">
				   	<br>
				   	<h5>Design Week 2019</h5>
				   	<br>
				   	<h4>RICUCITURE<br/>A cura di Giulio Iacchetti</h4>
				   	<br>
				   	<p class="homePage paragraphVideo">Anteprima ufficiale - Sabato 6 Aprile 2019, ore 19.00<br>
				   	Showroom Danese - Piazza San Nazaro in Brolo, 15</p>
				   	<p class="homePage paragraphVideo">Sarà presentata la Collezione Danese 2019.<br/>
				   	presso lo showroom Danese in Piazza San Nazaro in Brolo, 15<br/>
				   	9-14 aprile, ore 10.00 - 21.00</p>
			   </div> */-->
		  <!--  <div class="video-content-over" style="pointer-events: none;">
					<div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 video-content-block">        
						
					</div>
					<div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 video-content-block">        
						
					</div>
					<div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 video-content-block">        
						
					</div>
					<div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 video-content-block last">        
						
					</div>
				</div> -->
			</div>
		</div>
		
		<th:block th:replace="~{/footer :: body}"/>
		
	   	<th:block th:if="${login}!=null">
		<div th:replace="~{/modalLogin :: #loginModal}"></div>
	</th:block>

	<script type="text/javascript" th:inline="javascript">
		//<![CDATA[
			<!--/* Modal eccezione non gestita */-->
			if ([[${yadaExceptionObject!=null}]]==true) {
				yada.showErrorModal([[#{yada.error.http.other}]], [[${yadaExceptionObject!=null?yadaExceptionObject.getMessage():''}]]);
			}
			
			<!--/* Modal errore HTTP */-->
			if ([[${yadaHttpStatus!=null}]]==true) {
				yada.showErrorModal([[${#messages.msg('yada.error.http.title', yadaHttpStatus)}]], [[#{|yada.error.http.${yadaHttpStatus}|}]]);
			}
		//]]>
	</script> 
		
		<script type="text/javascript">
			function mobileViewFunction() {
				var viewSize = window.matchMedia("(max-width: 866px)")
				if (viewSize.matches) {
					$(".logo-containar").addClass("center-block designers-title");
					var offset = $(window).height() - $('footer').outerHeight() - $('.logo-containar').outerHeight() - 165;
				   /*  $('.home-video-div').height(offset); */
					
					var mobileDropDownHeight = $(window).height()- $('.logo-containar').outerHeight() - $('#mobileMenu').outerHeight()-30;
					$('#mobile-dropdown').height(mobileDropDownHeight);
				}
				else
				{
					$(".logo-containar").removeClass("center-block");
					var offset = $(window).height() - $('footer').outerHeight() - $('.logo-containar').outerHeight() - $('.desktop-menu').outerHeight()-76;
					$('.home-video-div').height(offset); 

					// Position the logo so that it is always visible when the video moves up
					// Store original position if not already stored
					if (!$('.logo-containar').data('original-position')) {
						$('.logo-containar').data('original-position', $('.logo-containar').offset().top);
					}

					// Get original position (before any transforms)
					var originalLogoPosition = $('.logo-containar').data('original-position');
					var homeVideoDivPosition = $('.home-video-div .carousel-inner').offset().top;
					var logoHeight = $('.logo-containar').outerHeight();
					var overlap = (originalLogoPosition + logoHeight) - homeVideoDivPosition;

					if (overlap > 0) {
						// Check if logo top position is below 80px
						var currentLogoTop = $('.logo-containar').offset().top;

						if (currentLogoTop < 80) {
							// Shrink the logo instead of translating
							var shrinkAmount = overlap + 10;
							var newHeight = Math.max(logoHeight - shrinkAmount, logoHeight * 0.3); // Minimum 30% of original size
							var scale = newHeight / logoHeight;
							$('.logo-containar').css('transform', 'scale(' + scale + ')');
						} else {
							// Apply transform based on original position (translate up)
							var translateY = -(overlap + 10);
							$('.logo-containar').css('transform', 'translateY(' + translateY + 'px)');
						}
					} else {
						// Only reset if there's no overlap in original position
						$('.logo-containar').css('transform', '');
					}
				}
			}
			
			$(document).ready(function () {
				$('.home-video-div').css("visibility", "visible");
				// Run after a 0.5s delay to ensure all elements are properly rendered
				setTimeout(function() {
					mobileViewFunction();
				}, 100);
			});

			$(window).resize(function () {
				mobileViewFunction();
			});
		</script>
	</body>
</html>
