package com.danesemilano.persistence.repository;

import java.util.Locale;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.danesemilano.persistence.entity.EventNews;

import net.yadaframework.components.YadaUtil;
import net.yadaframework.persistence.YadaSql;

/**
 * Locale-related operations on entities 
 */
@Repository
@Transactional(readOnly = true) 
public class EventNewsDao {
	
    //@Autowired private YadaConfiguration config;
    @Autowired private EventNewsRepoDao eventNewsRepository;
    @PersistenceContext
	private EntityManager em;
    
    
    /**
     * Cancella un EventNews
     * @param EventNews
     */
    @Transactional(readOnly = false) 
    public String delete(Long id, Locale locale) {
    	String name = (String) YadaSql.instance().selectFrom("select title from EventNews_title")
        		.where("where EventNews_id=:eventNewsId").and()
        		.where("where locale=:locale")
    	    	.setParameter("eventNewsId", id)
    	    	.setParameter("locale", locale.getLanguage())
    	    	.nativeQuery(em)
    	    	.getSingleResult();
    	
        	performQuery(em.createNativeQuery("delete from Tag_EventNews where EventNews_id = :eventNewsId"), id);
        	performQuery(em.createNativeQuery("delete from EventNews_content where EventNews_id = :eventNewsId"), id);
        	performQuery(em.createNativeQuery("delete from EventNews_title where EventNews_id = :eventNewsId"), id);
        	performQuery(em.createNativeQuery("delete from EventNews_subtitle where EventNews_id = :eventNewsId"), id);
        	performQuery(em.createNativeQuery("delete from EventNews where id = :eventNewsId"), id);
        	//performQuery(em.createNativeQuery("delete from YadaAttachedFile where attachedToId = :eventNewsId and relativeFolderPath='/images/news'"), id);
        	return name;
    }
    
    private void performQuery(Query query, Long id) {
    	query.setParameter("eventNewsId", id);
    	query.executeUpdate();
    }
    
    /**
     * Duplicate a EventNews
     * @param EventNews
     * @return
     */
    @Transactional(readOnly = false) 
    public Object duplicate(EventNews eventNews) {
    	return YadaUtil.copyEntity(eventNews);
    }

}
