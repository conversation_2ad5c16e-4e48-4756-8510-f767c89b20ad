package com.danesemilano.persistence.repository;

import java.util.List;

import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.NonUniqueResultException;
import jakarta.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.danesemilano.persistence.entity.Dealer;

@Repository
@Transactional(readOnly = true) 
public class DealerRepoDao {
	
	@PersistenceContext
	EntityManager em;
	
	// Kept for compatibility with Spring Data Repository
	public Dealer findOne(Long entityId) {
		return em.find(Dealer.class, entityId);
	}
	
	public Dealer findByName(String name) {
		String sql = "from Dealer where name = :name";
		try {
			return em.createQuery(sql, Dealer.class)
				.setMaxResults(1)
				.setParameter("name", name)
				.getSingleResult();
		} catch (NonUniqueResultException | NoResultException e) {
			return null; // Nothing found
		}
	}

	@Transactional(readOnly = false)
	public Dealer save(Dealer entity) {
		if (entity==null) {
			return null;
		}
		if (entity.getId()==null) {
			em.persist(entity);
			return entity;
		}
		return em.merge(entity);
	}
	
	public List<Dealer> findAll() {
		return em.createQuery("from Dealer", Dealer.class)
			.getResultList();
	}


}
