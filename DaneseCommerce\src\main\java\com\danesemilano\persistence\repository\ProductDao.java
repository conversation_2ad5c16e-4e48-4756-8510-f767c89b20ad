package com.danesemilano.persistence.repository;

import java.util.List;
import java.util.SortedSet;
import java.util.TreeSet;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.danesemilano.persistence.entity.Designer;
import com.danesemilano.persistence.entity.Product;
import com.danesemilano.persistence.entity.Tag;

import net.yadaframework.components.YadaUtil;
import net.yadaframework.persistence.YadaSql;
import net.yadaframework.persistence.entity.YadaAttachedFile;
import net.yadaframework.persistence.repository.YadaLocaleDao;
import net.yadaframework.web.YadaPageRequest;
import net.yadaframework.web.YadaPageRows;

/**
 * Locale-related operations on entities 
 */
@Repository
@Transactional(readOnly = true) 
public class ProductDao {
	
    //@Autowired private YadaConfiguration config;
    @Autowired private YadaLocaleDao yadaLocaleDao;
    @PersistenceContext
	private EntityManager em;
    
    /**
     * Ritorna tutti i file del tipo richiesto per il prodotto indicato
     * @param productId id del prodotto, oppure -1 per tutti
     * @param type "3d", "2d", "pdf"
     * @return
     */
    public List<YadaAttachedFile> getFiles(Long productId, String type) {
    	 YadaSql yadaSql = YadaSql.instance().selectFrom("select f from Product p")
    		.join("3d".equals(type), "join p.files3d f")
    		.join("2d".equals(type), "join p.files2d f")
    		.join("pdf".equals(type), "join p.files f")
    		.where("where p.published = true").and()
    		.where(productId!=-1, "p.id=:productId")
    		.setParameter("productId", productId);
    	 return yadaSql.query(em, YadaAttachedFile.class).getResultList();
    }
    
    /**
     * cancella la relazione tra Tag e NewsId senza cancellare il vero Tag
     * @param newsId
     */
    @Transactional(readOnly = false)
    public void deleteTagByProductId(Long productId) {
    	performQuery(em.createNativeQuery("delete from Tag_YadaProduct where products_id = :productId"), productId);
    }
    
    /**
     * Delete a Product
     * @param id, locale
     */
    @Transactional(readOnly = false) 
    public String delete(Long id) {
//    	String name = (String) YadaSql.instance().selectFrom("select productName from YadaProduct ")
//    		.where("where id=:productId").and()
//	    	.setParameter("productId", id)
//	    	.nativeQuery(em)
//	    	.getSingleResult();
    	//
    	// NOTA: invece di fare tutte queste delete a mano, si poteva prelevare l'oggeto Product e fare
    	// em.remove(product); 
    	// che cancella tutte le associazioni (se non lo fa bisogna aggiungere (cascade=CascadeType.REMOVE, orphanRemoval=true) su Product
    	//
    	Product n = em.find(Product.class, id);
    	deleteTagByProductId(id);
    	
    	for (Designer d : n.getDesigners()) {
    		d.getProducts().remove(n);
    	}
    	
    	String name = n.getLocalName();
    	em.remove(n);
    	
//    	performQuery(em.createNativeQuery("delete from Product_notes where Product_id = :productId"), id);
//    	performQuery(em.createNativeQuery("delete from Product_dimension where Product_id = :productId"), id);
//    	performQuery(em.createNativeQuery("delete from Product_color where Product_id = :productId"), id);
//    	performQuery(em.createNativeQuery("delete from Product_edition where Product_id = :productId"), id);
//    	performQuery(em.createNativeQuery("delete from Product_files where Product_id = :productId"), id);
//    	performQuery(em.createNativeQuery("delete from Product_files2d where Product_id = :productId"), id);
//    	performQuery(em.createNativeQuery("delete from Product_files3d where Product_id = :productId"), id);
//    	performQuery(em.createNativeQuery("delete from Product_finish where Product_id = :productId"), id);
//    	performQuery(em.createNativeQuery("delete from Product_specs where Product_id = :productId"), id);
//    	performQuery(em.createNativeQuery("delete from Product_technicalData where Product_id = :productId"), id);
//    	performQuery(em.createNativeQuery("delete from Product_tipology where Product_id = :productId"), id);
//    	performQuery(em.createNativeQuery("delete from Product_contract where Product_id = :productId"), id);
//    	performQuery(em.createNativeQuery("delete from YadaProduct_accessories where accessoryOf_id = :productId"), id);
//    	performQuery(em.createNativeQuery("delete from YadaProduct_attachments where YadaProduct_id = :productId"), id);
//    	performQuery(em.createNativeQuery("delete from YadaProduct_categories where YadaProduct_id = :productId"), id);
//    	performQuery(em.createNativeQuery("delete from YadaProduct_description where YadaProduct_id = :productId"), id);
//    	performQuery(em.createNativeQuery("delete from YadaProduct_galleryImages where YadaProduct_id = :productId"), id);
//    	performQuery(em.createNativeQuery("delete from YadaProduct_materials where YadaProduct_id = :productId"), id);
//    	performQuery(em.createNativeQuery("delete from YadaProduct_name where YadaProduct_id = :productId"), id);
//    	performQuery(em.createNativeQuery("delete from YadaProduct_subcategories where YadaProduct_id = :productId"), id);
//    	performQuery(em.createNativeQuery("delete from YadaProduct_subtitle where YadaProduct_id = :productId"), id);
//    	performQuery(em.createNativeQuery("delete from Designer_YadaProduct where products_id = :productId"), id);
//    	performQuery(em.createNativeQuery("delete from Tag_YadaProduct where products_id = :productId"), id);
//    	performQuery(em.createNativeQuery("delete from YadaProduct where id = :productId"), id);
    	//performQuery(em.createNativeQuery("delete from YadaAttachedFile where attachedToId = :productId and relativeFolderPath='/images/products'"), id);
    	return name;
    }
    
    private void performQuery(Query query, Long id) {
    	query.setParameter("productId", id);
    	query.executeUpdate();
    }
    
    /**
     * Duplicate a product
     * @param product
     * @return
     */
    @Transactional(readOnly = false) 
    public Object duplicate(Product product) {
    	product = em.merge(product); // serve?
    	Product copy = (Product) YadaUtil.copyEntity(product);
    	// La copia della lista dei tag non funziona bene, quindi la rifacciamo a mano
    	SortedSet<Tag> tagsCopy = new TreeSet<>();
    	for (Tag tag : product.getTags()) {
    		//tagsCopy.add(tag);
    		tag.getProducts().add(copy);
		}
    	// copy.setTags(tagsCopy);
    	//
//    	for (YadaAttachedFile yadaAttachedFile : copy.getGalleryImages()) {
//    		em.persist(yadaAttachedFile);
//    	}
//    	for (YadaAttachedFile yadaAttachedFile : copy.getAttachments()) {
//    		em.persist(yadaAttachedFile);
//    	}
//    	for (YadaAttachedFile yadaAttachedFile : copy.getFiles()) {
//    		em.persist(yadaAttachedFile);
//    	}
//    	for (YadaAttachedFile yadaAttachedFile : copy.getFiles2d()) {
//    		em.persist(yadaAttachedFile);
//    	}
//    	for (YadaAttachedFile yadaAttachedFile : copy.getFiles3d()) {
//    		em.persist(yadaAttachedFile);
//    	}
//    	if ( copy.getSilhouette()!=null) {
//    		em.persist(copy.getSilhouette());
//    	}
//    	if ( copy.getWireframe()!=null) {
//    		em.persist(copy.getWireframe());
//    	}
//    	if ( copy.getImage()!=null) {
//    		em.persist(copy.getImage());
//    	}
    	em.persist(copy);
    	return copy;
    }

    /**
     * Finds a Product and loads most of the lazy relationships
     * @param id
     * @return
     */
	public Product findAndLoadLazyAttributes(Long id) {
		Product found = yadaLocaleDao.findOneWithLocalValues(id, Product.class);
		if (found!=null) {
			// Initialize collections that are going to be used in the html
			found.getArticles().size();
			found.getGalleryImages().size();
			found.getCategories().size();
			found.getSubcategories().size();
			found.getContract().size();
			found.getTipology().size();
			found.getFiles2d().size();
			found.getFiles3d().size();
			found.getFiles().size();
		}
		return found;
	}
	
	 /**
     * Finds a List<Product> and loads most of the lazy relationships
     * @return
     */
	public List<Product> findAllLoadLazyAttributes() {
		List<Product> found = yadaLocaleDao.findAllWithLocalValues(Product.class);
		return found;
	}

	/**
     * Ritorna una pagina di Product per la categoria indicata
     * @param categoria la categoria da cercare
     * @param pageable
     * @return
     */
	// This is never used because pagination is not implemented (has been removed)
    public YadaPageRows<Product> findProductsByCategory(Long categoria, YadaPageRequest pageable) {
		YadaSql yadaSql = YadaSql.instance().selectFrom("from YadaProduct p")
			.join("join fetch p.categories c")
			.where("where p.published = true").and()
	    	.where(categoria!=null && categoria!=-1, "c.id = 8").and();
	    yadaSql.orderBy("p.modified desc")
	    	.setParameter("categoria", categoria);
		@SuppressWarnings("unchecked")
    	List<Product> found = yadaSql.query(em)
			.setFirstResult(pageable.getFirstResult())
			.setMaxResults(pageable.getMaxResults())
			.getResultList();
		return new YadaPageRows<Product>(found, pageable);
    }
}
