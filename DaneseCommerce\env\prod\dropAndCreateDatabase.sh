#!/bin/bash
# Usare il parametro "force" per bypassare il prompt
# NOTA: questo script VIENE COPIATO ogni volta dal build.xml del workspace, quindi non fare modifiche su ubuntu
# Note for yada developers: the $ in the yada template is the escaped form of the dollar sign

hostname=localhost
force=

if [ "$1" == "force" ]
then
	force=-f
fi

echo 
echo Rigenerazione del DB dncdbprod

mysqladmin $force --user=root --password=ndlib4ghg35h drop dncdbprod

# devo usare mysql e non mysqladmin perche' il secondo non mi setta il charset

mysql --verbose -u root --password=ndlib4ghg35h --host=$hostname <<SQLCOMMAND 
create database dncdbprod character set utf8mb4;
CREATE USER 'dncuserprod'@'localhost' IDENTIFIED BY 'ndlib4ghg35h';
GRANT ALL ON dncdbprod.* TO 'dncuserprod'@'localhost';
FLUSH PRIVILEGES;
SQLCOMMAND

mysql --verbose -u root --password=ndlib4ghg35h --host=$hostname dncdbprod < dnc.sql
mysql --verbose -u root --password=ndlib4ghg35h --host=$hostname dncdbprod < dncextra.sql

echo Done.
