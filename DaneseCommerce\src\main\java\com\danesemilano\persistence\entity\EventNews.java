package com.danesemilano.persistence.entity;

import java.lang.reflect.Field;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.SortedSet;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.ElementCollection;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.MapKeyColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.persistence.Transient;
import jakarta.persistence.Version;

import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.SortNatural;
import org.springframework.context.i18n.LocaleContextHolder;

import com.fasterxml.jackson.annotation.JsonProperty;

import net.yadaframework.core.CloneableDeep;
import net.yadaframework.persistence.entity.YadaAttachedFile;
import org.springframework.format.annotation.DateTimeFormat;

@Entity
public class EventNews implements CloneableDeep {
	private static final long serialVersionUID = 1L;
	// For synchronization with external databases
	@Column(columnDefinition="DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")
	@Temporal(TemporalType.TIMESTAMP)
	protected Date modified;
	// For optimistic locking
	@Version
	protected long version;
	
	@Id
	@GeneratedValue(strategy= GenerationType.IDENTITY)
	private Long id;
	
	private boolean evento = false;
	
	@ManyToMany(mappedBy="eventNews", fetch=FetchType.EAGER)
	@SortNatural // Ordino i tag in base al nome nel locale corrente
	private SortedSet<Tag> tags;

	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date publishDate;
	
	@Column(length=1024)
	private String video;
	
	@ElementCollection(fetch = FetchType.EAGER)
	@Fetch(FetchMode.SELECT) // Necessario per prevenire i duplicati
	@Column(length=255)
	@MapKeyColumn(name="locale", length=32)
	private Map<Locale, String> title = new HashMap<>();
	
	@ElementCollection(fetch = FetchType.EAGER)
	@Fetch(FetchMode.SELECT) // Necessario per prevenire i duplicati
	@Column(length=255)
	@MapKeyColumn(name="locale", length=32) 
	private Map<Locale, String> subtitle = new HashMap<>();

	@ElementCollection
	@Column(length=8192)
	@MapKeyColumn(name="locale", length=32) // th_TH_TH_#u-nu-thai
	private Map<Locale, String> content = new HashMap<>(); // a kind of small description
	
	/**
	 * The main image to show in lists etc.
	 */
	@OneToOne(cascade = CascadeType.REMOVE, orphanRemoval=true)
	protected YadaAttachedFile image;
	
	@OneToOne(cascade = CascadeType.REMOVE, orphanRemoval=true)
	protected YadaAttachedFile thumbnail;
	
	
	/////////////////////////////////////////////////////////////////////////////////////////////
	
	@Transient
	@JsonProperty("DT_RowId")
	public String getDT_RowId() {
		return this.getClass().getSimpleName()+"#"+this.id;
	}
	
	/**
	 * Returns the localized title in the current request locale
	 * @return
	 */
	public String getLocalTitle() {
		return title.get(LocaleContextHolder.getLocale());
	}
	
	/**
	 * Returns the localized subtitle in the current request locale
	 * @return
	 */
	public String getLocalSubtitle() {
		return subtitle.get(LocaleContextHolder.getLocale());
	}
	
	/**
	 * Returns the localized description in the current request locale
	 * @return
	 */
	public String getLocalDescription() {
		return content.get(LocaleContextHolder.getLocale());
	}

	/////////////////////////////////////////////////////////////////////////////////////////////

	public Long getId() {
		return id;
	}
	
	public void setId(Long id) {
		this.id = id;
	}
	
	public String getVideo() {
		return video;
	}
	public void setVideo(String video) {
		this.video = video;
	}
	public Map<Locale, String> getTitle() {
		return title;
	}
	public void setTitle(Map<Locale, String> title) {
		this.title = title;
	}
	
	public Map<Locale, String> getSubtitle() {
		return subtitle;
	}
	public void setSubtitle(Map<Locale, String> subtitle) {
		this.subtitle = subtitle;
	}
	
	public Map<Locale, String> getContent() {
		return content;
	}
	public void setContent(Map<Locale, String> description) {
		this.content = description;
	}
	
	@Override
	public Field[] getExcludedFields() {
		// s
		return null;
	}
	
//	@OneToMany(cascade=CascadeType.REMOVE, orphanRemoval=true)
//	@JoinTable(name="EventNews_images")
//	@OrderBy("sortOrder")
//	protected List<YadaAttachedFile> galleryImages;

	public Date getModified() {
		return modified;
	}

	public void setModified(Date modified) {
		this.modified = modified;
	}

	public long getVersion() {
		return version;
	}

	public void setVersion(long version) {
		this.version = version;
	}

	public YadaAttachedFile getImage() {
		return image;
	}

	public void setImage(YadaAttachedFile image) {
		this.image = image;
	}

	public YadaAttachedFile getThumbnail() {
		return thumbnail;
	}

	public void setThumbnail(YadaAttachedFile thumbnail) {
		this.thumbnail = thumbnail;
	}

	public boolean isEvento() {
		return evento;
	}

	public void setEvento(boolean evento) {
		this.evento = evento;
	}

	public Date getPublishDate() {
		return publishDate;
	}

	public void setPublishDate(Date publishDate) {
		this.publishDate = publishDate;
	}

	public SortedSet<Tag> getTags() {
		return tags;
	}

	public void setTags(SortedSet<Tag> tags) {
		this.tags = tags;
	}
}
