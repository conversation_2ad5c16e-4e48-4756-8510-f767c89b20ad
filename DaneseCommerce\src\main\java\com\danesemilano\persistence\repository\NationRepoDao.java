package com.danesemilano.persistence.repository;

import java.util.List;
import java.util.Locale;

import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.NonUniqueResultException;
import jakarta.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.danesemilano.persistence.entity.Nation;

@Repository
@Transactional(readOnly = true) 
public class NationRepoDao {
	
	@PersistenceContext
	EntityManager em;
	
	/**
	 * Find a Nation by name
	 * @param name
	 */
	public Nation findByNationName(String name) {
		String sql = "select * from Nation n join Nation_name nn ON n.id=nn.nation_id where nn.name = :name";
		try {
			return (Nation) em.createNativeQuery(sql, Nation.class)
				.setMaxResults(1)
				.setParameter("name", name)
				.getSingleResult();
		} catch (NonUniqueResultException | NoResultException e) {
			return null; // Nothing found
		}
	}
	
	/**
	 * Find all nation order by name
	 */
	public List<Nation> findAllOrderByNameAsc(Locale locale) {
		String sql = "select n from Nation n join n.name name where KEY(name)=:locale order by name asc";
		return em.createQuery(sql, Nation.class)
			.setParameter("locale", locale)
			.getResultList();
	}

	public List<Nation> findAll() {
		return em.createQuery("from Nation", Nation.class)
			.getResultList();
	}

	@Transactional(readOnly = false)
	public void save(List<Nation> batchToSave) {
		for (Nation entity : batchToSave) {
			save(entity);
		}
	}
	
	@Transactional(readOnly = false)
	public Nation save(Nation entity) {
		if (entity==null) {
			return null;
		}
		if (entity.getId()==null) {
			em.persist(entity);
			return entity;
		}
		return em.merge(entity);
	}

	// Kept for compatibility with Spring Data Repository
	public Nation findOne(Long entityId) {
		return em.find(Nation.class, entityId);
	}
	
}