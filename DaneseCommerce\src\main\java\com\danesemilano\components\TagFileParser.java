package com.danesemilano.components;

import java.io.File;
import java.io.IOException;
import java.util.Iterator;
import java.util.Locale;
import java.util.Map;

import org.apache.commons.lang3.LocaleUtils;
import org.apache.poi.EncryptedDocumentException;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.ui.Model;
import org.springframework.web.multipart.MultipartFile;

import com.danesemilano.core.DncConfiguration;
import com.danesemilano.persistence.entity.Tag;
import com.danesemilano.persistence.repository.TagRepoDao;

import net.yadaframework.components.YadaNotify;
import net.yadaframework.components.YadaWebUtil;

/**
 * Metodi per il caricamento dei file xlsx e csv
 * <AUTHOR>
 *
 */
@Component
public class TagFileParser {
	private final Logger log = LoggerFactory.getLogger(getClass());
	
	@Autowired private TagRepoDao tagRepository;
	@Autowired private DncConfiguration config;
	@Autowired YadaWebUtil yadaWebUtil;
	@Autowired YadaNotify yadaNotify;
	
	public class Result {
		public int added;
		public int updated;
		public int removed;
		public int errors;
	}

	/**
	 * Carica i tags prendendoli da un file xlsx
	 * @param attachedFile
	 * @param model
	 * @throws IOException 
	 * @throws InvalidFormatException 
	 * @throws EncryptedDocumentException 
	 */
	public Result loadFileXLS(MultipartFile  attachedFile, Model model) throws IOException, EncryptedDocumentException, InvalidFormatException {
		
		
		Result result = new Result();
		File tempFile = File.createTempFile("tag", ".xls");
		yadaWebUtil.saveAttachment(attachedFile, tempFile);
		// carichi.....try (Workbook workbook 
		
	   // 1. You can obtain a sheetIterator and iterate over it
       // Iterator<Sheet> sheetIterator = workbook.sheetIterator();
		
		// Memorizzo quello nuovo facendo EmotionLift Impact (P) - EmotionLift Impact (N)
		try (Workbook workbook = WorkbookFactory.create(tempFile)) {
			
			Sheet sheet = workbook.getSheetAt(0);
			Iterator<Row> rows = sheet.rowIterator();
			Row row;
			Cell cell;
			
			while (rows.hasNext()) {
				row = rows.next();
				Iterator<Cell> cells = row.cellIterator();
				while (cells.hasNext()) {
					cell = cells.next();
					
					if (cell.getCellType() != CellType.STRING) {
						break;
					}
					
					if (cell.getStringCellValue().equals("ITA")) { //Leggo l'header del file
						//Una volta che so che sono sulla riga del tag in italiano, allora sotto dovrei avere il valore
						//Quindi vado a prendere la riga successiva
						Row rowValueITA = rows.next();
						Iterator<Cell> celleDellaRigaSottostante = rowValueITA.cellIterator();
						while (celleDellaRigaSottostante.hasNext()) {
							
							Cell cellaITA = celleDellaRigaSottostante.next();
							Cell cellaEN = celleDellaRigaSottostante.next();
							
							String tagITA = cellaITA.getStringCellValue();
							String tagEN = cellaEN.getStringCellValue();
							
							result = salvaTag(tagITA, tagEN, result, model);
						}
					} if (!cell.getStringCellValue().equals("ITA")) {
						Cell cellaEN = cells.next();
						String tagITA = cell.getStringCellValue();
						String tagEN = cellaEN.getStringCellValue();
						result = salvaTag(tagITA, tagEN, result, model);
					} else {
						break;
					}

				}
			} 
		} catch (IOException e) {
			log.error("Impossibile caricare il file XLS dei Tags: ", e);
			result.errors++;
		} finally {
			tempFile.delete();
		}
		return result;
	}

	public Result salvaTag(String tagITA, String tagEN, Result result, Model model) {
		Locale localeIT = LocaleUtils.toLocale("it");
		Locale localeEN = LocaleUtils.toLocale("en");
		try {
			Tag tag = new Tag();
			Map<Locale,String> nameMap = tag.getName(); //è vuota
			nameMap.put(localeIT, tagITA);
			nameMap.put(localeEN, tagEN);
			
									
			//salva
			tagRepository.save(tag);
			result.added++;
		} catch (Exception e) {
			yadaNotify.title("XLS  File Parsing", model).error().message("Tag {}-{} not added - similar or equal tag already exists", tagITA, tagEN).add();
			result.errors++;
		}
		return result;
	}
	
}
