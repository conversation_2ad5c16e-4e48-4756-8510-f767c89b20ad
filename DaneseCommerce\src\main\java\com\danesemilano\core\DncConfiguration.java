package com.danesemilano.core;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import net.yadaframework.core.YadaConfiguration;

// Usare XPath
public class DncConfiguration extends YadaConfiguration {
	private static final long serialVersionUID = 1L;
	private final transient Logger log = LoggerFactory.getLogger(getClass());
	
	// Cached values
	// Questi valori li memorizzo perchè non mi aspetto che cambino a runtime
	private Integer sessionTimeoutMinutes=null;
	
	/**
	 * Returns the widths of the product gallery images
	 * @return [desktopWidth, mobileWidth]
	 */
	public Integer[] getImageWidthProductGallery() {
		return getImageWidth("/product/gallery");
	}
	
	public Integer[] getImageWidthProductThumbnail() {
		return getImageWidth("/product/thumbnail");
	}
	
	public Integer[] getImageWidthProductWireframe() {
		return getImageWidth("/product/wireframe");
	}
	
	public Integer[] getImageWidthProductSilhouette() {
		return getImageWidth("/product/silhouette");
	}

	public Integer[] getImageWidthDesignerThumbnail() {
		return getImageWidth("/designer/thumbnail");
	}
	
	public Integer[] getImageWidthNewsGallery() {
		return getImageWidth("/news/gallery");
	}
	
	public Integer[] getImageWidthNewsThumbnail() {
		return getImageWidth("/news/thumbnail");
	}

	/**
	 * Get the width of an image, for desktop and mobile
	 * @param relativeKey the key like "/product/gallery", relative to "config/size"
	 * @return { desktopWidth, mobileWidth }, null when not configured
	 */
	private Integer[] getImageWidth(String relativeKey) {
		Integer desktop = configuration.getInteger("config/size" + relativeKey + "/desktop", null);
		Integer mobile = configuration.getInteger("config/size" + relativeKey + "/mobile", null);
		return new Integer[] {desktop, mobile};
	}
	
	public Integer getSessionTimeoutMinutes() {
		if (sessionTimeoutMinutes==null) {
			sessionTimeoutMinutes = configuration.getInt("config/security/sessionTimeoutMinutes", -1);
			if (sessionTimeoutMinutes==-1) {
				// Se non si configura uguale a web.xml, il js di controllo entra ogni 20 minuti e se è inferiore al timeout vero, la sessione non scade mai
				log.error("Session timeout not configured correctly (defaulting to 20). Please configure session timeout in conf.webapp.xml equal to web.xml");
				sessionTimeoutMinutes = 20;
			}
		}
		return sessionTimeoutMinutes;
	}
	
	/**
	 * Ritorna le sorgenti a cui inviare le richieste "contact us"
	 * @param locale
	 * @return
	 */
	public List<String> getAllSorgentiLuminose() {
		List<String> sorgenti = Arrays.asList(configuration.getStringArray("config/sources/source"));
		Collections.sort(sorgenti);
		
		return sorgenti;
	}

}
