package com.danesemilano.persistence.entity;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.persistence.Transient;
import jakarta.persistence.Version;

import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.annotation.JsonProperty;

import net.yadaframework.core.CloneableFiltered;
import net.yadaframework.persistence.entity.YadaPersistentEnum;

@Entity
public class Dealer implements CloneableFiltered, Serializable  {
	
	private static final long serialVersionUID = 1L;
	// For synchronization with external databases
	@Column(columnDefinition="DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")
	@Temporal(TemporalType.TIMESTAMP)
	protected Date modified;
	// For optimistic locking
	@Version
	protected long version;
	
	@Id
	@GeneratedValue(strategy= GenerationType.IDENTITY)
	private Long id;
	
	@Column(length=255)
	protected String name;
	
	@OneToOne(fetch = FetchType.EAGER)
	private YadaPersistentEnum<EnumRegion> region;
	
	@OneToOne(fetch = FetchType.EAGER)
	private YadaPersistentEnum<EnumProvince> province;
	
	@ManyToOne
	private Nation nation;
	
	@Column(length=8192)
	private String address;
	
	private boolean agente = false;
	
	//@Column(nullable=false, unique=true, length=128)
	@Column(length=255)
	private String email;
	
	@Column(length=255)
	private String phone;
	
	@Column(length=255)
	private String fax;
	
	////////////////////////////////////////////////////////////////////////////////////////
	
	@Transient
	@JsonProperty("DT_RowId")
	public String getDT_RowId() {
		return this.getClass().getSimpleName()+"#"+this.id;
	}
	
	/**
	 * Ritorna il numero di telefono senza caratteri non-numerici come spazi o trattini
	 * @return
	 */
	@Transient
	public String getPhoneNumeric() {
		return phone.replaceAll("\\D", "");
	}
	
	@Transient
	public boolean hasAddress() {
		return StringUtils.isNotBlank(address);
	}
	
	@Transient
	public boolean hasPhone() {
		return StringUtils.isNotBlank(phone);
	}
	
	@Transient
	public boolean hasFax() {
		return StringUtils.isNotBlank(fax);
	}
	
	@Transient
	public boolean hasMail() {
		return StringUtils.isNotBlank(email);
	}
	
	///////////////////////////////////////////////////////////////////////////////////////////
	
	public Long getId() {
		return id;
	}
	
	public void setId(Long id) {
		this.id = id;
	}
	
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	public Nation getNation() {
		return nation;
	}

	public void setNation(Nation nation) {
		this.nation = nation;
	}
	
	public YadaPersistentEnum<EnumRegion> getRegion() {
		return region;
	}

	public void setRegion(YadaPersistentEnum<EnumRegion> region) {
		this.region = region;
	}

	public YadaPersistentEnum<EnumProvince> getProvince() {
		return province;
	}

	public void setProvince(YadaPersistentEnum<EnumProvince> province) {
		this.province = province;
	}

	public boolean isAgente() {
		return agente;
	}

	public void setAgente(boolean agente) {
		this.agente = agente;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}
	
	public String getAddress() {
		return address;
	}


	public void setAddress(String address) {
		this.address = address;
	}
	
	@Override
	public Field[] getExcludedFields() {
		// TODO Auto-generated method stub
		return null;
	}


	public String getFax() {
		return fax;
	}


	public void setFax(String fax) {
		this.fax = fax;
	}
}
