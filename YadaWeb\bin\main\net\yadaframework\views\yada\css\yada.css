.yadaInvisible {
	visibility: hidden !important;
}

.yadaHidden {
	display: none !important;
}

.yadaCover {
	background-color: white;
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 100;
    opacity: 0.6;
}

.yadaRefresh {
	cursor: pointer;	
}

/* Checkmark for data-yadaShowAjaxFeedback */
.yadaAjaxFeedbackOk {
    top: 40%;
    width: 100%;
    text-align: center;
    position: fixed;
    color: #00d900;
    display: none;
    z-index: 1000000;
    pointer-events: none;
}

.yadaAjaxDisabled {
	cursor: no-drop;
}

/*
 * To be used in a div that has to be "disabled"
 */
.yadaDisabled {
	position: relative;
}
.yadaDisabled:before {
	content: " ";
	display: block;
	position: absolute;
	width: 100%;
	height: 100%;
	background-color: black;
	opacity: 0.2;	
	z-index: 999999;    
}

.modal .btn-close, .popover .btn-close {
	/* float: right; */
    border: none;
    margin: 0;
    /* padding: 0; */
    /*opacity: .2;*/
    position: relative;
    bottom: 18px;
}

.yadaResponseData {
	display: none;
}

.yadaImpersonification {
	position: fixed;
	top: 0px;
	width: 100%;
	z-index: 50;
}


.yadaScrollTop {
	position: fixed;
	bottom: 0px;
	right: 0px;
	background-color: #1baed9;
	padding: 5px 8px;
	margin: 0px;
	font-size: 12px;
	color: #fff;
	font-weight: bold;
	cursor: pointer;
	display: none;
	z-index: 100;
}


.yadaScrollTop:hover {
	background-color: #1794b7;
	color: #fff;
}

/*********/
/* Forms */
/*********/

.yadaCheckbox label, label.yadaCheckbox {
	font-weight: 400;
    cursor: pointer;
}

.yadaHelpButton {
	margin-left: 10px;
	margin-right: 10px;
	color: #1baed9;
	font-size: 18px;
	z-index: 3;
    position: relative;
    cursor: pointer;
}

.yadaHelpButton.input-group-text {
	margin-right: 0;
}

a.yadaHelpButton:focus {
	outline: none;
}

.yadaHelpButton:hover {
	color: #1794b7;
}

.yadaInputError {
	margin-top: 10px;
}

input.yadaReadonly, input.yadaReadonly:focus, input.yadaReadonly:hover {
	border: none;
	box-shadow: none;
	cursor: default;
}

.yadaWidth80 input {
	width: 80%;
}

.yadaHideEdit .yadaEditButton {
	display: none;
}

.yadaHideClone .yadaClonetButton {
	display: none;
}

.yadaHideDelete .yadaDeletetButton {
	display: none;
}

/*************/
/* DataTable */
/*************/

.dataTables_wrapper {
	position: relative;
}

.yadaTableBlock {
	margin-top: 30px;
	margin-bottom: 60px;
}

table.dataTable .yadaCheckInCell {
	text-align: center;
	width: 50px;
	padding-left: 0;
	padding-right: 0;
}

.yadaTableToolbar {
	margin-top: 15px;
}

table.dataTable .yadaRowCommandButton {
	margin-right: 10px;
	font-size: 20px;
  	vertical-align: top;
}

table.dataTable .yadaRowCommandButton.disabled {
	cursor: no-drop;
}

table.dataTable .yadaCommandButtonCell {
	white-space: nowrap;
}

table.dataTable.compact .yadaCheckInCell {
	padding-right: 4px;
}

table.dataTable th.sorting_asc, table.dataTable th.sorting_desc {
	/* background-color: #fafafa; */
	border-style: solid !important;
	border-width: 2px !important;
	border-color: #e5e5e5 !important;
	border-bottom-style: none !important;
}

/****************/
/* Notification */
/****************/
.yadaNotify .modal-header .yadaIcon:not(.yadaIcon-close) {
	font-size: 40px;
	vertical-align: middle;
	padding-right: 10px;
}
.yadaNotify .modal-body {
	max-height: 400px;
	overflow: auto;
} 
.yadaNotify .yadaIcon.ok {
	color: #25B72E;
}
.yadaNotify .yadaIcon.error {
	color: #E81111;
}
.yadaNotify .yadaIcon.info {
	color: #209EF9;
}
.yadaNotify .notifyMessage {
	margin-bottom: 10px;
}

.yadaNotify [role=tab] a {
	color: black;
	vertical-align: top;
}

.yadaNotify [role=tab] a span {
    margin-left: 0.4rem;
}

.yadaNotify [role=tab] a:not(.collapsed) .bi-caret-down {
	display: inline-block;
	transform: rotate(180deg);
	transition: transform 0.5s ease;
} 
.yadaNotify [role=tab] a.collapsed .bi-caret-down {
	display: inline-block;
	transform: rotate(0deg);
	transition: transform 0.5s ease;
}

/***********************/
/* BOOTSTRAP OVERRIDE  */
/***********************/

/* non va
.yada .disabled {
	cursor: not-allowed;
}
*/

/* Senza questo la X delle validazioni viene troppo alta in chrome (e negli altri?) */
.yada .has-feedback label~.form-control-feedback {
  top: 28px;
}

/***************/
/* CSS SWITCH  */
/***************/
/* http://callmenick.com/post/css-toggle-switch-examples */

label.yadaSwitchLabel {
	padding-left: 10px;	
}
.yadaSwitch {
  position: absolute;
  margin-left: -9999px;
  visibility: hidden;
}
.yadaSwitch ~ label {
  /*display: block;*/
  position: relative;
  cursor: pointer;
  outline: none;
  vertical-align: middle;
}
input.yadaSwitch-round ~ label {
  padding: 2px;
  width: 40px;
  height: 20px;
  background-color: #dddddd;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  border-radius: 20px;
}
input.yadaSwitch-round ~ label:before, input.yadaSwitch-round ~ label:after {
  display: block;
  position: absolute;
  top: 1px;
  left: 1px;
  bottom: 1px;
  content: "";
}
input.yadaSwitch-round ~ label:before {
  right: 1px;
  background-color: #f1f1f1;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  border-radius: 20px;
  -webkit-transition: background 0.4s;
  -moz-transition: background 0.4s;
  -o-transition: background 0.4s;
  transition: background 0.4s;
}
input.yadaSwitch-round ~ label:after {
  width: 18px;
  background-color: #fff;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  border-radius: 100%;
  -webkit-box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  -moz-box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  -webkit-transition: margin 0.4s;
  -moz-transition: margin 0.4s;
  -o-transition: margin 0.4s;
  transition: margin 0.4s;
}
input.yadaSwitch-round:disabled ~ label:after {
	display: none; /* This may not be correct */
	cursor: not-allowed;
}
input.yadaSwitch-round:disabled ~ label:before {
  cursor: not-allowed;
}
input.yadaSwitch-round:checked ~ label:before {
  background-color: #8ce196;
}
input.yadaSwitch-round:checked ~ label:after {
  margin-left: 20px;
}

/****************/
/* Bootstrap 4  */
/****************/
/* These are needed to use Bootstrap 4 on Bootstrap 3 code */

.btn-default {
    color: #333;
    background-color: #fff;
    border-color: #ccc;
}
.btn-default:hover {
    color: #333;
    background-color: #e6e6e6;
    border-color: #adadad;
}

/***************/
/* Yada Icons  */
/***************/
/* This is for bootstrap 5 but can be overridden for any other icon library */

.yadaIcon {
	font-family: "bootstrap-icons";
	-moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
    font-weight: normal;
}

.yadaIcon-ok:before {
    content: "\F26B";
}

.yadaIcon-info:before, .yadaIcon-warning:before {
    content: "\F333";
}

.yadaIcon-error:before {
    content: "\F623";
}

.yadaIcon-close:before {
    content: "\F62A";
}

.yadaIcon-down:before {
    content: "\F078";
}

.yadaIcon-up:before {
    content: "\F077";
}

.yadaIcon-edit:before {
    content: "\F044";
}

.yadaIcon-delete:before {
    content: "\F2ED";
}

.yadaIcon-clone:before {
    content: "\F24D";
}

.yadaIcon-add:before {
    content: "\F067";
}

.yadaIcon-help:before {
    content: "\F505";
}

/********************/
/* Image crop page  */
/********************/

#yadaCropForm {
    display: flex;
    flex-direction: column;
    align-items: center;
}
#yadaCropForm .jcrop-widget.yadaDesktop {
	border-color: green;
}
#yadaCropForm .legend p {
	cursor: pointer;
}
#yadaCropForm .legend p.active {
	font-weight: bold;
}
#yadaCropForm .legend p > span:first-child {
	width: 10px;
	height: 10px;
	display: inline-block;
}
#yadaCropForm .jcrop-widget.yadaDesktop .jcrop-handle,
#yadaCropForm .legend p.yadaDesktop > span:first-child {
    background-color: green;
}
#yadaCropForm .jcrop-widget.yadaMobile {
	border-color: yellow;
}
#yadaCropForm .jcrop-widget.yadaMobile .jcrop-handle,
#yadaCropForm .legend p.yadaMobile > span:first-child {
    background-color: yellow;
}

#yadaCropForm .jcrop-widget.yadaPdf {
	border-color: red;
}
#yadaCropForm .jcrop-widget.yadaPdf .jcrop-handle,
#yadaCropForm .legend p.yadaPdf > span:first-child {
    background-color: red;
}

#yadaCropForm #yadaCropImage {
    max-width: 100%;
    max-height: 60vh;
}

.yadaImageCropper {
	margin: 50px 0;
}

.yadaUploadedImages {
	width: 80%;
    margin: 50px auto;
}

.yadaUploadedImages a {
	cursor: zoom-in;
}

/**************/
/* CKEditor 5 */

/* Needed when in a modal to show ckeditor link dialog */
.ck.ck-balloon-panel {
	z-index: 999991 !important;
}

/*****************/
/* Cookie Banner */

#yadaCookieBanner {
	background-color: black;
	color: white;
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    padding: 0 0 10px 5px;
    z-index: 9876543;
}
#yadaCookieBanner .closeIcon {
	position: absolute;
    right: 10px;
    cursor: pointer;
    width: 20px;
    height: 20px;
    background-image: url("images/cookieClose.png");
    background-repeat: no-repeat;
    margin-top: 5px;
}
#yadaCookieBanner p {
	display: inline-block;
    margin-right: 10px;
    margin-top: 20px;
}
#yadaCookieBanner a {
	color: white;
}

.yadaInputCounter {
	font-size: 0.7em;
	display: flex;
	align-items: flex-end;
}
.yadaInputCounter span:first-child {
	margin-right: 0.4em;
}
.yadaInputCounter span:first-child:after {
	content: "/";
	margin-left: 0.4em;
}

.yadaInputNumber {
	text-align: end;
}

/******************/
/* Element Loader */

.yadaElementLoaderOverlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;

}
.yadaElementLoaderIcon {
	border: 4px solid rgba(0, 0, 0, 0.1);
	border-top: 4px solid #000;
	border-radius: 50%;
	width: 40px;
	height: 40px;
	animation: yadaSpin 1s linear infinite;
}

@keyframes yadaSpin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}
