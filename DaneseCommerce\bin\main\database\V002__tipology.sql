# Modifica della tipology di Article, e nuovi campi per prodotto

drop table Article_tipology;
create table Article_tipology (Article_id bigint not null, tipology_id bigint not null) engine=InnoDB;

create table Product_notes (Product_id bigint not null, notes varchar(8192), locale varchar(32) not null, primary key (Product_id, locale)) engine=InnoDB;
create table Product_specs (Product_id bigint not null, specs varchar(255), locale varchar(32) not null, primary key (Product_id, locale)) engine=InnoDB;

alter table Article_tipology add constraint UKb03qdw5l641v0ja6mfu8ic16v unique (tipology_id, Article_id);
alter table Article_tipology add constraint FKhpfvtm2r89ucbxlgtnms1tf5q foreign key (tipology_id) references YadaPersistentEnum (id);

alter table Product_notes add constraint FK37c2i8c4h12n33uvk99658ou6 foreign key (Product_id) references YadaProduct (id);
alter table Product_specs add constraint FKj901iuf672g6r3pfacw271vyy foreign key (Product_id) references YadaProduct (id);
