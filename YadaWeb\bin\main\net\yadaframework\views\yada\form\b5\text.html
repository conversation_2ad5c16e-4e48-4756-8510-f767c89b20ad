<!DOCTYPE html>
<html xmlns:yada="http://www.yadaframework.net" xmlns:th="http://www.thymeleaf.org">
<head>
<meta charset="UTF-8">
</head>

<!--/* [Bootstrap 5 version]
A text input element.
It becomes a textarea if maxlength>80.
It uses CKEditor5 when editor=true. 
The CKEditor script must be loaded for this to work. The possible toolbar options depend on the chosen plugins.

Parameters:
- fieldName = name of the field holding the value
- id = id of the field holding the value (optional)
- labelKey = key in messages.properties for the label text
- label = label to use, if not using a labelKey (optional)
- classes = base classes to set on the input/textarea element. When not specified, "form-control" is used
- placeholderKey = key in messages.properties for the placeholder text (optional)
- placeholder = placeholder to use, if not using a placeholderKey (optional)
- type = the input type ('text', 'hidden', 'password', 'number', 'email', 'date', ...) (optional)
- inline = true to have label and field inline (optional) TO BE IMPLEMENTED for B5
- min = minimum value (optional)
- max = maximum value (optional)
- step = numerical increment (optional)
- required = true for a required value (optional)
- disabled = true for a disabled field - NOT SENT TO SERVER (optional)
- readonly = true for readonly (optional)
- maxlength = max number of characters accepted (optional). 
              When greater than 80, a textarea is generated instead of an input tag, unless notextarea is true.
- rows = number of rows for the textarea (optional)
- notextarea = do not automatically use textarea when maxlength>80 (optional)
- addonLeft = character to set as a bootstrap "input-group-prepend" to the left of the input field (optional)
- addonRight = character to set as a bootstrap "input-group-text" to the right of the input field (optional)
- help = testo per il pulsante di help, se non c'è helpKey (optional)
- helpKey = chiave del testo per il pulsante di help (optional)
- editor = true/false to use ckeditor
- toolbar = the ckeditor toolbar options as an array of strings: ["undo", "redo", "bold"]
- noFeedback = (obsolete) true to remove space reserved for field feedback icon (optional)
- noShowPassword = true to remove the icon that shows the password in cleartext
- tabindex = the tabindex for the field (optional)
- autocomplete = the autocomplete value (optional)
- oninput = function to call on the input event. It receives the 'input' event (optional)
- ariaDescribedby = id for the aria-describedby attribute
- pasteHandler =  function to call on paste (optional)
- linkCustomClass = class to add to the link element (optional)

Example:

<div th:replace="/yada/form/b5/text::field(fieldName='subject',maxlength=64,required=true,labelKey='form.label.subject')"></div>
<div th:replace="/yada/form/b5/text::field(fieldName='id',type='hidden')"></div>


*/-->
<body th:remove="tag">
<!--/* TODO see boolean.html for how to set a unique default id when fields in a modal have the same name as fields in the main page */-->
<th:block th:with="tmp=${fieldName+'seqName'},seqName=${#strings.replace(#strings.replace(tmp,']',''),'[','')}">
<input th:if="${#strings.equals(type,'hidden')}" th:field="*{{__${fieldName}__}}" th:disabled="${disabled}" th:type="hidden" th:id="${id}?:${#ids.seq(seqName)}" />
<div th:if="${!#strings.equals(type,'hidden')}" class="mb-3"
	th:with="hasError=${#fields.hasErrors('__${fieldName}__')}">
	<label th:if="${label!=null || labelKey!=null}" class="form-label" th:for="${id}?:${#ids.next(seqName)}" th:utext="${labelKey!=null?#messages.msg(labelKey):label}">Nome:</label>
	<div th:classappend="|__${help!=null || addonRight!=null || addonLeft!=null || (type=='password' && noShowPassword!=true)}?input-group__ __${hasError==true}?is-invalid__|" th:style="${inline==true?'display:inline-table':'width:100%'}"> <!--/* il width serve perché se non c'è l'help il campo resta corto (ad esempio i campi password) */-->
		<!--/* Can't use th:field="*{__${fieldName}__}" because somehow passwords remain blank when they already have a value */-->
		<div th:if="${addonLeft!=null}" class="input-group-prepend">
			<span class="input-group-text" th:text="${addonLeft}">$</span>
		</div>
		<input th:if="${(maxlength==null or maxlength <= 80 or notextarea==true) and editor!=true}" th:name="${fieldName}" th:value="*{{__${fieldName}__}}" 
			th:placeholder="${placeholderKey!=null?#messages.msg(placeholderKey):placeholder}"
			th:required="${required}" th:disabled="${disabled}" th:type="${type}?:'text'" th:readonly="${readonly}" 
			th:min="${min}" th:max="${max}" th:step="${step}" 
			th:class="${classes}?:'form-control'"
			th:classappend="|${hasError==true?'is-invalid':''} ${readonly==true?'yadaReadonly':''}|"
			th:maxlength="${maxlength}" 
			th:tabindex="${tabindex}"
			th:autocomplete="${autocomplete}"
			th:id="${id}?:${#ids.seq(seqName)}"  
			th:aria-describedby="${ariaDescribedby}?:(${id}?:${#ids.next(seqName)})"> <!--/* WRONG ID */-->
		<div th:if="${addonRight!=null}" class="input-group-text">
			<span class="input-group-text" th:text="${addonRight}">.00</span>
		</div>
		<span th:if="${type=='password' && noShowPassword!=true}" class="input-group-btn">
			<a class="btn btn-default yadaShowPassword" href="#" title="Show password"><span class="bi bi-eye"></span></a>
		</span>
		<textarea th:if="${(maxlength > 80 and notextarea!=true) or editor==true}" th:rows="${rows}?:3" th:field="*{{__${fieldName}__}}" 
			th:placeholder="${placeholderKey!=null?#messages.msg(placeholderKey):placeholder}"
			th:required="${required}" th:disabled="${disabled}" th:maxlength="${maxlength}" 
			th:class="${classes}?:'form-control'"
			th:classappend="|${hasError==true?'is-invalid':''} ${readonly==true?'yadaReadonly':''}|"
			th:tabindex="${tabindex}"
			th:autocomplete="${autocomplete}"
			th:id="${id}?:${#ids.seq(seqName)}" 
			th:attr="aria-describedby=${id}?:${#ids.next(seqName)}"></textarea> <!--/* WRONG ID */-->
		<th:block th:with="help = ${helpKey!=null}?#{__${helpKey}__}:${help}">
			<a th:if="${help!=null}" class="yadaHelpButton input-group-text" data-trigger="focus" data-container="body" 
				data-toggle="popover" data-placement="auto bottom" tabindex="-1"
				data-html="true"
				th:attr="data-content=${help}">
				<i class="yadaIcon yadaIcon-help"></i>
			</a>
		</th:block>
	</div>
	<script th:if="${editor==true}" type="text/javascript" th:inline="javascript">
		//<![CDATA[
        $(function(){
        	var targetId = [[${id}?:${#ids.prev(seqName)}]];
        	ClassicEditor
            	.create(document.getElementById(targetId), {
            		// Possible values for Classic build:
            		// ["undo", "redo", "bold", "italic", "blockQuote", "ckfinder", "imageTextAlternative", "imageUpload", "heading", "imageStyle:full", "imageStyle:side", "indent", "outdent", "link", "numberedList", "bulletedList", "mediaEmbed", "insertTable", "tableColumn", "tableRow", "mergeTableCells"]
            		// "link" removed because it doesn't work in modals
            		removePlugins: [ 'Image', 'Media embed' ],
            		toolbar: [[${toolbar}?:${ {"undo", "redo", "bold", "italic", "blockQuote", "heading", "indent", "outdent", "numberedList", "bulletedList", "insertTable", "tableColumn", "tableRow", "mergeTableCells"} }]],
            		image: {
            			toolbar: ['|']
            		},
            		mediaEmbed: {
            			toolbar: ['|']
            		}
            	})
				.then(editor => {
					let pasteHandler = [[${pasteHandler}]];
					if (pasteHandler) {
						editor.plugins.get('Clipboard').on('inputTransformation', (evt, data) => {
							window[pasteHandler](evt, data);
						});
					}

					let linkCustomClass = [[${linkCustomClass}]];
					if (linkCustomClass) {
						editor.conversion.for('downcast').attributeToElement({
							model: 'linkHref',
							view: (attributeValue, { writer }) => writer.createAttributeElement('a', {
								class: linkCustomClass,
								href: attributeValue,
								target: "_blank"
							}),
							converterPriority: 'highest'
						});
					}
				})
				// Show all toolbar values with the following code:
            	// .then( editor => {
        		// 	console.log( 'Editor was initialized', editor );
        		// 	console.log(Array.from( editor.ui.componentFactory.names() ));
            	// })
            	.catch( error => {
                console.error( error );
            } );
        });
		//]]>
	</script>
	<script th:if="${oninput!=null}" type="text/javascript">
		//<![CDATA[
		$("[['#'+${#ids.prev(seqName)}]]").on('input', function (e) {
			[[${oninput}]](e);
		});
		//]]>
	</script>
	<div th:each="err : ${#fields.errors('__${fieldName}__')}" class="help-block invalid-feedback yadaInputError" role="alert">
		<span th:text="${err}">Error Text</span>
	</div>
</div>    	
</th:block>
</body>
</html>
		