#!/bin/bash
# Script di deploy del sito - copiato ogni volta sul server
# Parametri:
# 1 - path del file war, e.g. /srv/amdcol/deploy/ldm.prod.[02-maggio-2014-17.17].war
# 2 - path tomcat base, e.g. /srv/amdcol/tomcat
# 3 - nome della directory che corrisponde al "virtual host", e.g. alpha.letturedametropolitana.it
# Va eseguito in sudo

warFile=$1
tomcatBase=$2
vhost=$3

destDir=${tomcatBase}/webapps
scriptDir=$( dirname $0 )

echo
echo $(date) Start of Sito deployment script
echo warFile=$warFile
echo tomcatBase=$tomcatBase
echo vhost=$vhost
echo Stopping Tomcat...
$scriptDir/site-shutdown.sh

# Cancello la work di tomcat che spesso rimane sporca, e il context
rm -rf ${tomcatBase}/work/Catalina/${vhost}
rm -f ${tomcatBase}/conf/Catalina/${vhost}/ROOT.xml
rm -rf ${destDir}/ROOT 2>/dev/null
# unzip -qd ${destDir}/ROOT ${warFile}
unzip -qod ${destDir}/ROOT ${warFile}
chown -R tomcat8:tomcat8 ${destDir}/ROOT

echo Starting Tomcat...
$scriptDir/site-startup.sh

# Elimino il file di deploy per evitare di riempire il filesystem
rm ${warFile}

echo $(date) End of Sito deployment script
