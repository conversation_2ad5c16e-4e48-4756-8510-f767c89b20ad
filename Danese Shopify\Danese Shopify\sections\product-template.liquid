{% include 'breadcrumb' %}

<div class="product" id="product-{{ product.id }}" data-product="{{ product.id }}">
  {% case section.settings.showcase_size %}
    {% when 'small' %}
      {% assign product_showcase_grid_class = '2/5--lap-and-up' %}
      {% assign product_info_grid_class = '3/5--lap-and-up' %}

    {% when 'normal' %}
      {% assign product_showcase_grid_class = '1/2--lap-and-up' %}
      {% assign product_info_grid_class = '1/2--lap-and-up' %}

    {% when 'big' %}
      {% assign product_showcase_grid_class = '3/5--lap-and-up' %}
      {% assign product_info_grid_class = '2/5--lap-and-up' %}
  {% endcase %}

  <div class="product__details">
    <div class="inner">
      {% include 'product-meta', show_vendor: section.settings.show_vendor, for_mobile: true %}

      <div class="grid">
        {% comment %}
        -----------------------------------------------------
        PRODUCT SLIDESHOW
        -----------------------------------------------------
        {% endcomment %}

        {% if product.selected_or_first_available_variant.image %}
          {% assign initial_image_position = product.selected_or_first_available_variant.image.position | minus: 1 %}
        {% else %}
          {% assign initial_image_position = 0 %}
        {% endif %}

        <div class="product__showcase grid__cell {{ product_showcase_grid_class }}">
          <ul class="product__slideshow"
              data-initial-slide="{{ initial_image_position }}"
              data-rotation-effect="{{ section.settings.showcase_rotation_effect }}"
              data-zoom-enabled="{{ section.settings.showcase_zoom_enabled }}"
              data-zoom-magnification="{{ section.settings.showcase_zoom_magnification }}">
            {% if product.images.size == 0 %}
              <li class="product__slideshow-slide" data-type="image" data-image-large-url="{{ product.featured_image | img_url: '1500x' }}">
                <img class="product__slideshow-image"
                     width="{{ product.featured_image.width }}"
                     height="{{ product.featured_image.height }}"
                     src="{{ product.featured_image | img_url: '1000x' }}"
                     srcset="{{ product.featured_image | img_url: '750x', format: 'pjpg' }} 750w, {{ product.featured_image | img_url: '1000x', format: 'pjpg' }} 1000w, {{ product.featured_image | img_url: '1200x', format: 'pjpg' }} 1400w"
                     sizes="(max-width: 800px) 100vw, 50vw"
                     alt="{{ product.featured_image.alt | escape }}">
              </li>
            {% else %}
              {% for image in product.images %}
                {% if image.alt contains 'youtube.com/embed' or image.alt contains 'player.vimeo.com' %}
                  <li class="product__slideshow-slide" data-type="video" data-index="{{ forloop.index0 }}" {% unless forloop.first %}style="display: none;"{% endunless %}>
                    <div class="product__video-wrapper video__wrapper">
                      <iframe class="product__slideshow-video video__element" width="560" height="349" src="{{ image.alt }}" frameborder="0" allowfullscreen=""></iframe>
                    </div>
                  </li>
                {% else %}
                  <li class="product__slideshow-slide" data-type="image" data-index="{{ forloop.index0 }}" data-image-id="{{ image.id }}" data-image-large-url="{{ image | img_url: '1500x' }}" {% unless forloop.first %}style="display: none;"{% endunless %}>
                    <img class="product__slideshow-image"
                         width="{{ image.width }}"
                         height="{{ image.height }}"
                         src="{{ image | img_url: '1000x' }}"
                         srcset="{{ image | img_url: '750x', format: 'pjpg' }} 750w, {{ image | img_url: '1000x', format: 'pjpg' }} 1000w, {{ image | img_url: '1200x', format: 'pjpg' }} 1400w"
                         sizes="(max-width: 800px) 100vw, 50vw"
                         alt="{{ image.alt | escape }}">
                  </li>
                {% endif %}
              {% endfor %}
            {% endif %}
          </ul>

          {% if section.settings.showcase_show_thumbnails %}
            <ul class="product__thumbnails">
              {% for image in product.images %}
                <li class="product__thumbnail" data-index="{{ forloop.index0 }}">
                  <img src="{{ image.src | img_url: '200x' }}" alt="{{ image.alt | escape }}">
                </li>
              {% endfor %}
            </ul>
          {% endif %}
        </div>

        {% comment %}
        -----------------------------------------------------
        PRODUCT INFO
        -----------------------------------------------------
        {% endcomment %}

        <div class="product__info grid__cell {{ product_info_grid_class }}">
          {% include 'product-meta', show_vendor: section.settings.show_vendor, for_mobile: false %}

          {% if section.settings.form_position == 'before_description' %}
            {% include 'product-form' %}
          {% endif %}

          {% if section.settings.always_show_description and product.description != blank %}
            <div class="product__description rte">
              {{ product.description }}
            </div>
          {% endif %}

          <div class="product__share">
            <div class="share-buttons">
              <span class="share-buttons__label">{{ 'general.social.share' | t }}</span>

              <ul class="share-buttons__list">
                {% assign share_url = shop.url | append: product.url %}
                {% assign twitter_text = product.title %}
                {% assign pinterest_description = product.description | strip_html | truncatewords: 15 | url_param_escape %}
                {% assign pinterest_image = product.featured_image | img_url: 'large' | prepend: 'https:' %}

                <li class="share-buttons__item">
                  <a href="https://www.facebook.com/sharer.php?u={{ share_url }}" target="_blank">{% include 'icon' with 'facebook' %}</a>
                </li>

                <li class="share-buttons__item">
                  <a href="https://twitter.com/share?{% if twitter_text != blank %}text={{twitter_text}}&{% endif %}url={{ share_url }}" target="_blank">{% include 'icon' with 'twitter' %}</a>
                </li>

                <li class="share-buttons__item">
                  <a href="https://pinterest.com/pin/create/button/?url={{ share_url }}{% if pinterest_image != blank %}&media={{ pinterest_image }}{% endif %}&description={{ pinterest_description }}" target="_blank">{% include 'icon' with 'pinterest' %}</a>
                </li>
              </ul>
            </div>
          </div>

          {% if section.settings.form_position == 'after_description' %}
            {% include 'product-form' %}
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    $('#product-{{ product.id }}').product({
      product: {{ product | json }},
      selectedVariantId: {% if product.selected_variant %}{{ product.selected_variant.id }}{% else %}null{% endif %},
      context: 'main',
      enableHistoryState: true
    });
  });
</script>

{% schema %}
{
  "name": "Product page",
  "settings": [
    {
      "type": "checkbox",
      "id": "show_vendor",
      "label": "Show vendor",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "hide_variants_if_only_one",
      "label": "Hide selectors if there is only one variant",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "always_show_description",
      "label": "Show description on the right",
      "info": "If enabled, description will be duplicated on the right",
      "default": true
    },
    {
      "type": "select",
      "id": "form_position",
      "label": "Show \"add to cart\"...",
      "options": [
        {
          "value": "before_description",
          "label": "Before description"
        },
        {
          "value": "after_description",
          "label": "After description"
        }
      ],
      "default": "after_description"
    },
    {
      "type": "header",
      "content": "Image slideshow"
    },
    {
      "type": "select",
      "id": "showcase_size",
      "label": "Size",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "normal",
          "label": "Normal"
        },
        {
          "value": "big",
          "label": "Big"
        }
      ],
      "default": "big"
    },
    {
      "type": "select",
      "id": "showcase_rotation_effect",
      "label": "Rotation effect",
      "options": [
        {
          "value": "fade",
          "label": "Fade"
        },
        {
          "value": "slide",
          "label": "Slide"
        }
      ],
      "default": "slide"
    },
    {
      "type": "checkbox",
      "id": "showcase_show_thumbnails",
      "label": "Show thumbnails",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "showcase_zoom_enabled",
      "label": "Enable image zoom",
      "info": "This is disabled on mobile.",
      "default": true
    },
    {
      "type": "select",
      "id": "showcase_zoom_magnification",
      "label": "Zoom magnification (if enabled)",
      "options": [
        {
          "value": "0.5",
          "label": "50%"
        },
        {
          "value": "1",
          "label": "100%"
        },
        {
          "value": "1.5",
          "label": "150%"
        },
        {
          "value": "2",
          "label": "200%"
        }
      ],
      "default": "1.5"
    },
    {
      "type": "header",
      "content": "Reviews"
    },
    {
      "type": "paragraph",
      "content": "Make sure that you have installed [Shopify's free Product Reviews](https://apps.shopify.com/product-reviews) app before enabling this option."
    },
    {
      "type": "checkbox",
      "id": "show_reviews_badge",
      "label": "Show reviews badge",
      "default": false
    }
  ]
}
{% endschema %}