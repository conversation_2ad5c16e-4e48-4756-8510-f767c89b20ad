# cfgHostname=false
# Set your new user name here
cfgUser=dncuserprod
# Set your user password here:
cfgUserPwd=lFfn48slb9fi8
# Set your key here:
cfgAuthorizedKeys="ssh-rsa AAAAB3NzaC1yc2EAAAABJQAAAgEAg3restMMTJIrGEaQ2ZB3UflG8aBoOoE2/tE5qhsMh+BjbpJLoLhwr8GffDTtyctSS0GGFoT4Cn9QCNL64bNA/1PKP5m8OvHe8gslvUClS5/s9coTfg81Z78bcGJVGeQvWBRL1x18nc5WkKmOAe7lFPRDbQEf9VJ7/601HqTX4ek2UZ3x09P0VmB0axRIO9LagcHlXXVTGnTPXFGY+wvHNXs+srP8SscKoaavbUB2IdhONi+rve3ZH3k89vNVmj9skWNFUadakZcoJDJhhT+4Q9UybgNkPEPhtMR/BdIG30UuaIUCYcLwgEvLneF/b4YnHuRQ8E/Kyd9UfAZrNd5in0pNfwUIWa+Qv9mgjqWxDstAH38/8daY6NQyZiXzKQOs0TWSiSErfxyUVsqKfAX+Zq0nTD2tnFhDpJ5AE+UgDoVV3k6KUmh5bXkAJKmd/omqvAlEDmF5wXPjQhLPwHYI6Tbi8nQVXOo/Zu+jf5smV8cZYHgSGUCrCUS11L8/h/zUvZiO2QHZie1CZcoHeR2frqQBOef4a3Yqn9n+zRDjnbcwL308P2SSjOsr8V1eSI4x5uiH5RSWhFcYzEwZW9bdHEV+wHebKKVN92eAeicpGZ+IBq1fH18sNCOQqsCN7cqYzgRW3YzJub8sezIVRzMk/UssSCyG4U9r4C/yGN8B1uM= dnc produzione"
#cfgNoUfw=true
cfgPkgJava="openjdk-8-jdk"
# Installare la nuova versione di libtcnative scaricandola e compilandola (vedi terraforming.sh)
# cfgPkgTomcat="tomcat8 tomcat8-admin libtcnative-1"
cfgTomcatTarGz=http://it.apache.contactlab.it/tomcat/tomcat-8/v8.5.31/bin/apache-tomcat-8.5.31.tar.gz
# Pagina dove cercare la url per il native connector: http://tomcat.apache.org/download-native.cgi
# Viene scaricato e compilato, ma solo se si installa tomcat via tar.gz
cfgTomcatNativeTarGz=http://it.apache.contactlab.it/tomcat/tomcat-connectors/native/1.2.16/source/tomcat-native-1.2.16-src.tar.gz
cfgTomcatTarGzHome=/srv/dncprod/tomcatprod
# L'utente può essere "root" (per le istallazioni sulla porta 80) oppure "tomcat8", "tomcat9", etc.
# ma viene usato solo se si installa via TarGz
cfgTomcatUser=tomcat8
cfgTomcatRam=2G
cfgTomcatTimeout=20000
cfgTomcatManagerPwd=hn8odf74odthn8
cfgPkgMysql="mysql-server-5.7"
cfgMysqlRootPwd=vsdh4sejsdh7k
cfgPkgApache="apache2"
cfgPkgModJk="libapache2-mod-jk"
# cfgPkgPhp="libapache2-mod-php5 php5 php5-mysql"
cfgPkgOther="imagemagick unzip"
cfgEmail=false
# Percentage of free disk space to dedicate to squid3
# Comment out to install squid
# cfgSquidPercent="80"
# Ram for squid cache (default is 256 MB)
# cfgSquidRam="100 MB"
# Install certbot for SSL certificates https://certbot.eff.org
# Enabled by default - installation only, no configuration
# cfgCertbot=false