<header class="page__header">
  {% include 'breadcrumb' %}

  <div class="page__header-wrapper">
    <div class="container">
      <h1 class="page__title">{{ 'gift_cards.general.title' | t }}</h1>
    </div>
  </div>
</header>

<div class="page__content">
  <div class="container">
    <div class="gift-card">
      <div class="gift-card__illustration">
        <img src="{{ 'gift-card/card.jpg' | shopify_asset_url }}" alt="{{ 'gift_cards.alt.illustration' | t }}">
      </div>

      <h2 class="gift-card__issued-text">{{ 'gift_cards.issued.issued_text' | t }}</h2>

      {% assign formatted_initial_value = gift_card.initial_value | money_without_trailing_zeros: gift_card.currency %}
      {% assign formatted_initial_value_stripped = formatted_initial_value | strip_html %}

      <span class="gift-card__amount">
                {% if gift_card.balance != gift_card.initial_value %}
                  {{ gift_card.balance | money }} {{ 'gift_cards.issued.left_balance' }}
                {% else %}
                  {{ formatted_initial_value }}
                {% endif %}
              </span>

      <p class="gift-card__instructions">
        {% unless gift_card.enabled %}
        {{ 'gift_cards.issued.disabled' | t }}
        {% endunless %}

        {{ 'gift_cards.issued.redeem' | t }}

        {% if gift_card.expires_on %}
          {% capture expiry_date %}{{ gift_card.expires_on | date: format: 'month_day_year' }}{% endcapture %}
          {{ 'gift_cards.issued.expires_on' | t: expiry: expiry_date }}
        {% endif %}
      </p>

      <span class="gift-card__code" id="gift-card-digits" onclick="selectText('gift-card-digits');">{{ gift_card.code }}</span>

      <div id="qr-code" class="gift-card__qr-code"></div>

      {% if gift_card.pass_url %}
        <a class="gift-card__apple-wallet" href="{{ gift_card.pass_url }}">
          <img src="{{ 'gift-card/add-to-apple-wallet.svg' | shopify_asset_url }}" width="120" height="40" alt="{{ 'gift_cards.alt.add_to_apple_wallet' | t }}">
        </a>
      {% endif %}

      <div class="gift-card__actions button-group">
        <div class="button-group__item">
          <a href="#" class="button button--secondary" onclick="window.print();">{{ 'gift_cards.issued.print' | t }}</a>
        </div>

        <div class="button-group__item">
          <a href="{{ shop.url }}" class="button button--primary">{{ 'gift_cards.issued.shop_link' | t }}</a>
        </div>
      </div>
    </div>
  </div>
</div>

{{ 'vendor/qrcode.js' | shopify_asset_url | script_tag }}

<script>
  /*============================================================================
   Auto-select gift card code on click, based on ID passed to the function
   - Use a different method depending on IE or others
   ==============================================================================*/
  function selectText(element) {
    var doc = document,
      text = doc.getElementById(element),
      range = null;

    if (doc.body.createTextRange) { // ms
      range = doc.body.createTextRange();
      range.moveToElementText(text);
      range.select();
    } else if (window.getSelection) { // moz, opera, webkit
      var selection = window.getSelection();

      range = doc.createRange();
      range.selectNodeContents(text);
      selection.removeAllRanges();
      selection.addRange(range);
    }
  }

  new QRCode(document.getElementById('qr-code'), {
    text: {{ gift_card.qr_identifier | json }},
    width: 150,
    height: 150
  });
</script>