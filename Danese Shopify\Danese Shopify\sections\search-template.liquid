{% if search.performed == false or search.results_count == 0 %}
  {% assign search_not_performed_or_empty = true %}
{% endif %}

<header class="page__header {% if search_not_performed_or_empty %}page__header--no-border{% endif %}">
  {% include 'breadcrumb' %}

  <div class="page__header-wrapper">
    <div class="container">
      <h1 class="page__title">{{ 'search.general.title' | t }}</h1>

      {% if search.performed %}
        {% if search.results_count == 0 %}
          <div class="page__description page__description--centered">
            <p>{{ 'search.general.notice' | t }}</p>
          </div>
        {% else %}
          <div class="page__description page__description--centered">
            {% assign search_terms = search.terms | remove: '*' | escape %}
            <span>{{ 'search.general.results_count' | t: count: search.results_count, terms: search_terms }}</span>
          </div>
        {% endif %}
      {% endif %}

      {% if search_not_performed_or_empty %}
        <div class="page__header-actions">
          <form class="search__form" action="/search" method="GET" role="search">
            <input type="hidden" name="type" value="{{ settings.search_mode }}">
            <input type="search" name="q" class="search__input" autofocus="autofocus" autocorrect="off" placeholder="{{ 'search.general.input_placeholder' | t }}" value="{{ search.terms | escape }}">
          </form>
        </div>
      {% endif %}
    </div>
  </div>
</header>

{% unless search_not_performed_or_empty %}
  {% paginate search.results by 28 %}
    <div class="inner">
      <div class="search-results collection--grid grid grid--gallery grid--middle">
        {% for result in search.results %}
          {% case result.object_type %}
            {% when 'product' %}
              {% include 'product-grid-item', product: result, grid_items_per_row: 4, show_vendor: section.settings.show_vendor, show_quick_shop: section.settings.show_quick_shop %}

            {% when 'article' %}
              {%- assign url_parts = result.url | split: '/' -%}
              {%- assign article_blog_handle = url_parts[2] -%}
              {%- assign article_blog = blogs[article_blog_handle] -%}

              <div class="collection__item product-item grid__cell 1/{{ settings.mobile_grid_items_per_row }} 1/{{ settings.tablet_grid_items_per_row }}--handheld 1/4--lap-and-up">
                <div class="product-item__inner">
                  <a href="{{ result.url }}" class="product-item__image">
                    <img src="{{ result.image | img_url: '600x' }}" alt="{{ result.image.alt }}">
                  </a>

                  <div class="product-item__info">
                    <h3 class="product-item__vendor">
                      <a href="{{ article_blog.url }}">{{ article_blog.title }}</a>
                    </h3>

                    <h2 class="product-item__title">
                      <a href="{{ result.url }}">{{ result.title }}</a>
                    </h2>
                  </div>
                </div>
              </div>

            {% when 'page' %}
              <div class="collection__item product-item grid__cell 1/{{ settings.mobile_grid_items_per_row }} 1/{{ settings.tablet_grid_items_per_row }}--handheld 1/4--lap-and-up">
                <div class="product-item__inner">
                  <a href="{{ result.url }}" class="product-item__image">
                    <img src="{{ result.image | img_url: 'x500' }}" alt="{{ result.image.alt }}">
                  </a>

                  <div class="product-item__info">
                    <h3 class="product-item__vendor">{{ 'search.general.page' | t }}</h3>

                    <h2 class="product-item__title">
                      <a href="{{ result.url }}">{{ result.title }}</a>
                    </h2>
                  </div>
                </div>
              </div>
          {% endcase %}
        {% endfor %}
      </div>
    </div>

    {% include 'pagination' %}
  {% endpaginate %}
{% endunless %}

{% schema %}
{
  "name": "Search page",
  "settings": [
    {
      "type": "checkbox",
      "id": "show_vendor",
      "label": "Show vendor",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_quick_shop",
      "label": "Show quick shop",
      "default": true
    }
  ]
}
{% endschema %}