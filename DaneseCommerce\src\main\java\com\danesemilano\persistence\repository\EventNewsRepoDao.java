package com.danesemilano.persistence.repository;

import java.util.List;
import java.util.Locale;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.danesemilano.persistence.entity.EventNews;

import net.yadaframework.persistence.YadaSql;
import net.yadaframework.web.YadaPageRequest;
import net.yadaframework.web.YadaPageRows;

@Repository
@Transactional(readOnly = true) 
public class EventNewsRepoDao {
	
	@PersistenceContext
	EntityManager em;
	
	/**
	 * Trova tutti i prodotti pubblicati che hanno il tag indicato
	 * @param tagId
	 * @return
	 */
	public List<EventNews> findByTagPublished(Long tagId) {
		String sql = "select n from EventNews n join n.tags t where n.publishDate <= NOW() and t.id = :tagId";
		return em.createQuery(sql, EventNews.class)
			.setParameter("tagId", tagId)
			.getResultList();
	}

	/**
	 * Trova una pagina di news pubblicate
	 * @param pageable
	 * @return
	 */
	public YadaPageRows<EventNews> findPublished(YadaPageRequest pageable) {
		String sql = "select n from EventNews n join fetch n.content where n.publishDate <= NOW()";
		YadaSql yadaSql = YadaSql.instance().selectFrom(sql);
		boolean isPage = pageable!=null && pageable.isValid();
		if (isPage) {
			yadaSql.orderBy(pageable);
		}
		TypedQuery<EventNews> query = yadaSql.query(em, EventNews.class);
		if (isPage) {
			query.setFirstResult(pageable.getFirstResult()).setMaxResults(pageable.getSize());
		}
		return new YadaPageRows<EventNews>(query.getResultList(), pageable);
	}
	
	/**
	 * Trova tutte le EventNews che hanno titolo o sottotitolo contenente la stringa passata
	 * @param searchString
	 * @param locale
	 * @return
	 */
	public List<EventNews> search(String searchString, Locale locale) {
		String sql = "select en from EventNews en join en.title t left join en.subtitle s where en.publishDate <= NOW() and KEY(t)=:locale and KEY(s)=:locale and (lower(t) like :s or lower(s) like :s)";
		return em.createQuery(sql, EventNews.class)
			.setParameter("s", searchString)
			.setParameter("locale", locale)
			.getResultList();
	}
	
	/**
	 * Update a new main image attachment to a eventNews
	 * @param eventNewsId
	 * @param yadaAttachedFileId
	 */
	@Transactional(readOnly = false) 
	public void setImage(Long eventNewsId, Long yadaAttachedFileId) {
		String sql = "UPDATE EventNews SET image_id=:yadaAttachedFileId where id=:eventNewsId";
		em.createNativeQuery(sql)
			.setParameter("eventNewsId", eventNewsId)
			.setParameter("yadaAttachedFileId", yadaAttachedFileId)
			.executeUpdate();
	}
	
	/**
	 * Update a new thumbnail image attachment to a eventNews
	 * @param eventNewsId
	 * @param yadaAttachedFileId
	 */
	@Transactional(readOnly = false) 
	public void setThumbnailImage(Long eventNewsId, Long yadaAttachedFileId) {
		String sql = "UPDATE EventNews SET thumbnail_id=:yadaAttachedFileId where id=:eventNewsId";
		em.createNativeQuery(sql)
			.setParameter("eventNewsId", eventNewsId)
			.setParameter("yadaAttachedFileId", yadaAttachedFileId)
			.executeUpdate();
	}

	@Transactional(readOnly = false)
	public EventNews save(EventNews entity) {
		if (entity==null) {
			return null;
		}
		if (entity.getId()==null) {
			em.persist(entity);
			return entity;
		}
		return em.merge(entity);
	}
	
}
