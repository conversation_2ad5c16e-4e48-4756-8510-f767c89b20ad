[{"name": "theme_info", "theme_name": "<PERSON><PERSON>", "theme_author": "Maestrooo", "theme_version": "4.3.3", "theme_documentation_url": "http://support.maestrooo.com/", "theme_support_email": "<EMAIL>"}, {"name": "Colors", "settings": [{"type": "header", "content": "General"}, {"type": "color", "id": "background", "label": "Background", "default": "#ffffff"}, {"type": "color", "id": "secondary_background", "label": "Secondary background", "default": "#f8f8f8", "info": "For best results, we recommend you to select a color that is a slight variation of the main color."}, {"type": "color", "id": "heading_color", "label": "Headings", "default": "#383838"}, {"type": "color", "id": "text_color", "label": "Body text", "default": "#a5a5a5"}, {"type": "color", "id": "accent_color", "label": "Links and accents", "default": "#3d7ac6"}, {"type": "color", "id": "border_color", "label": "Lines and borders", "default": "#dddddd"}, {"type": "header", "content": "Buttons"}, {"type": "color", "id": "primary_button_color", "label": "Primary button text", "default": "#ffffff"}, {"type": "color", "id": "primary_button_background", "label": "Primary button background", "default": "#407ac6"}, {"type": "color", "id": "secondary_button_color", "label": "Secondary button text", "default": "#ffffff"}, {"type": "color", "id": "secondary_button_background", "label": "Secondary button background", "default": "#a5a5a5"}, {"type": "header", "content": "Header and footer"}, {"type": "color", "id": "header_color", "label": "Text and icons", "default": "#c1c0c0"}, {"type": "color", "id": "header_background", "label": "Background", "default": "#ffffff"}, {"type": "color", "id": "header_accent_color", "label": "Accent color", "default": "#407ac6"}, {"type": "color", "id": "header_border_color", "label": "Border", "default": "#dddddd"}, {"type": "header", "content": "Navigation sidebar"}, {"type": "color", "id": "navigation_sidebar_color", "label": "Text", "default": "#ffffff"}, {"type": "color", "id": "navigation_sidebar_background", "label": "Background", "default": "#407ac6"}, {"type": "color", "id": "navigation_sidebar_border", "label": "Border", "default": "#155cb3"}, {"type": "header", "content": "Newsletter", "info": "They apply to the newsletter on home page and the newsletter module at the bottom of all pages"}, {"type": "color", "id": "newsletter_color", "label": "Text", "default": "#ffffff"}, {"type": "color", "id": "newsletter_background", "label": "Background", "default": "#000000"}, {"type": "header", "content": "Slideshow (home page)"}, {"type": "color", "id": "slideshow_heading_color", "label": "Heading", "default": "#ffffff"}, {"type": "color", "id": "slideshow_subheading_color", "label": "Sub-heading", "default": "#ffffff"}, {"type": "header", "content": "Product"}, {"type": "color", "id": "product_label_on_sale_background", "label": "On sale label background", "default": "#da2929"}, {"type": "color", "id": "product_label_sold_out_background", "label": "Sold out / unavailable label background", "default": "#ffffff"}]}, {"name": "Typography", "settings": [{"type": "header", "content": "Headings"}, {"type": "select", "id": "heading_font", "label": "Font", "options": [{"value": "'Avant <PERSON>', <PERSON><PERSON><PERSON><PERSON>, 'Century Gothic', <PERSON><PERSON><PERSON><PERSON>, 'AppleGothic', sans-serif", "label": "Avant <PERSON>e", "group": "Sans-serif"}, {"value": "'<PERSON> Sans', 'Gill Sans MT', Calibri, sans-serif", "label": "<PERSON>", "group": "Sans-serif"}, {"value": "'HelveticaNeue', 'Helvetica Neue', Helvetica, Arial, sans-serif", "label": "Helvetica Neue", "group": "Sans-serif"}, {"value": "<PERSON><PERSON>, 'Helvetica Neue', Helvetica, sans-serif", "label": "<PERSON><PERSON>", "group": "Sans-serif"}, {"value": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif", "label": "Candara", "group": "Sans-serif"}, {"value": "Geneva, Tahoma, Verdana, sans-serif", "label": "Geneva", "group": "Sans-serif"}, {"value": "Google_Droid+Sans", "label": "Droid Sans", "group": "Sans-serif | Google Font"}, {"value": "Google_Lato", "label": "<PERSON><PERSON>", "group": "Sans-serif | Google Font"}, {"value": "Google_Montserrat", "label": "Montserrat", "group": "Sans-serif | Google Font"}, {"value": "Google_Open+Sans", "label": "Open Sans", "group": "Sans-serif | Google Font"}, {"value": "Google_PT+Sans", "label": "PT Sans", "group": "Sans-serif | Google Font"}, {"value": "Google_Roboto", "label": "Roboto", "group": "Sans-serif | Google Font"}, {"value": "Google_Source+Sans+Pro", "label": "Source Sans Pro", "group": "Sans-serif | Google Font"}, {"value": "Google_Ubuntu", "label": "Ubuntu", "group": "Sans-serif | Google Font"}, {"value": "Google_Raleway", "label": "<PERSON><PERSON><PERSON>", "group": "Sans-serif | Google Font"}, {"value": "Google_Kalam", "label": "Ka<PERSON>", "group": "Sans-serif | Google Font"}, {"value": "Google_Quicksand", "label": "Quicksand", "group": "Sans-serif | Google Font"}, {"value": "Google_Dosis", "label": "<PERSON><PERSON>", "group": "Sans-serif | Google Font"}, {"value": "Google_Maven+Pro", "label": "Maven <PERSON>", "group": "Sans-serif | Google Font"}, {"value": "Google_Josefin+Sans", "label": "<PERSON><PERSON>", "group": "Sans-serif | Google Font"}, {"value": "'Big Caslon', 'Book Antiqua', '<PERSON><PERSON><PERSON>', Georgia, serif", "label": "Big Caslon", "group": "<PERSON><PERSON>"}, {"value": "'Calisto MT', 'Bookman Old Style', <PERSON>man, 'Goudy Old Style', <PERSON><PERSON><PERSON>, 'Hoefler Text', 'Bitstream Charter', Georgia, serif", "label": "Calisto MT", "group": "<PERSON><PERSON>"}, {"value": "Baskerville, 'Baskerville Old Face', 'Hoefler Text', <PERSON><PERSON><PERSON>, 'Times New Roman', serif", "label": "Baskerville", "group": "<PERSON><PERSON>"}, {"value": "<PERSON><PERSON><PERSON>, Baskerville, 'Baskerville Old Face', 'Hoefler Text', 'Times New Roman', serif", "label": "<PERSON><PERSON><PERSON>", "group": "<PERSON><PERSON>"}, {"value": "TimesNewRoman, 'Times New Roman', Times, Baskerville, Georgia, serif", "label": "Times New Roman", "group": "<PERSON><PERSON>"}, {"value": "Google_Arapey", "label": "Ara<PERSON><PERSON>", "group": "Serif | Google Font"}, {"value": "Google_Arvo", "label": "Arvo", "group": "Serif | Google Font"}, {"value": "Google_Crimson+Text", "label": "Crimson Text", "group": "Serif | Google Font"}, {"value": "Google_Droid+Serif", "label": "Droid Serif", "group": "Serif | Google Font"}, {"value": "Google_Lora", "label": "<PERSON><PERSON>", "group": "Serif | Google Font"}, {"value": "Google_Old+Standard+TT", "label": "Old Standard", "group": "Serif | Google Font"}, {"value": "Google_PT+Serif", "label": "PT Serif", "group": "Serif | Google Font"}, {"value": "Google_Vollkorn", "label": "<PERSON><PERSON><PERSON>", "group": "Serif | Google Font"}, {"value": "Google_Playfair+Display", "label": "Playfair Display", "group": "Serif | Google Font"}], "default": "Google_Montserrat"}, {"type": "select", "id": "page_title_font_size", "label": "Page title font size", "info": "This only applies to the big page title that appears at the top", "options": [{"value": "50px", "label": "Extra small"}, {"value": "60px", "label": "Small"}, {"value": "70px", "label": "Normal"}, {"value": "80px", "label": "Large"}, {"value": "100px", "label": "Extra large"}], "default": "80px"}, {"type": "header", "content": "Body text"}, {"type": "select", "id": "text_font", "label": "Font", "options": [{"value": "'Avant <PERSON>', <PERSON><PERSON><PERSON><PERSON>, 'Century Gothic', <PERSON><PERSON><PERSON><PERSON>, 'AppleGothic', sans-serif", "label": "Avant <PERSON>e", "group": "Sans-serif"}, {"value": "'<PERSON> Sans', 'Gill Sans MT', Calibri, sans-serif", "label": "<PERSON>", "group": "Sans-serif"}, {"value": "'HelveticaNeue', 'Helvetica Neue', Helvetica, Arial, sans-serif", "label": "Helvetica Neue", "group": "Sans-serif"}, {"value": "<PERSON><PERSON>, 'Helvetica Neue', Helvetica, sans-serif", "label": "<PERSON><PERSON>", "group": "Sans-serif"}, {"value": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif", "label": "Candara", "group": "Sans-serif"}, {"value": "Geneva, Tahoma, Verdana, sans-serif", "label": "Geneva", "group": "Sans-serif"}, {"value": "Google_Droid+Sans", "label": "Droid Sans", "group": "Sans-serif | Google Font"}, {"value": "Google_Lato", "label": "<PERSON><PERSON>", "group": "Sans-serif | Google Font"}, {"value": "Google_Montserrat", "label": "Montserrat", "group": "Sans-serif | Google Font"}, {"value": "Google_Open+Sans", "label": "Open Sans", "group": "Sans-serif | Google Font"}, {"value": "Google_PT+Sans", "label": "PT Sans", "group": "Sans-serif | Google Font"}, {"value": "Google_Roboto", "label": "Roboto", "group": "Sans-serif | Google Font"}, {"value": "Google_Source+Sans+Pro", "label": "Source Sans Pro", "group": "Sans-serif | Google Font"}, {"value": "Google_Ubuntu", "label": "Ubuntu", "group": "Sans-serif | Google Font"}, {"value": "Google_Raleway", "label": "<PERSON><PERSON><PERSON>", "group": "Sans-serif | Google Font"}, {"value": "Google_Kalam", "label": "Ka<PERSON>", "group": "Sans-serif | Google Font"}, {"value": "Google_Quicksand", "label": "Quicksand", "group": "Sans-serif | Google Font"}, {"value": "Google_Dosis", "label": "<PERSON><PERSON>", "group": "Sans-serif | Google Font"}, {"value": "Google_Maven+Pro", "label": "Maven <PERSON>", "group": "Sans-serif | Google Font"}, {"value": "Google_Josefin+Sans", "label": "<PERSON><PERSON>", "group": "Sans-serif | Google Font"}, {"value": "'Big Caslon', 'Book Antiqua', '<PERSON><PERSON><PERSON>', Georgia, serif", "label": "Big Caslon", "group": "<PERSON><PERSON>"}, {"value": "'Calisto MT', 'Bookman Old Style', <PERSON>man, 'Goudy Old Style', <PERSON><PERSON><PERSON>, 'Hoefler Text', 'Bitstream Charter', Georgia, serif", "label": "Calisto MT", "group": "<PERSON><PERSON>"}, {"value": "Baskerville, 'Baskerville Old Face', 'Hoefler Text', <PERSON><PERSON><PERSON>, 'Times New Roman', serif", "label": "Baskerville", "group": "<PERSON><PERSON>"}, {"value": "<PERSON><PERSON><PERSON>, Baskerville, 'Baskerville Old Face', 'Hoefler Text', 'Times New Roman', serif", "label": "<PERSON><PERSON><PERSON>", "group": "<PERSON><PERSON>"}, {"value": "TimesNewRoman, 'Times New Roman', Times, Baskerville, Georgia, serif", "label": "Times New Roman", "group": "<PERSON><PERSON>"}, {"value": "Google_Arapey", "label": "Ara<PERSON><PERSON>", "group": "Serif | Google Font"}, {"value": "Google_Arvo", "label": "Arvo", "group": "Serif | Google Font"}, {"value": "Google_Crimson+Text", "label": "Crimson Text", "group": "Serif | Google Font"}, {"value": "Google_Droid+Serif", "label": "Droid Serif", "group": "Serif | Google Font"}, {"value": "Google_Lora", "label": "<PERSON><PERSON>", "group": "Serif | Google Font"}, {"value": "Google_Old+Standard+TT", "label": "Old Standard", "group": "Serif | Google Font"}, {"value": "Google_PT+Serif", "label": "PT Serif", "group": "Serif | Google Font"}, {"value": "Google_Vollkorn", "label": "<PERSON><PERSON><PERSON>", "group": "Serif | Google Font"}, {"value": "Google_Playfair+Display", "label": "Playfair Display", "group": "Serif | Google Font"}], "default": "Google_Montserrat"}, {"type": "header", "content": "Slideshow (home page)"}, {"type": "select", "id": "slideshow_heading_font", "label": "Heading font", "options": [{"value": "'Avant <PERSON>', <PERSON><PERSON><PERSON><PERSON>, 'Century Gothic', <PERSON><PERSON><PERSON><PERSON>, 'AppleGothic', sans-serif", "label": "Avant <PERSON>e", "group": "Sans-serif"}, {"value": "'<PERSON> Sans', 'Gill Sans MT', Calibri, sans-serif", "label": "<PERSON>", "group": "Sans-serif"}, {"value": "'HelveticaNeue', 'Helvetica Neue', Helvetica, Arial, sans-serif", "label": "Helvetica Neue", "group": "Sans-serif"}, {"value": "<PERSON><PERSON>, 'Helvetica Neue', Helvetica, sans-serif", "label": "<PERSON><PERSON>", "group": "Sans-serif"}, {"value": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif", "label": "Candara", "group": "Sans-serif"}, {"value": "Geneva, Tahoma, Verdana, sans-serif", "label": "Geneva", "group": "Sans-serif"}, {"value": "Google_Droid+Sans", "label": "Droid Sans", "group": "Sans-serif | Google Font"}, {"value": "Google_Lato", "label": "<PERSON><PERSON>", "group": "Sans-serif | Google Font"}, {"value": "Google_Montserrat", "label": "Montserrat", "group": "Sans-serif | Google Font"}, {"value": "Google_Open+Sans", "label": "Open Sans", "group": "Sans-serif | Google Font"}, {"value": "Google_PT+Sans", "label": "PT Sans", "group": "Sans-serif | Google Font"}, {"value": "Google_Roboto", "label": "Roboto", "group": "Sans-serif | Google Font"}, {"value": "Google_Source+Sans+Pro", "label": "Source Sans Pro", "group": "Sans-serif | Google Font"}, {"value": "Google_Ubuntu", "label": "Ubuntu", "group": "Sans-serif | Google Font"}, {"value": "Google_Raleway", "label": "<PERSON><PERSON><PERSON>", "group": "Sans-serif | Google Font"}, {"value": "Google_Kalam", "label": "Ka<PERSON>", "group": "Sans-serif | Google Font"}, {"value": "Google_Quicksand", "label": "Quicksand", "group": "Sans-serif | Google Font"}, {"value": "Google_Dosis", "label": "<PERSON><PERSON>", "group": "Sans-serif | Google Font"}, {"value": "Google_Maven+Pro", "label": "Maven <PERSON>", "group": "Sans-serif | Google Font"}, {"value": "Google_Josefin+Sans", "label": "<PERSON><PERSON>", "group": "Sans-serif | Google Font"}, {"value": "'Big Caslon', 'Book Antiqua', '<PERSON><PERSON><PERSON>', Georgia, serif", "label": "Big Caslon", "group": "<PERSON><PERSON>"}, {"value": "'Calisto MT', 'Bookman Old Style', <PERSON>man, 'Goudy Old Style', <PERSON><PERSON><PERSON>, 'Hoefler Text', 'Bitstream Charter', Georgia, serif", "label": "Calisto MT", "group": "<PERSON><PERSON>"}, {"value": "Baskerville, 'Baskerville Old Face', 'Hoefler Text', <PERSON><PERSON><PERSON>, 'Times New Roman', serif", "label": "Baskerville", "group": "<PERSON><PERSON>"}, {"value": "<PERSON><PERSON><PERSON>, Baskerville, 'Baskerville Old Face', 'Hoefler Text', 'Times New Roman', serif", "label": "<PERSON><PERSON><PERSON>", "group": "<PERSON><PERSON>"}, {"value": "TimesNewRoman, 'Times New Roman', Times, Baskerville, Georgia, serif", "label": "Times New Roman", "group": "<PERSON><PERSON>"}, {"value": "Google_Arapey", "label": "Ara<PERSON><PERSON>", "group": "Serif | Google Font"}, {"value": "Google_Arvo", "label": "Arvo", "group": "Serif | Google Font"}, {"value": "Google_Crimson+Text", "label": "Crimson Text", "group": "Serif | Google Font"}, {"value": "Google_Droid+Serif", "label": "Droid Serif", "group": "Serif | Google Font"}, {"value": "Google_Lora", "label": "<PERSON><PERSON>", "group": "Serif | Google Font"}, {"value": "Google_Old+Standard+TT", "label": "Old Standard", "group": "Serif | Google Font"}, {"value": "Google_PT+Serif", "label": "PT Serif", "group": "Serif | Google Font"}, {"value": "Google_Vollkorn", "label": "<PERSON><PERSON><PERSON>", "group": "Serif | Google Font"}, {"value": "Google_Playfair+Display", "label": "Playfair Display", "group": "Serif | Google Font"}], "default": "Google_Montserrat"}, {"type": "select", "id": "slideshow_heading_font_size", "label": "Heading size", "options": [{"value": "30px", "label": "Extra small"}, {"value": "50px", "label": "Small"}, {"value": "60px", "label": "Normal"}, {"value": "70px", "label": "Large"}, {"value": "80px", "label": "Extra large"}], "default": "80px"}, {"type": "select", "id": "slideshow_subheading_font", "label": "Subheading font", "options": [{"value": "'Avant <PERSON>', <PERSON><PERSON><PERSON><PERSON>, 'Century Gothic', <PERSON><PERSON><PERSON><PERSON>, 'AppleGothic', sans-serif", "label": "Avant <PERSON>e", "group": "Sans-serif"}, {"value": "'<PERSON> Sans', 'Gill Sans MT', Calibri, sans-serif", "label": "<PERSON>", "group": "Sans-serif"}, {"value": "'HelveticaNeue', 'Helvetica Neue', Helvetica, Arial, sans-serif", "label": "Helvetica Neue", "group": "Sans-serif"}, {"value": "<PERSON><PERSON>, 'Helvetica Neue', Helvetica, sans-serif", "label": "<PERSON><PERSON>", "group": "Sans-serif"}, {"value": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif", "label": "Candara", "group": "Sans-serif"}, {"value": "Geneva, Tahoma, Verdana, sans-serif", "label": "Geneva", "group": "Sans-serif"}, {"value": "Google_Droid+Sans", "label": "Droid Sans", "group": "Sans-serif | Google Font"}, {"value": "Google_Lato", "label": "<PERSON><PERSON>", "group": "Sans-serif | Google Font"}, {"value": "Google_Montserrat", "label": "Montserrat", "group": "Sans-serif | Google Font"}, {"value": "Google_Open+Sans", "label": "Open Sans", "group": "Sans-serif | Google Font"}, {"value": "Google_PT+Sans", "label": "PT Sans", "group": "Sans-serif | Google Font"}, {"value": "Google_Roboto", "label": "Roboto", "group": "Sans-serif | Google Font"}, {"value": "Google_Source+Sans+Pro", "label": "Source Sans Pro", "group": "Sans-serif | Google Font"}, {"value": "Google_Ubuntu", "label": "Ubuntu", "group": "Sans-serif | Google Font"}, {"value": "Google_Raleway", "label": "<PERSON><PERSON><PERSON>", "group": "Sans-serif | Google Font"}, {"value": "Google_Kalam", "label": "Ka<PERSON>", "group": "Sans-serif | Google Font"}, {"value": "Google_Quicksand", "label": "Quicksand", "group": "Sans-serif | Google Font"}, {"value": "Google_Dosis", "label": "<PERSON><PERSON>", "group": "Sans-serif | Google Font"}, {"value": "Google_Maven+Pro", "label": "Maven <PERSON>", "group": "Sans-serif | Google Font"}, {"value": "Google_Josefin+Sans", "label": "<PERSON><PERSON>", "group": "Sans-serif | Google Font"}, {"value": "'Big Caslon', 'Book Antiqua', '<PERSON><PERSON><PERSON>', Georgia, serif", "label": "Big Caslon", "group": "<PERSON><PERSON>"}, {"value": "'Calisto MT', 'Bookman Old Style', <PERSON>man, 'Goudy Old Style', <PERSON><PERSON><PERSON>, 'Hoefler Text', 'Bitstream Charter', Georgia, serif", "label": "Calisto MT", "group": "<PERSON><PERSON>"}, {"value": "Baskerville, 'Baskerville Old Face', 'Hoefler Text', <PERSON><PERSON><PERSON>, 'Times New Roman', serif", "label": "Baskerville", "group": "<PERSON><PERSON>"}, {"value": "<PERSON><PERSON><PERSON>, Baskerville, 'Baskerville Old Face', 'Hoefler Text', 'Times New Roman', serif", "label": "<PERSON><PERSON><PERSON>", "group": "<PERSON><PERSON>"}, {"value": "TimesNewRoman, 'Times New Roman', Times, Baskerville, Georgia, serif", "label": "Times New Roman", "group": "<PERSON><PERSON>"}, {"value": "Google_Arapey", "label": "Ara<PERSON><PERSON>", "group": "Serif | Google Font"}, {"value": "Google_Arvo", "label": "Arvo", "group": "Serif | Google Font"}, {"value": "Google_Crimson+Text", "label": "Crimson Text", "group": "Serif | Google Font"}, {"value": "Google_Droid+Serif", "label": "Droid Serif", "group": "Serif | Google Font"}, {"value": "Google_Lora", "label": "<PERSON><PERSON>", "group": "Serif | Google Font"}, {"value": "Google_Old+Standard+TT", "label": "Old Standard", "group": "Serif | Google Font"}, {"value": "Google_PT+Serif", "label": "PT Serif", "group": "Serif | Google Font"}, {"value": "Google_Vollkorn", "label": "<PERSON><PERSON><PERSON>", "group": "Serif | Google Font"}, {"value": "Google_Playfair+Display", "label": "Playfair Display", "group": "Serif | Google Font"}], "default": "Google_Montserrat"}, {"type": "select", "id": "slideshow_subheading_font_size", "label": "Subheading size", "options": [{"value": "14px", "label": "Extra small"}, {"value": "16px", "label": "Small"}, {"value": "20px", "label": "Normal"}, {"value": "24px", "label": "Large"}, {"value": "30px", "label": "Extra large"}], "default": "16px"}]}, {"name": "Navigation", "settings": [{"type": "link_list", "id": "navigation_menu", "label": "<PERSON><PERSON>", "default": "main-menu"}, {"type": "select", "id": "navigation_mode", "label": "Navigation mode", "info": "Horizontal navigation will only display on desktop.", "options": [{"value": "horizontal", "label": "Horizontal"}, {"value": "sidebar", "label": "Sidebar"}], "default": "sidebar"}, {"type": "header", "content": "Sidebar navigation", "info": "Those settings only apply when the navigation is set to sidebar"}, {"type": "image_picker", "id": "navigation_sidebar_image", "label": "Default image", "info": "650 x 300px .jpg recommended"}, {"type": "header", "content": "Horizontal navigation", "info": "Those settings only apply when the navigation is set to horizontal"}, {"type": "text", "id": "navigation_horizontal_mega_nav", "label": "Mega-menu", "info": "Enter menu item to apply a mega menu dropdown"}, {"type": "image_picker", "id": "navigation_horizontal_image", "label": "Default image for mega-navigation", "info": "500 x 500px .jpg recommended"}, {"type": "checkbox", "id": "navigation_horizontal_switch_image", "label": "Switch image on hover in mega-navigation", "info": "This only apply if the link is a product or a collection", "default": false}]}, {"name": "Search", "settings": [{"type": "select", "id": "search_mode", "label": "Search only...", "options": [{"value": "product", "label": "Products only"}, {"value": "product,page", "label": "Products and pages"}, {"value": "product,article", "label": "Products and articles"}, {"value": "product,article,page", "label": "Products, articles and pages"}], "default": "product"}]}, {"name": "Currency conversion", "settings": [{"type": "checkbox", "id": "currency_conversion_enabled", "label": "Enable currency conversion", "default": false}, {"type": "text", "id": "currency_conversion_supported_currencies", "label": "Supported currencies", "info": "List of currencies to display. You must separate each currencies by an empty space, and use the ISO 4217 3-letter code. Example: USD CAD EUR. All the possible values are [listed there](http://bit.ly/1C4cZ3w)", "default": "USD CAD EUR GBP"}, {"type": "text", "id": "currency_conversion_default_currency", "label": "Default currency", "default": "USD"}]}, {"name": "Collection", "settings": [{"type": "select", "id": "mobile_grid_items_per_row", "label": "Items per row (mobile)", "options": [{"value": "1", "label": "1"}, {"value": "2", "label": "2"}], "default": "2"}, {"type": "select", "id": "tablet_grid_items_per_row", "label": "Items per row (tablet)", "options": [{"value": "2", "label": "2"}, {"value": "3", "label": "3"}, {"value": "4", "label": "4"}], "default": "3"}]}, {"name": "Cart page", "settings": [{"type": "select", "id": "cart_page", "label": "Cart page", "options": [{"value": "page", "label": "Page"}, {"value": "drawer", "label": "Drawer"}, {"value": "message", "label": "Message"}], "default": "drawer"}]}, {"name": "Social media", "settings": [{"type": "header", "content": "Accounts"}, {"type": "text", "id": "social_facebook", "info": "https://www.facebook.com/shopify", "label": "Facebook"}, {"type": "text", "id": "social_twitter", "info": "https://twitter.com/shopify", "label": "Twitter"}, {"type": "text", "id": "social_google_plus", "info": "https://plus.google.com/+shopify", "label": "Google Plus"}, {"type": "text", "id": "social_pinterest", "info": "https://www.pinterest.com/shopify", "label": "Pinterest"}, {"type": "text", "id": "social_instagram", "info": "https://instagram.com/shopify", "label": "Instagram"}, {"type": "text", "id": "social_vimeo", "info": "https://vimeo.com/shopify", "label": "Vimeo"}, {"type": "text", "id": "social_tumblr", "info": "http://shopify.tumblr.com", "label": "Tumblr"}, {"type": "text", "id": "social_youtube", "info": "https://www.youtube.com/user/shopify", "label": "YouTube"}, {"type": "text", "id": "social_fancy", "info": "http://fancy.com/Shopify", "label": "Fancy"}]}, {"name": "Favicon", "settings": [{"type": "image_picker", "id": "favicon", "label": "Image", "info": "32 x 32px .png recommended"}]}]