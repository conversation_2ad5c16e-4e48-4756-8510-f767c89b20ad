{"general": {"meta": {"tags": "Tagged \"{{ tags }}\"", "page": "Page {{ page }}"}, "breadcrumb": {"home": "Home", "account": "Account"}, "pagination": {"previous_page": "Previous", "next_page": "Next", "page_count": "Page {{current_page}} of {{total_pages}}"}, "social": {"share": "Share", "tags": "Tags"}, "forms": {"errors": "There are some errors"}, "404": {"title": "404", "subtitle": "The page you're looking for could not be found.", "go_back": "Go back to homepage"}}, "layout": {"navigation": {"search_placeholder": "Search..."}, "newsletter": {"title_success": "Thank you!", "subtitle_success": "We will let you know once we have exciting news to share!", "input_placeholder": "Your email...", "submit": "Subscribe"}, "promotions": {"email_placeholder": "Your email", "newsletter_success": "Thank you for subscribing!", "submit": "Join us"}, "footer": {"newsletter_placeholder": "Email address", "newsletter_submit": "Submit", "newsletter_success": "Thank you for subscribing!", "copyright_html": " "}}, "home_page": {"onboarding": {"brand_title": "<PERSON>'s name", "product_title": "Your product's name", "collection_title": "Your collection's name", "article_name": "Your post's name", "blog_author": "Author name", "blog_excerpt": "Your store hasn’t published any blog posts yet. A blog can be used to talk about new product launches, tips, or other news you want to share with your customers. You can check out Shopify’s ecommerce blog for inspiration and advice for your own store and blog.", "instagram_date": "Example date", "tweet_date": "Example date", "tweet_content": "This is an example of a short tweet"}}, "blog": {"general": {"rss": "Subscribe to our RSS feed"}, "article": {"read_more": "Read More"}, "comments": {"write": "Write a comment", "success": "Your comment has been published", "success_moderated": "Your comment is waiting to be approved", "name": "Name", "email": "Email", "comment": "Comment", "approval": "Comments are moderated", "submit": "Post my comment", "previous_comments": "Previous", "next_comments": "Next", "comments_count": {"one": "{{ count }} comment", "other": "{{ count }} comments"}}}, "collections": {"general": {"title": "All collections"}}, "collection": {"general": {"empty": "This collection does not contain any products", "lets_shop": "Let's shop"}, "product": {"from_price_html": "From <span data-money-convertible>{{ price }}</span>"}, "filters": {"filter": "Filter", "all_products": "All products", "no_type_selected": "All types", "no_vendor_selected": "All vendors"}, "sorting": {"title": "Sort By", "featured": "Featured", "best_selling": "Best Selling", "az": "Alphabetically: A-Z", "za": "Alphabetically: Z-A", "price_ascending": "Price: Low to High", "price_descending": "Price: High to Low", "date_descending": "Newest to Oldest", "date_ascending": "Oldest to Newest"}}, "product": {"general": {"view_details": "View Details", "loading_reviews": "Loading reviews...", "add_to_cart": "Add to cart", "adding_to_cart": "Adding...", "added_to_cart": "Added!", "quantity": "Qty", "related_products": "Related Products", "sale_price": "Sale price", "regular_price": "Regular price"}, "form": {"variant_label": "<PERSON><PERSON><PERSON>"}, "labels": {"sold_out": "Sold out", "on_sale": "On sale", "unavailable": "Unavailable", "quick_shop": "Quick Buy"}, "tabs": {"gallery": "Gallery", "description": "Description", "reviews": "Reviews"}, "reviews": {"close": "Close"}}, "search": {"general": {"title": "Search", "input_placeholder": "Search our store", "notice": "Your search did not yield any results. You can try again !", "page": "Page", "results_count": {"zero": "No results for {{ terms }}", "one": "{{ count }} result for {{ terms }}", "other": "{{ count }} results for {{ terms }}"}}, "autocomplete": {"input_placeholder": "Search...", "no_results": "No results", "see_all": "See all results"}}, "customer": {"login": {"title": "<PERSON><PERSON>", "email": "Email", "password": "Password", "submit": "<PERSON><PERSON>", "forgot": "Forgot it?", "not_member": "Not a member yet?", "register": "Register now"}, "recover": {"title": "Recover your password", "email": "Email", "submit": "Send instructions", "sent": "Instructions have been sent to your email"}, "reset_password": {"title": "Reset password", "password": "Password", "password_confirmation": "Password confirmation", "submit": "Reset my password"}, "activate_account": {"title": "Activate account", "password": "Password", "password_confirmation": "Password confirmation", "cancel": "Decline", "submit": "Activate my account"}, "register": {"title": "Create account", "first_name": "First name", "last_name": "Last name", "email": "Email", "password": "Password", "submit": "Create my account", "already_member": "Already have an account?", "login": "Login now"}, "account": {"title": "My Account", "logout": "Logout", "manage_addresses": "Manage Addresses"}, "orders": {"order_number": "Order", "date": "Date", "payment_status": "Payment Status", "fulfillment_status": "Fulfillment Status", "total": "Total", "none": "You haven't placed any orders yet.", "next": "Newer orders", "previous": "Older orders"}, "order": {"title": "Order {{number}}", "details": "Order details", "date": "Order placed on {{ date }}", "payment_status": "Payment Status", "fulfillment_status": "Fulfillment Status", "cancelled": "Order Cancelled on {{ date }}", "discount": "Discount", "shipping": "Shipping", "tax": "Tax", "product": "Product", "sku": "SKU", "price": "Price", "quantity": "Quantity", "total": "Total", "fulfilled_at": "Fulfilled {{ date }}", "subtotal": "Subtotal", "shipping_address": "Shipping address", "billing_address": "Billing address"}, "addresses": {"title": "Addresses", "empty": "You do not have any addresses saved yet.", "default": "Default address", "add_new": "Add a new address", "edit_address": "Edit address", "first_name": "First name", "last_name": "Last name", "company": "Company", "address1": "Address1", "address2": "Address2", "city": "City", "country": "Country", "province": "Province", "zip": "Postal/Zip Code", "phone": "Phone", "set_default": "Set as <PERSON><PERSON><PERSON> Ad<PERSON>?", "add": "Add", "update": "Update", "cancel": "Cancel", "edit": "Edit", "delete": "Delete"}}, "cart": {"general": {"title": "<PERSON><PERSON>", "empty_title": "Empty cart", "empty_subtitle": "It feels lonely...", "empty_button": "Let's shop!", "add_note": "Special instructions", "checkout": "Checkout", "checkout_with": "Also checkout with", "shipping_not_included": "Shipping not included", "estimated_shipping": "+ Estimated shipping:", "currency_notice": "All orders are processed in shop's currency, which is {{shop_currency}}. The content of your cart may be displayed in another currency, at the most current exchange rate."}, "items": {"product": "Product", "item": "<PERSON><PERSON>", "price": "Price", "quantity": "Quantity", "remove": "Remove", "subtotal": "Subtotal", "total": "Total"}, "shipping_estimator": {"title": "Estimate Shipping", "country": "Country", "province": "Province/State", "zip": "Zip Code", "submit": "Estimate", "submitting": "Estimating...", "error": "There are some errors:", "rates": "Available shipping rates:", "no_rates": "We do not ship to this destination"}, "mini_cart": {"empty_title": "Your cart is empty", "empty_subtitle": "It feels desperately alone", "empty_button": "Let's shop", "product_added": "Your product has been added!", "continue_to_checkout": "Continue to checkout"}}, "gift_cards": {"general": {"title": "A gift card is waiting for you"}, "issued": {"title_html": "Here's your {{ value }} gift card for {{ shop }}!", "issued_text": "Here's your gift card!", "left_balance": "left", "disabled": "Disabled", "expired": "Expired on {{ expiry }}", "expires_on": "Expires on {{ expiry }}", "redeem": "Use this code at checkout to redeem your gift card", "shop_link": "Start shopping now", "print": "Print"}, "alt": {"illustration": "Gift card illustration", "add_to_apple_wallet": "Add to Apple Wallet"}}, "contact": {"form": {"thank_you": "Your message has been sent! We'll get back to you as soon as possible.", "email": "Email", "name": "Name", "message": "Message", "submit": "Send my message"}}, "password": {"general": {"title": "Opening soon!", "admin_link_html": "Are you the store owner? <a href=\"/admin\" class=\"link link--primary\">Click here to login</a> or <a href=\"#\" class=\"link link--primary\" data-action=\"storefront-password\">enter the store password</a>.", "powered_by": "This store will be powered by"}, "form": {"enter_password": "Enter store using password:", "submit": "Enter"}, "newsletter": {"thank_you": "Thank you for signing up!", "email": "Email", "find_out": "Find out when we open"}}, "date_formats": {"month_day_year": "%B %d, %Y", "month_day_year_time": "%B %d, %Y at %I:%M%p"}, "shopify": {"links": {"powered_by_shopify": "  Copyright © 2018 Artemide S.p.A."}, "checkout": {"newsletter": {"subscription_checkbox_label": "Subscribe to our newsletter.\nBy choosing \"Continue to shipping method\" below, you accept the terms of our Privacy Policy."}, "marketing": {"accept_marketing_checkbox_label": "Subscribe to our newsletter. \nBy choosing \"Continue to shipping method\" below, you accept the terms of our Privacy Policy."}}}}