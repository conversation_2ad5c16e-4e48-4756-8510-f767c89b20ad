package com.danesemilano.persistence.entity;

import java.util.Locale;

import org.springframework.context.MessageSource;

import net.yadaframework.core.YadaLocalEnum;
import net.yadaframework.persistence.entity.YadaPersistentEnum;

public enum EnumTipology implements YadaLocalEnum<EnumTipology> {
	WALL,
	CEILING,
	TABLE,
	FLOOR,
	SUSPENSION;
	
	private static String MESSAGES_PREFIX = "enum.tipology.";
	
	private YadaPersistentEnum<EnumTipology> yadaPersistentEnum;
	
	public YadaPersistentEnum<EnumTipology> toYadaPersistentEnum() {
		return yadaPersistentEnum;
	}
	
	// TODO fix generics
	public void setYadaPersistentEnum(YadaPersistentEnum yadaPersistentEnum) {
		this.yadaPersistentEnum = yadaPersistentEnum;
	}
	
	/**
	 * Return the localized text for this enum
	 * @param messageSource
	 * @param locale
	 * @return
	 */
	public String toString(MessageSource messageSource, Locale locale) {
		return messageSource.getMessage(MESSAGES_PREFIX + name().toLowerCase(), null, locale);
	}

}
