<config>
	<info>
		<env>col</env>
		<appName>Dane<PERSON>Commerce</appName>
		<version>1.0 COL</version>
		<build>${build}</build>
		<date>@@RELEASEDATE_PLACEHOLDER@@</date> <!-- Injected by gradle -->
	</info>
	<paths>
		<serverAddress>https://col.danesemilano.com/</serverAddress>
		<basePath>/srv/dnccol</basePath>
	</paths>
	<email>
		<enabled>false</enabled>
		<from><EMAIL></from>
		<support>
			<!-- Addresses to which a support request is sent -->
			<to><EMAIL></to>
		</support>
		<logoImage>template.email/logo50.png</logoImage>
		<smtpserver>
			<host>smtp.EXAMPLE.com</host>
			<port>587</port>
			<protocol>smtp</protocol>
			<username><EMAIL></username>
			<password>EXAMPLE</password>
			<!--  If set to true, and a message has some valid and some invalid addresses, send the message anyway, reporting the partial failure with a SendFailedException. If set to false (the default), the message is not sent to any of the recipients if there is an invalid recipient address.  -->
			<properties>mail.smtp.sendpartial=true</properties> 
			<properties>mail.smtp.auth=true</properties>
			<properties>mail.smtp.starttls.enable=true</properties>
			<properties>mail.smtp.quitwait=false</properties>
		</smtpserver>
		<!-- Remove this list to enable email to everyone -->
		<validEmail><EMAIL></validEmail>
		<validEmail><EMAIL></validEmail>
		<validEmail><EMAIL></validEmail>
	</email>

	<database>
		<jndiname>java:comp/env/jdbc/dncdbcol</jndiname>
		<showSql>false</showSql>
		<entityPackage>com.danesemilano.persistence.entity</entityPackage>
	</database>


</config>