<header class="page__header">
  {% include 'breadcrumb' %}

  <div class="page__header-wrapper">
    <div class="container">
      <h1 class="page__title">{{ 'customer.addresses.title' | t }}</h1>

      {% if customer.addresses.size > 0 %}
        <div class="page__action">
          <a href="#" data-action="open-new-address-modal" class="button button--primary">{{ 'customer.addresses.add_new' | t }}</a>
        </div>
      {% else %}
        <div class="page__description page__description--centered">
          <p class="rte">{{ 'customer.addresses.empty' | t }}</p>

          <div class="page__header-actions">
            <a href="#" data-action="open-new-address-modal" class="button button--primary">{{ 'customer.addresses.add_new' | t }}</a>
          </div>
        </div>
      {% endif %}
    </div>
  </div>
</header>

{% if customer.addresses.size > 0 %}
  {% paginate customer.addresses by 6 %}
    <div class="page__content">
      <div class="inner">
        <div class="account__addresses grid grid--gallery grid--middle">
          {% for address in customer.addresses %}
            <div class="grid__cell 1/2--handheld 1/3--lap-and-up">
              <section class="account__address account__address--main">
                {{ address | format_address }}

                <div class="address__actions button-group">
                  <a href="#" data-action="open-edit-address-modal" data-address="{{ address.id }}" class="button button--primary">
                    {{ 'customer.addresses.edit' | t }}
                  </a>

                  <a href="#" onclick="Shopify.CustomerAddress.destroy({{ address.id }}); return false;" class="button button--secondary">
                    {{ 'customer.addresses.delete' | t }}
                  </a>
                </div>
              </section>
            </div>
          {% endfor %}
        </div>
      </div>
    </div>

    {% include 'pagination' %}
  {% endpaginate %}
{% endif %}

<div class="addresses__new addresses__modal modal remodal">
  <h4 class="modal__title">{{ 'customer.addresses.add_new' | t }}</h4>

  <div class="modal__body">
    {% form 'customer_address', customer.new_address %}
      <div class="form__control">
        <label class="form__label" for="address_first_name_{{form.id}}">{{ 'customer.addresses.first_name' | t }}</label>
        <input type="text" id="address_first_name_{{form.id}}" name="address[first_name]" value="{{ form.first_name }}">
      </div>

      <div class="form__control">
        <label class="form__label" for="address_last_name_{{form.id}}">{{ 'customer.addresses.last_name' | t }}</label>
        <input type="text" id="address_last_name_{{form.id}}" name="address[last_name]" value="{{ form.last_name }}">
      </div>

      <div class="form__control">
        <label class="form__label" for="address_company_{{form.id}}">{{ 'customer.addresses.company' | t }}</label>
        <input type="text" id="address_company_{{form.id}}" name="address[company]" value="{{ form.company }}">
      </div>

      <div class="form__control">
        <label class="form__label" for="address_address1_{{form.id}}">{{ 'customer.addresses.address1' | t }}</label>
        <input type="text" id="address_address1_{{form.id}}" name="address[address1]" value="{{ form.address1 }}">
      </div>

      <div class="form__control">
        <label class="form__label" for="address_address2_{{form.id}}">{{ 'customer.addresses.address2' | t }}</label>
        <input type="text" id="address_address2_{{form.id}}" name="address[address2]" value="{{ form.address2 }}">
      </div>

      <div class="form__control">
        <label class="form__label" for="address_city_{{form.id}}">{{ 'customer.addresses.city' | t }}</label>
        <input type="text" id="address_city_{{form.id}}" name="address[city]" value="{{ form.city }}">
      </div>

      <div class="form__control">
        <label class="form__label" for="address_country_new">{{ 'customer.addresses.country' | t }}</label>

        <div class="styled-select">
          {% include 'icon' with 'dropdown-arrow' %}
          <select id="address_country_new" name="address[country]" data-default="{{form.country}}">{{ country_option_tags }}</select>
        </div>
      </div>

      <div id="address-province-container-new" class="form__control address-province-container" style="display: none">
        <label class="form__label" for="address_province_new">{{ 'customer.addresses.province' | t }}</label>

        <div class="styled-select">
          {% include 'icon' with 'dropdown-arrow' %}
          <select id="address_province_new" class="address_form" name="address[province]" data-default="{{form.province}}"></select>
        </div>
      </div>

      <div class="form__control">
        <label class="form__label" for="address_zip_{{form.id}}">{{ 'customer.addresses.zip' | t }}</label>
        <input type="text" id="address_zip_{{form.id}}" name="address[zip]" value="{{ form.zip }}">
      </div>

      <div class="form__control">
        <label class="form__label" for="address_phone_{{form.id}}">{{ 'customer.addresses.phone' | t }}</label>
        <input type="text" id="address_phone_{{form.id}}" name="address[phone]" value="{{ form.phone }}">
      </div>

      <div class="form__control">
        <label class="form__label">
          {{ form.set_as_default_checkbox }} {{ 'customer.addresses.set_default' | t }}
        </label>
      </div>

      <div class="button-group button-wrapper">
        <div class="button-group__item">
          <button class="button button--secondary" data-remodal-action="close">{{ 'customer.addresses.cancel' | t }}</button>
        </div>

        <div class="button-group__item">
          <button class="button button--primary" type="submit">{{ 'customer.addresses.add' | t }}</button>
        </div>
      </div>
    {% endform %}
  </div>
</div>

{% for address in customer.addresses %}
  <div class="addresses__edit addresses__modal modal remodal" data-address="{{ address.id }}">
    <h4 class="modal__title">{{ 'customer.addresses.edit_address' | t }}</h4>

    <div class="modal__body">
      {% form 'customer_address', address %}
        <div class="form__control">
          <label class="form__label" for="address_first_name_{{form.id}}">{{ 'customer.addresses.first_name' | t }}</label>
          <input type="text" id="address_first_name_{{form.id}}" name="address[first_name]" value="{{ form.first_name }}">
        </div>

        <div class="form__control">
          <label class="form__label" for="address_last_name_{{form.id}}">{{ 'customer.addresses.last_name' | t }}</label>
          <input type="text" id="address_last_name_{{form.id}}" name="address[last_name]" value="{{ form.last_name }}">
        </div>

        <div class="form__control">
          <label class="form__label" for="address_company_{{form.id}}">{{ 'customer.addresses.company' | t }}</label>
          <input type="text" id="address_company_{{form.id}}" name="address[company]" value="{{ form.company }}">
        </div>

        <div class="form__control">
          <label class="form__label" for="address_address1_{{form.id}}">{{ 'customer.addresses.address1' | t }}</label>
          <input type="text" id="address_address1_{{form.id}}" name="address[address1]" value="{{ form.address1 }}">
        </div>

        <div class="form__control">
          <label class="form__label" for="address_address2_{{form.id}}">{{ 'customer.addresses.address2' | t }}</label>
          <input type="text" id="address_address2_{{form.id}}" name="address[address2]" value="{{ form.address2 }}">
        </div>

        <div class="form__control">
          <label class="form__label" for="address_city_{{form.id}}">{{ 'customer.addresses.city' | t }}</label>
          <input type="text" id="address_city_{{form.id}}" name="address[city]" value="{{ form.city }}">
        </div>

        <div class="form__control">
          <label class="form__label" for="address_country_{{ form.id }}">{{ 'customer.addresses.country' | t }}</label>

          <div class="styled-select">
            {% include 'icon' with 'dropdown-arrow' %}
            <select id="address_country_{{ form.id }}" name="address[country]" data-default="{{form.country}}">{{ country_option_tags }}</select>
          </div>
        </div>

        <div id="address-province-container-{{ form.id }}" class="form__control address-province-container" style="display: none">
          <label class="form__label" for="address_province_{{ form.id }}">{{ 'customer.addresses.province' | t }}</label>

          <div class="styled-select">
            {% include 'icon' with 'dropdown-arrow' %}
            <select id="address_province_{{ form.id }}" class="address_form" name="address[province]" data-default="{{form.province}}"></select>
          </div>
        </div>

        <div class="form__control">
          <label class="form__label" for="address_zip_{{form.id}}">{{ 'customer.addresses.zip' | t }}</label>
          <input type="text" id="address_zip_{{form.id}}" name="address[zip]" value="{{ form.zip }}">
        </div>

        <div class="form__control">
          <label class="form__label" for="address_phone_{{form.id}}">{{ 'customer.addresses.phone' | t }}</label>
          <input type="text" id="address_phone_{{form.id}}" name="address[phone]" value="{{ form.phone }}">
        </div>

        <div class="form__control">
          <label class="form__label">
            {{ form.set_as_default_checkbox }} {{ 'customer.addresses.set_default' | t }}
          </label>
        </div>

        <div class="button-group button-wrapper">
          <div class="button-group__item">
            <button class="button button--secondary" data-remodal-action="close">{{ 'customer.addresses.cancel' | t }}</button>
          </div>

          <div class="button-group__item">
            <button class="button button--primary" type="submit">{{ 'customer.addresses.update' | t }}</button>
          </div>
        </div>
      {% endform %}
    </div>
  </div>
{% endfor %}

<script>
  document.addEventListener('DOMContentLoaded', function() {
    new Shopify.CountryProvinceSelector('address_country_new', 'address_province_new', {hideElement: 'address-province-container-new'});

    {% for address in customer.addresses %}
      new Shopify.CountryProvinceSelector('address_country_{{address.id}}', 'address_province_{{address.id}}', {hideElement: 'address-province-container-{{address.id}}'});
    {% endfor %}
  });
</script>