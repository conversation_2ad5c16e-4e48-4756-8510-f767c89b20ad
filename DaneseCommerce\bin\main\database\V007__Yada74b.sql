# Modifiche necessarie per passare dal branch yada 0.4.1 al branch 0.7.4

ALTER TABLE EventNews MODIFY publishDate DATETIME(6);
ALTER TABLE YadaArticle ADD length float AFTER height, ADD radius float AFTER length, ADD internalName varchar(255) AFTER width, ADD sku varchar(32) AFTER published;
ALTER TABLE YadaArticle_name MODIFY name varchar(256);
ALTER TABLE YadaAttachedFile MODIFY uploadTimestamp TIMESTAMP NULL;
ALTER TABLE YadaAutoLoginToken MODIFY expiration TIMESTAMP NULL, MODIFY timestamp TIMESTAMP NULL;
ALTER TABLE YadaRegistrationRequest MODIFY timestamp TIMESTAMP NULL;
ALTER TABLE YadaUserCredentials MODIFY creationDate TIMESTAMP NULL, MODIFY lastFailedAttempt TIMESTAMP NULL, MODIFY lastSuccessfulLogin TIMESTAMP NULL, MODIFY passwordDate TIMESTAMP NULL;
ALTER TABLE YadaUserProfile ADD avatar_id bigint AFTER version;
alter table YadaArticle add constraint UK_nhcjr5g5me98n5drrkfg1p7rc unique (sku);
alter table YadaUserProfile add constraint FKpi28ogwa7vguwb3vv1tkmpovi foreign key (avatar_id) references YadaAttachedFile (id);
