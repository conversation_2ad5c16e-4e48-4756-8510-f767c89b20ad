package com.danesemilano.persistence.entity;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import jakarta.persistence.CollectionTable;
import jakarta.persistence.Column;
import jakarta.persistence.ElementCollection;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.MapKeyColumn;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.persistence.Transient;
import jakarta.persistence.UniqueConstraint;
import jakarta.persistence.Version;

import org.springframework.context.i18n.LocaleContextHolder;

import com.fasterxml.jackson.annotation.JsonProperty;

import net.yadaframework.core.CloneableFiltered;

@Entity
public class Tag implements CloneableFiltered, Serializable, Comparable<Tag> {
	private static final long serialVersionUID = 1L;
	// For synchronization with external databases
	@Column(columnDefinition="DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")
	@Temporal(TemporalType.TIMESTAMP)
	protected Date modified;
	// For optimistic locking
	@Version
	protected long version;
	@Id
	@GeneratedValue(strategy= GenerationType.IDENTITY)
	private Long id;
	
	@ManyToMany
	@JoinTable(
		// Un tag non può essere associato a un prodotto più di una volta
		uniqueConstraints = @UniqueConstraint(columnNames={"tags_id", "products_id"})
	)
	private List<Product> products;
	
	@ManyToMany
	@JoinTable(
		// Un tag non può essere associato a una news più di una volta
		uniqueConstraints = @UniqueConstraint(columnNames={"tags_id", "eventNews_id"})
	)
	private List<EventNews> eventNews;
	
	@ElementCollection(fetch= FetchType.EAGER)
	@Column(length=255)
	@MapKeyColumn(name="locale", length=32)
	@CollectionTable(
		uniqueConstraints= @UniqueConstraint(columnNames={"name","locale"})
	)
	private Map<Locale, String> name = new HashMap<>();
	
	@Transient
	@JsonProperty("DT_RowId")
	public String getDT_RowId() {
		return this.getClass().getSimpleName()+"#"+this.id;
	}
	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	
	public Map<Locale, String> getName() {
		return name;
	}

	public void setName(Map<Locale, String> name) {
		this.name = name;
	}
	
	/**
	 * Returns the localized name in the current request locale
	 * 
	 * @return
	 */
	public String getLocalName() {
		return name.get(LocaleContextHolder.getLocale());
	}
	
	@Override
	public Field[] getExcludedFields() {
		// TODO Auto-generated method stub
		return null;
	}


	public List<EventNews> getEventNews() {
		return eventNews;
	}

	public void setEventNews(List<EventNews> eventNews) {
		this.eventNews = eventNews;
	}
	
	@Override
	public int hashCode() {
		if (id!=null) {
			return id.hashCode();
		}
		return super.hashCode();
	}
	
	@Override
	public boolean equals(Object obj) {
		if (obj==null) {
			return false;
		}
		if (obj instanceof Tag && this.id!=null) {
			return this.id.equals(((Tag)obj).getId());
		}
		return super.equals(obj);
	}

	public List<Product> getProducts() {
		return products;
	}

	public void setProducts(List<Product> products) {
		this.products = products;
	}

	@Override
	public int compareTo(Tag obj) {
	    if (obj == null) return 1;  

	    String myValue = getLocalName();
	    String objValue = obj.getLocalName();

	    if (myValue == null && objValue == null) return 0; 
	    if (myValue == null) return -1;  
	    if (objValue == null) return 1; 

	    return myValue.compareTo(objValue);
	}

	@Override
	public String toString() {
		return getLocalName();
	}

}
