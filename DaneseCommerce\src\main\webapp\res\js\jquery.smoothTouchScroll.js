
;
(function ($, window, document, undefined) {

    $.widget( "thomaskahn.smoothTouchScroll" , {

        // Default options
        options: {
			scrollableAreaClass: "scrollableArea", // String
			scrollWrapperClass: "scrollWrapper", // String
			continuousScrolling: true, // Boolean
			startAtElementId: "" // String
        },

        // Setup widget
        _create: function () {
			var self = this, o = this.options, el = this.element;
			
			// Create variables for any existing or not existing 
			// scroller elements on the page.
			el.data("scrollWrapper", el.find("." + o.scrollWrapperClass));
			el.data("scrollableArea", el.find("." + o.scrollableAreaClass));
			
			// Check what the DOM looks like
	
			// Both the scrollable area and the wrapper are missing
			if (el.data("scrollableArea").length === 0 && el.data("scrollWrapper").length === 0) {
				el.wrapInner("<div class='" + o.scrollableAreaClass + "'>").wrapInner("<div class='" + o.scrollWrapperClass + "'>");

				el.data("scrollWrapper", el.find("." + o.scrollWrapperClass));
				el.data("scrollableArea", el.find("." + o.scrollableAreaClass));
			}
			
			// Only the wrapper is missing
			else if (el.data("scrollWrapper").length === 0) {
				el.wrapInner("<div class='" + o.scrollWrapperClass + "'>");
				el.data("scrollWrapper", el.find("." + o.scrollWrapperClass));
			}
			
			// Only the scrollable area is missing
			else if (el.data("scrollableArea").length === 0) {
				el.data("scrollWrapper").wrapInner("<div class='" + o.scrollableAreaClass + "'>");
				el.data("scrollableArea", el.find("." + o.scrollableAreaClass));
			}
			
			// Create variables in the element data storage.
			el.data("scrollXPos", 0);
			el.data("scrollableAreaWidth", 0);
			el.data("startingPosition", 0);
			el.data("rightScrollingInterval", null);
			el.data("leftScrollingInterval", null);
			el.data("previousScrollLeft", 0);
			el.data("getNextElementWidth", true);
			el.data("swapAt", null);
			el.data("startAtElementHasNotPassed", true);
			el.data("swappedElement", null);
			el.data("originalElements", el.data("scrollableArea").children());
			el.data("visible", true);
			el.data("enabled", true);
			el.data("scrollableAreaHeight", el.data("scrollableArea").height());
			
			/*****************************************
			SET UP EVENTS FOR TOUCH SCROLLING
			*****************************************/
			if (el.data("enabled")) {
				el.data("scrollWrapper").kinetic({
					cursor: 'move',
					decelerate: true,
					triggerHardware: false,
					y: false,
					x: true,
					axisTolerance: 7,
					slowdown: 0.9,
					maxvelocity: 120,
					throttleFPS: 60,
					moved: function (settings) {
//						if (o.continuousScrolling) {
//							if (el.data("scrollWrapper").scrollLeft() <= 0) {
//								self._checkContinuousSwapLeft();
//							} else {
//								self._checkContinuousSwapRight();
//							}
//						}
//						console.log(settings);
                        // Callback
						self._trigger("touchMoved");
					},
					stopped: function (settings) {
						// Callback
						self._trigger("touchStopped");
					}
				});
			}
			
			/*****************************************
			SET UP EVENT FOR RESIZING THE BROWSER WINDOW
			*****************************************/
			$(window).bind("resize", function () {
				self._trigger("windowResized");
			});
			
        },
		/**********************************************************
		_init 
		**********************************************************/
		// This code is run every time the widget is called without arguments.
		// Use it when you add elements to the scroller without reloading the page.
		 _init: function () {
			var self = this, el = this.element;
		 
			// Recalculate the total width of the elements inside the scrollable area
			self.recalculateScrollableArea();
		
			// Trigger callback
			self._trigger("initializationComplete");
		},
		/**********************************************************
		Recalculate the scrollable area
		**********************************************************/
		recalculateScrollableArea: function () {

			var tempScrollableAreaWidth = 0, foundStartAtElement = false, o = this.options, el = this.element, children = el.data("scrollableArea").children();

			// Add up the total width of all the items inside the scrollable area
			if (children.length) {
				el.data("scrollableArea").children().each(function () {
					// Check to see if the current element in the loop is the one where the scrolling should start
					if ((o.startAtElementId.length > 0) && (($(this).attr("id")) === o.startAtElementId)) {
						el.data("startingPosition", tempScrollableAreaWidth);
						foundStartAtElement = true;
					}
					tempScrollableAreaWidth = tempScrollableAreaWidth + $(this).outerWidth(true);
				});
			} else {
				// Doesn't have children, so calculate width of scollableAread
				tempScrollableAreaWidth += el.data("scrollableArea").outerWidth(true);
			}

			// If the element with the ID specified by startAtElementId
			// is not found, reset it
			if (!(foundStartAtElement)) {
				el.data("startAtElementId", "");
			}

			// Set the width of the scrollable area
			el.data("scrollableAreaWidth", tempScrollableAreaWidth);
			el.data("scrollableArea").width(el.data("scrollableAreaWidth"));

			// Move to the starting position
			el.data("scrollWrapper").scrollLeft(el.data("startingPosition"));
			el.data("scrollXPos", el.data("startingPosition"));
		}
		
    });

})( jQuery, window, document );
