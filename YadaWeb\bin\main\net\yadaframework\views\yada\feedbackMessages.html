<!DOCTYPE html>
<html 
	xmlns:th="http://www.thymeleaf.org">
<head>
	<meta charset="utf-8" />
</head>
<body>
<!--/* 
	* Shows alerts embedded in page with information messages or errors 
	* Parameters:
	* - messages for information messages
	* - errorMessages for error messages
	Usage: <div th:replace="~{/yada/feedbackMessages :: div}"></div>
*/-->
<div style="margin-top: 10px;" th:each="message : ${messages}" class="alert alert-success alert-dismissible" role="alert">
	<span th:utext="${message}">Messaggio</span>
	<button th:if="${@config.bootstrapVersion>4}" type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
	<button th:if="${@config.bootstrapVersion<5}" type="button" class="close" data-dismiss="alert"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
</div>
<div style="margin-top: 10px;" th:each="message : ${errorMessages}" class="alert alert-danger alert-dismissible" role="alert">
	<span th:utext="${message}">Messaggio</span>
	<button th:if="${@config.bootstrapVersion>4}" type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
	<button th:if="${@config.bootstrapVersion<5}" type="button" class="close" data-dismiss="alert"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
</div>
</body>
</html>