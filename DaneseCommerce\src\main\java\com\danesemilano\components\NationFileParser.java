package com.danesemilano.components;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import com.danesemilano.persistence.entity.Nation;
import com.danesemilano.persistence.repository.NationRepoDao;

/**
 * Metodi per il caricamento dei file xlsx e csv
 * <AUTHOR>
 *
 */
@Component
public class NationFileParser {
	private final Logger log = LoggerFactory.getLogger(getClass());
	
	@Autowired private NationRepoDao nationRepository;
	
	public class Result {
		public int added;
		public int updated;
		public int removed;
		public int errors;
	}

	/**
	 * Carica il file csv dei nations.
	 * @param multipartFile
	 * @param Locale localeToLoad
	 * @throws IOException 
	 */
	public Result parseCsvFile(MultipartFile multipartFile, Locale localeToLoad) throws IOException {
		Result result = new Result();
		int COL_CODE=0;
		int COL_NAME=1;
		List<Nation> allNations = nationRepository.findAll();
		Map<String, Nation> previousNationMap = new HashMap<>();
		for (Nation nation : allNations) {
			previousNationMap.put(nation.getCode(), nation);
		}
		//
		try (InputStream inputStream = multipartFile.getInputStream()) {
			List<Nation> batchToSave = new ArrayList<>(); 
			String charset = "UTF-8"; // or what corresponds
			try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, charset))) {
				String line = reader.readLine();
				line = reader.readLine(); // Salta l'header
				while (line!=null) {
					try {
						String[] columns = StringUtils.splitPreserveAllTokens(line, ',');
						String code= columns[COL_CODE];
						String name= columns[COL_NAME];
						
						Nation nation = previousNationMap.remove(code);
						if (nation==null) {
							nation = new Nation();
							nation.setCode(code);
							// nation.setName(newName);
							result.added++;
						} else {
							result.updated++;
						}
						Map<Locale, String> newName = nation.getName();
						newName.put(localeToLoad, name.replace("\"", ""));
						// Non salvo subito la Entity ma la aggiungo a una lista per salvare
						// in gruppo, che è più veloce
						batchToSave.add(nation);
						if (batchToSave.size() % 100 == 99) {
							// Salvo 100 oggetti alla volta
							nationRepository.save(batchToSave);
							batchToSave.clear();
						}
					} catch (Exception e) {
						log.error("Impossibile caricare la linea: {}", line, e);
						result.errors++;
					}
					line = reader.readLine();
				}
			}
			if (batchToSave.size()>0) {
				// Salvo gli ultimi rimasti
				nationRepository.save(batchToSave);
				batchToSave.clear();
			}
//			// Nella mappa sono rimasti gli elementi che sono nel database ma non nel file
//			for (Nation nation : previousNationMap.values()) {
//				// Cancello il nation
//				nationRepository.delete(nation);
//				result.removed++;
//			}
		} catch (IOException e) {
			throw e;
		}
		return result;
	}

	
}
