<!DOCTYPE html>
<html
	xmlns:th="http://www.thymeleaf.org"
	xmlns:yada="http://yada.yodadog.net"
  	>
<head>
	<meta charset="utf-8" />
</head>
<body>
<!--/*
	 * This is a generic template for a bootstrap 5 modal with title and body, to be returned via ajax or included in page.
	 * @param title modal-title text. Not used when header is specified
	 * @param header optional custom modal-header. Use ~{} when not needed or _ for the default
	 * @param body modal-body
	 * @param footer optional custom modal-footer. Use ~{} when not needed or _ for the default
	 * @param script optional script
	 * @param extraDialogClasses any classes to be added to the modal-dialog
	 * @param id optional modal id. Use ~{} when not needed
 */-->
<div th:fragment="fragment(title,header,body,footer,script,extraDialogClasses,id)" th:with="titleId='t'+${#strings.randomAlphanumeric(16)}"
	class="modal fade" tabindex="-1" role="dialog" th:aria-labelledby="${titleId}" aria-hidden="true" th:id="${id?:#strings.randomAlphanumeric(16)}">
	<div class="modal-dialog" th:classappend="${extraDialogClasses}" role="document">
    	<div class="modal-content card">
    		<div th:replace="${header}">
	    		<div class="modal-header">
	    			<h4 class="modal-title" th:id="${titleId}"><span th:replace="${title}"></span></h4>
	    			<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
	   			</div>
    		</div>
     		<div th:replace="${body}">
	   			<div class="modal-body p0">
	   				<div class="card-body">
	   					Default body
	   				</div>
	   			</div>
 			</div>
 			<div th:replace="${footer}">
	   			<div class="modal-footer">
					<button type="button" class="btn  btn-secondary" data-bs-dismiss="modal" th:text="#{yada.view.modal.button.close}">Close</button>
	   			</div>
 			</div>   		
  		</div>
		<script th:replace="${script}"></script>
	</div>

</div>

</body>
</html>