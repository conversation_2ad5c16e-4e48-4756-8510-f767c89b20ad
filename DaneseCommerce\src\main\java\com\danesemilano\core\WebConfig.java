package com.danesemilano.core;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.thymeleaf.spring6.SpringTemplateEngine;

import net.yadaframework.security.YadaWebSecurityConfig;

@ComponentScan(basePackages = { "com.danesemilano.web" })
@Configuration
// Extend YadaWebConfig if you're not using the YadaWebSecurity project
public class WebConfig extends YadaWebSecurityConfig {

	@Override
	protected void addExtraDialect(SpringTemplateEngine engine) {
		// Add any dialects you might need
		// engine.addDialect(new LayoutDialect()); // thymeleaf-layout-dialect
	}
	
}

