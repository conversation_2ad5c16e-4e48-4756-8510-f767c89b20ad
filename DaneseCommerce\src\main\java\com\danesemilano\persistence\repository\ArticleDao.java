package com.danesemilano.persistence.repository;

import java.util.Locale;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.danesemilano.persistence.entity.Article;

import net.yadaframework.components.YadaUtil;
import net.yadaframework.persistence.repository.YadaLocaleDao;

/**
 * Locale-related operations on entities 
 */
@Repository
@Transactional(readOnly = true) 
public class ArticleDao {
	private final Logger log = LoggerFactory.getLogger(this.getClass());
	
    //@Autowired private YadaConfiguration config;
//	@Autowired private YadaUtil yadaUtil;
	@Autowired private YadaLocaleDao yadaLocaleDao;
//    @Autowired private YadaArticleRepository<Article> yadaArticleRepository;
    @PersistenceContext
	private EntityManager em;
    
    /**
     * Delete an Article
     * @param id, locale
     */
    @Transactional(readOnly = false) 
    public String delete(Long id, Locale locale) {
    	// E' molto comodo usare l'Entity Manager perché, sebbene carichi tutta la riga prima di cancellare, 
    	// si occupa di eliminare tutte le relazioni senza problemi di foreign key violations.
    	Article t = em.find(Article.class, id);
    	String name="";
		try {
			name = t.getProduct().getLocalName();
		} catch (Exception e) {
			log.error("Impossibile recuperare il nome dell'Article {} (ignorato)", id, e);
		}
    	em.remove(t);
        return name;
//    	String name = (String) YadaSql.instance().selectFrom("select name from YadaArticle a ")
//    		.join("join YadaArticle_name n ON a.id = n.YadaArticle_id")
//    		.where("a.id=:articleId").and()
//    		.where("n.locale=:locale").and()
//	    	.setParameter("articleId", id)
//	    	.setParameter("locale", locale.toString())
//	    	.nativeQuery(em)
//	    	.getSingleResult();
//    	performQuery(em.createNativeQuery("delete from Article_finish where Article_id = :articleId"), id);
//    	performQuery(em.createNativeQuery("delete from Article_itemSize where Article_id = :articleId"), id);
//    	performQuery(em.createNativeQuery("delete from Article_laguage where Article_id = :articleId"), id);
//    	performQuery(em.createNativeQuery("delete from Article_tipology where Article_id = :articleId"), id);
//    	performQuery(em.createNativeQuery("delete from YadaArticle_galleryImages where YadaArticle_id = :articleId"), id);
//    	performQuery(em.createNativeQuery("delete from YadaArticle_attachments where YadaArticle_id = :articleId"), id);
//    	performQuery(em.createNativeQuery("delete from YadaArticle_silhouetteImages where YadaArticle_id = :articleId"), id);
//    	performQuery(em.createNativeQuery("delete from YadaArticle_name where YadaArticle_id = :articleId"), id);
//    	performQuery(em.createNativeQuery("delete from YadaArticle_color where YadaArticle_id = :articleId"), id);
//    	performQuery(em.createNativeQuery("delete from YadaArticle where id = :articleId"), id);
//    	return name;
    }
    
//    private void performQuery(Query query, Long id) {
//    	query.setParameter("articleId", id);
//    	query.executeUpdate();
//    }
    
    /**
     * Duplicate an article
     * @param article
     * @return
     */
    @Transactional(readOnly = false) 
    public Object duplicate(Article article) {
    	return YadaUtil.copyEntity(article);
    }

    /**
     * Inizializzo tutti gli attributi lazy che mi servono in pagina (oltre alle stringhe localizzate)
     * @param id
     * @return
     */
    public Article findAndLoadLazyAttributes(Long id) {
		Article found = yadaLocaleDao.findOneWithLocalValues(id, Article.class);
		if (found!=null) {
			if (found.getProduct()!=null) {
				found.getProduct().getName().size(); // Nome del prodotto
			}
			found.getTipology().size();
		}
		return found;
	}
    
	@Transactional(readOnly = false)
	public Article save(Article entity) {
		if (entity==null) {
			return null;
		}
		if (entity.getId()==null) {
			em.persist(entity);
			return entity;
		}
		return em.merge(entity);
	}

}
