<header class="page__header">
  {% include 'breadcrumb' %}

  <div class="page__header-wrapper">
    <div class="container">
      <h1 class="page__title">{{ 'customer.login.title' | t }}</h1>
    </div>
  </div>
</header>

<div class="page__content">
  <section class="login-form">
    <div class="container container--shrink">
      {% form 'customer_login', class: 'form--shrinked' %}
        {% if form.errors %}
          <div class="alert alert--error">
            <span class="alert__title">{{ 'general.forms.errors' | t }}</span>
            {% include 'form-errors' %}
          </div>
        {% endif %}

        <div class="form__control {% if form.errors.message.email %}form__control--error{% endif %}">
          <label class="form__label" for="customer__email">{{ 'customer.login.email' | t }}</label>
          <input type="email" id="customer__email" name="customer[email]" required="required" tabindex="1">
        </div>

        <div class="form__control {% if form.errors.message.password %}form__control--error{% endif %}">
          <label class="form__label" for="customer__password">
            {{ 'customer.login.password' | t }}
            <a href="#" class="login-form__forgot link link--primary" data-action="display-recover-form">{{ 'customer.login.forgot' | t }}</a>
          </label>

          <input type="password" id="customer__password" name="customer[password]" required="required" tabindex="2">
        </div>

        <div class="button-wrapper">
          <button type="submit" class="button button--primary">{{ 'customer.login.submit' | t }}</button>
        </div>
      {% endform %}
    </div>
  </section>

  <section class="recover-form" style="display: none">
    <div class="container container--shrink">
      {% form 'recover_customer_password', class: 'form--shrinked' %}
        {% if form.errors %}
          <div class="alert alert--error">
            <span class="alert__title">{{ 'general.forms.errors' | t }}</span>
            {% include 'form-errors' %}
          </div>
        {% endif %}

        {% if form.posted_successfully? %}
          <script>window.recoverPassword = true;</script>

          <div class="alert alert--success">
            <span class="alert__title">{{ 'customer.recover.sent' | t }}</span>
          </div>
        {% endif %}

        <div class="form__control {% if form.errors.message.email %}form__control--error{% endif %}">
          <label class="form__label" for="customer__email">{{ 'customer.recover.email' | t }}</label>
          <input type="email" id="customer__email" name="email" required="required" tabindex="1">
        </div>

        <div class="button-wrapper">
          <button type="submit" class="button button--primary">{{ 'customer.recover.submit' | t }}</button>
        </div>
      {% endform %}
    </div>
  </section>
</div>

<footer class="page__footer">
  <h4 class="page__footer-text">{{ 'customer.login.not_member' | t }}</h4>
  <a href="/account/register" class="button button--secondary">{{ 'customer.login.register' | t }}</a>
</footer>