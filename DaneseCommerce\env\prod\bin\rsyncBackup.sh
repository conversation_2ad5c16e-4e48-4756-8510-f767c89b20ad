#!/bin/bash
# Copy a folder to a remote server via rsync
# Should be set in cron daily:
# ln -s /myPath/rsyncBackup.sh /etc/cron.daily/rsyncbackup

identityKey=/home/<USER>/.ssh/dnc.private.pem
week=$((($(date +%-d)-1)/7+1))

targetIP=www.artemide.com
user=amduser
targetPath=/mnt/backup/danese/sito2018/dncprod-${week}
options='--delete -avv'
logfile=/srv/dncprod/logs/rsync_client.log

# Trailing slash is important to skip folder creation on destination
srcDir=/srv/dncprod/
exclude="--exclude=backup --exclude=logs --exclude=uploads  --exclude=deploy"

# Faccio il backup del db da copiare insieme
mysqldump --defaults-file=/srv/dncprod/bin/backupdb.cnf dncdbprod | gzip > /srv/dncprod/dncdbprod.gz

# The -i <pathToPrivateKey> option is not needed if servers are configured for mutual ssh authentication
nice -n 20 rsync -e "ssh -i $identityKey" $options $exclude $srcDir ${user}@${targetIP}:${targetPath} >> $logfile

