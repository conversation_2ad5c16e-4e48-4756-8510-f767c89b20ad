<nav class="navigation-sidebar" role="navigation">
  {% comment %}
  The first level is a bit special because it is the one containing the cross to close the menu. Next levels are
  just recursive
  {% endcomment %}

  <header class="navigation-sidebar__header">
    <div class="navigation-sidebar__top">
      <a href="#" class="navigation-sidebar__close" data-action="close-navigation-sidebar">
        {% include 'icon' with 'close-thin' %}
      </a>

      <a href="#" data-action="rewind-navigation-sidebar" style="display: none">
        {% include 'icon' with 'arrow-left' %}
      </a>

      <span class="navigation-sidebar__current">
        <span class="navigation-sidebar__current-title"></span>
      </span>
    </div>

    {% if settings.navigation_sidebar_image %}
      <div class="navigation-sidebar__image">
        <img src="{{ settings.navigation_sidebar_image | img_url: '650x300' }}" alt="{{ shop.name | escape }}">
      </div>
    {% endif %}

    <div class="navigation-sidebar__search--mobile">
      {% include 'icon' with 'search' %}

      <form class="navigation-sidebar__search-form" action="/search" method="GET" role="search">
        <input type="hidden" name="type" value="{{ settings.search_mode }}">
        <input type="search" name="q" class="search__input" autocomplete="off" autocorrect="off" aria-label="{{ 'layout.navigation.search_placeholder' | t }}" placeholder="{{ 'layout.navigation.search_placeholder' | t }}" value="{{ search.terms | escape }}">
      </form>
    </div>
  </header>

  {% assign handle = settings.navigation_menu | default: 'main-menu' %}
  {% assign sidebar_nav_linklist = linklists[handle] %}

  <div class="navigation-sidebar__lists">
    {% comment %}
    A complex thing in Kagami sidebar navigation is that each list is flatten to be able to provide the desired effect, and not nested into
    the DOM. To achieve that, we are doing various iterations
    {% endcomment %}

    <ul class="navigation-sidebar__list navigation-sidebar__list--active" tabindex="-1" data-nav-for="{{ handle }}" data-nav-name="{{ sidebar_nav_linklist.title | escape }}">
      {% for link in sidebar_nav_linklist.links %}
        {% assign sub_linklist = linklists[link.handle] %}
        {% assign has_sublinks = false %}

        {% if sub_linklist.handle != blank and sub_linklist.links.size > 0 %}
          {% assign has_sublinks = true %}
        {% endif %}

        <li class="navigation-sidebar__item {% if has_sublinks %}navigation-sidebar__item--expandable{% endif %}">
          <a href="{{ link.url }}" class="navigation-sidebar__link" {% if has_sublinks %}data-open-nav="{{ link.handle }}" aria-haspopup="true"{% endif %}>
            <span class="navigation-sidebar__link-title">{{ link.title | escape }}</span>
            {% if has_sublinks %}{% include 'icon' with 'arrow-right' %}{% endif %}
          </a>
        </li>
      {% endfor %}

      {% comment %}For the first level, we also add some special links{% endcomment %}

      {% if shop.customer_accounts_enabled %}
        {% if customer %}
          <li class="navigation-sidebar__item">
            <a href="/account" class="navigation-sidebar__link" >{{ 'customer.account.title' | t }}</a>
          </li>
        {% else %}
          <li class="navigation-sidebar__item">
            <a href="/account/login" class="navigation-sidebar__link" >{{ 'customer.login.title' | t }}</a>
          </li>
        {% endif %}
      {% endif %}
    </ul>

    {% comment %}To output second level, we iterate only in those that have links{% endcomment %}

    {% for link in sidebar_nav_linklist.links %}
      {% assign sub_linklist = linklists[link.handle] %}

      {% if sub_linklist.handle == blank or sub_linklist.links.size == 0 %}
        {% continue %}
      {% endif %}

      <ul class="navigation-sidebar__list" tabindex="-1" data-nav-for="{{ link.handle }}" data-nav-name="{{ link.title | escape }}" data-parent-nav="{{ sidebar_nav_linklist.handle }}">
        {% for sub_link in sub_linklist.links %}
          {% assign sub_sub_linklist = linklists[sub_link.handle] %}
          {% assign has_subsublinks = false %}

          {% if sub_sub_linklist.handle != blank and sub_sub_linklist.links.size > 0 %}
            {% assign has_subsublinks = true %}
          {% endif %}

          <li class="navigation-sidebar__item {% if has_subsublinks %}navigation-sidebar__item--expandable{% endif %}">
            <a href="{{ sub_link.url }}" class="navigation-sidebar__link" {% if has_subsublinks %}data-open-nav="{{ sub_link.handle }}" aria-haspopup="true"{% endif %}>
              <span class="navigation-sidebar__link-title">{{ sub_link.title | escape }}</span>
              {% if has_subsublinks %}{% include 'icon' with 'arrow-right' %}{% endif %}
            </a>
          </li>
        {% endfor %}
      </ul>
    {% endfor %}

    {% comment %}To output third level, we iterate only in link that have links that have links (:D){% endcomment %}

    {% for link in sidebar_nav_linklist.links %}
      {% assign sub_linklist = linklists[link.handle] %}

      {% if sub_linklist.handle == blank or sub_linklist.links.size == 0 %}
        {% continue %}
      {% endif %}

      {% for sub_link in sub_linklist.links %}
        {% assign sub_sub_linklist = linklists[sub_link.handle] %}

        {% if sub_sub_linklist.handle == blank or sub_sub_linklist.links.size == 0 %}
          {% continue %}
        {% endif %}

        <ul class="navigation-sidebar__list" tabindex="-1" data-nav-for="{{ sub_link.handle }}" data-nav-name="{{ sub_link.title | escape }}" data-parent-nav="{{ link.handle }}">
          {% for sub_sub_link in sub_sub_linklist.links %}
            <li class="navigation-sidebar__item">
              <a href="{{ sub_sub_link.url }}" class="navigation-sidebar__link">
                <span class="navigation-sidebar__link-title">{{ sub_sub_link.title | escape }}</span>
              </a>
            </li>
          {% endfor %}
        </ul>
      {% endfor %}
    {% endfor %}
  </div>
</nav>