{% comment %}
Add Facebook and Pinterest Open Graph meta tags to product pages
for friendly Facebook sharing and Pinterest pinning.

More info Open Graph meta tags
- https://developers.facebook.com/docs/opengraph/using-objects/
- https://developers.pinterest.com/rich_pins/

Use the Facebook Open Graph Debugger for validation (and cache clearing)
- http://developers.facebook.com/tools/debug

Validate your Pinterest rich pins
- https://developers.pinterest.com/rich_pins/validator/
{% endcomment %}

{% if template contains 'product' %}
  <meta property="og:type" content="product">
  <meta property="og:title" content="{{ product.title | strip_html | escape }}">

  {% for image in product.images limit:3 %}
    <meta property="og:image" content="http:{{ image.src | img_url: 'grande' }}">
    <meta property="og:image:secure_url" content="https:{{ image.src | img_url: 'grande' }}">
  {% endfor %}

  <meta property="og:price:amount" content="{{ product.price | money_without_currency | strip_html | escape }}">
  <meta property="og:price:currency" content="{{ shop.currency }}">
{% elsif template contains 'article' %}
  <meta property="og:type" content="article">
  <meta property="og:title" content="{{ article.title | strip_html | escape }}">

  {% if article.image %}
    <meta property="og:image" content="http:{{ article.image | img_url: 'grande' }}">
    <meta property="og:image:secure_url" content="https:{{ article.image | img_url: 'grande' }}">
  {% endif %}
{% else %}
  <meta property="og:type" content="website">
  <meta property="og:title" content="{{ page_title | escape }}">

  {% if settings.header_use_logo %}
    <meta property="og:image" content="http:{{ 'logo.png' | asset_url }}">
    <meta property="og:image:secure_url" content="https:{{ 'logo.png' | asset_url }}">
  {% endif %}
{% endif %}

{% if page_description %}
  <meta property="og:description" content="{{ page_description | escape }}">
{% endif %}

<meta property="og:url" content="{{ canonical_url }}">
<meta property="og:site_name" content="{{ shop.name }}">

{% comment %}
This snippet renders meta data needed to create a Twitter card
for products and articles.

Your cards must be approved by Twitter to be activated
- https://dev.twitter.com/docs/cards/validation/validator

More information:
- https://dev.twitter.com/cards/types/summary
{% endcomment %}

{% comment %}
Twitter user name of the site, based on theme settings
{% endcomment %}
{% if settings.twittercard_handle != blank %}
  <meta name="twitter:site" content="{{ settings.twittercard_handle }}">
{% endif %}
<meta name="twitter:card" content="summary">
{% if template contains 'product' %}
  <meta name="twitter:title" content="{{ product.title }}">
  <meta name="twitter:description" content="{{ product.description | strip_html | truncatewords: 140, '' | escape }}">
  <meta name="twitter:image" content="https:{{ product.featured_image.src | img_url: 'medium' }}">
  <meta name="twitter:image:width" content="240">
  <meta name="twitter:image:height" content="240">
{% elsif template contains 'article' %}
  <meta name="twitter:title" content="{{ article.title }}">
  <meta name="twitter:description" content="{{ article.excerpt_or_content | strip_html | truncatewords: 140, '' | escape }}">
  {% comment %}
  Check if content contains an image to add to the card
  - Source: http://blog.viralica.com/2013/09/twitter-product-cards-on-shopify/
  {% endcomment %}

  {% if article.image %}
    <meta property="twitter:image" content="{{ article.image | img_url: 'medium' }}">
    <meta name="twitter:image:width" content="240">
    <meta name="twitter:image:height" content="240">
  {% endif %}
{% endif %}