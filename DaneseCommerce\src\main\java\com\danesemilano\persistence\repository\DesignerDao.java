package com.danesemilano.persistence.repository;

import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.danesemilano.persistence.entity.Designer;
import com.danesemilano.persistence.entity.Product;

import net.yadaframework.components.YadaUtil;
import net.yadaframework.persistence.YadaSql;
import net.yadaframework.persistence.repository.YadaLocaleDao;

/**
 * Locale-related operations on entities 
 */
@Repository
@Transactional(readOnly = true) 
public class DesignerDao {
	
    //@Autowired private YadaConfiguration config;
    @Autowired private DesignerRepoDao designerRepository;
    @Autowired private YadaLocaleDao yadaLocaleDao;
    @PersistenceContext
	private EntityManager em;
    
    /**
     * Rimuovo l'associazione tra il prodotto e i designer che non sono in toKeep.
     * @param toKeep
     * @param productId
     * @return the removed designers
     */
    @Transactional(readOnly = false) 
	public List<Designer> deleteMissingDesigners(List<Designer> toKeep, Long productId) {
    	// Find designers not to keep
    	Product product = em.find(Product.class, productId);
    	String slqRemovedDesigners = "select d from Designer d where :product member of d.products and d not in :toKeep";
    	List<Designer> removed = em.createQuery(slqRemovedDesigners, Designer.class)
    		.setParameter("product", product)
    		.setParameter("toKeep", toKeep)
    		.getResultList();
    	// Delete the designers
    	List<Long> toKeepIds = toKeep.stream().map(Designer::getId).collect(Collectors.toList());
    	String slqDeleteDesigner = "delete from Designer_YadaProduct where products_id = :productId and designers_id not in :toKeepIds";
		em.createNativeQuery(slqDeleteDesigner)
			.setParameter("productId", productId)
			.setParameter("toKeepIds", toKeepIds)
			.executeUpdate();
		return removed;
//		// Also delete designer tags
//		// First, find the tags to delete
//		String sqlTagToDelete = "select * from Tag t join Tag_YadaProduct typ on t.id = typ.tags_id join Designer_YadaProduct dyp on typ.products_id = dyp.products_id\"\r\n"
//				+ "			+ \" where typ.products_id = :productId and dyp.designers_id not in :toKeepIds\";
//		
//		String sqlTag = "delete dyp, typ from Tag_YadaProduct typ join Designer_YadaProduct dyp on typ.products_id = dyp.products_id"
//			+ " where typ.products_id = :productId and dyp.designers_id not in :toKeepIds";
//		em.createNativeQuery(sqlTag)
//		.setParameter("productId", productId)
//		.setParameter("toKeepIds", toKeepIds)
//		.executeUpdate();
	}
    
    /**
     * Finds a Designer and loads most of the lazy relationships
     * @param id
     * @return
     */
	public Designer findOneWithLocalValuesAndSubcategories(Long id) {
		Designer found = yadaLocaleDao.findOneWithLocalValues(id, Designer.class);
		if (found!=null) {
			for (Product productFromDesigner : found.getProducts()) {
				productFromDesigner.getSubcategories().size();
			}
		}
		return found;
	}
    
    /**
     * Cancella un designer
     * @param designer
     */
    @Transactional(readOnly = false) 
    public String delete(Long id, Locale locale) {
    	String name = (String) YadaSql.instance().selectFrom("select name from Designer")
        		.where("where id=:designerId")
    	    	.setParameter("designerId", id)
    	    	.nativeQuery(em)
    	    	.getSingleResult();
    	
        	performQuery(em.createNativeQuery("delete from Designer_description where Designer_id = :designerId"), id);
        	performQuery(em.createNativeQuery("delete from Designer_YadaProduct where designers_id = :designerId"), id);
        	performQuery(em.createNativeQuery("delete from Designer where id = :designerId"), id);
        	//performQuery(em.createNativeQuery("delete from YadaAttachedFile where attachedToId = :designerId and relativeFolderPath='/images/designers'"), id);
        	return name;
    }
    
    private void performQuery(Query query, Long id) {
    	query.setParameter("designerId", id);
    	query.executeUpdate();
    }
    
    /**
     * Duplicate a designer
     * @param designer
     * @return
     */
    @Transactional(readOnly = false) 
    public Object duplicate(Designer designer) {
    	return YadaUtil.copyEntity(designer);
    }

}
