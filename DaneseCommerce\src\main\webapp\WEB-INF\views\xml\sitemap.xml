<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:th="http://www.thymeleaf.org"> 
  <url th:each="url : ${urls}">
	<loc th:text="${@config.webappAddress() + url}">http://path/to/page</loc>
	<changefreq>daily</changefreq>
  </url>
  <!-- Add any other dynamic pages here 
  <url th:each="id : ${ids}">
	<loc th:text="${@config.webappAddress() + '/racconti/' + id}">http://path/to/story</loc>
	<changefreq>monthly</changefreq>
  </url>
  -->
  <url th:each="product : ${products}">
	<loc th:text="${@config.webappAddress() + '/productDetails?idProduct='+product.id}">http://path/to/products</loc>
  	<changefreq>weekly</changefreq>
  </url>
  <url th:each="designer : ${designers}">
	<loc th:text="${@config.webappAddress() + '/designerDetails?idDesigner='+designer.id}">http://path/to/designers</loc>
  	<changefreq>weekly</changefreq>
  </url>
 <url  th:each="enum : ${@yadaWebUtil.sortLocalEnum(T(com.danesemilano.persistence.entity.EnumContract), #locale)}">
   	<loc th:text="${@config.webappAddress() + '/contract/'+enum.toYadaPersistentEnum().getId()}">http://path/to/contract</loc> 
   	<changefreq>yearly</changefreq>
 </url>
</urlset>
