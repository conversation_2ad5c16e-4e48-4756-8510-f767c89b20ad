package com.danesemilano.core;

import org.apache.commons.configuration2.ex.ConfigurationException;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import net.yadaframework.core.YadaAppConfig;

@Configuration
@ComponentScan(basePackages = { "com.danesemilano.components", "com.danesemilano.persistence.entity", "com.danesemilano.persistence.repository" })
public class AppConfig extends YadaAppConfig {
	
	/**
	 * Creo il bean "config" in modo da poterlo usare in pagina con @config.xy invece di @dncConfiguration.xy
	 */
	@Bean
	public DncConfiguration config() throws ConfigurationException {
		DncConfiguration config = new DncConfiguration();
		super.makeCombinedConfiguration(config);
		return config;
	}

}
