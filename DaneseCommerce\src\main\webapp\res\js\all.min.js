if(function(yada){"use strict";var clickedButton,ajaxCounter=0;yada.postLoginHandler=null;function initObservers($element){yada.enableAjaxTriggerInViewport($element)}function handlePaginationHistoryAttribute($elem,$linkOrForm){var yadaPagination=$elem.attr("data-yadaPaginationHistory");if(null==yadaPagination)return!1;""==yadaPagination&&(yadaPagination=null);const paginationParams=yada.listToArray(yadaPagination);return yada.fixPaginationLinkHistory($linkOrForm,paginationParams[0],paginationParams[1],paginationParams[2]),!0}function openLoginModalIfPresent(responseHtml){var loadedLoginModal=$(responseHtml).find("#loginModal");if(loadedLoginModal.length>0){var currentLoginModal=$("#loginModal.in");if(currentLoginModal.length>0){$("#loginModalDialog",currentLoginModal).replaceWith($("#loginModalDialog",loadedLoginModal)),$("#username").focus()}else{const $existingModals=$(".modal.show");$existingModals.modal("hide"),$existingModals.remove(),$("#loginModal").remove(),$("body").append(loadedLoginModal),$("#loginModal").on("shown.bs.modal",function(e){$("#username").focus()}),$("#loginModal").modal("show")}return yada.enableAjaxForm($("#loginForm"),null),!0}return!1}yada.markerAjaxModal="yadaAjaxModal",yada.initAjaxHandlersOn=function($element){yada.enableAjaxForms(null,$element),yada.enableAjaxLinks(null,$element),yada.enableAjaxSelects(null,$element),yada.enableAjaxCheckboxes(null,$element),yada.enableDropUpload(null,$element),initObservers($element),yada.enableAjaxInputs()},yada.fixPaginationLinkHistory=function($linkOrForm,pageParam,sizeParam,loadPreviousParam){pageParam=pageParam||"page",sizeParam=sizeParam||"size",loadPreviousParam=loadPreviousParam||"loadPrevious";const nextPageUrl=$linkOrForm.attr("data-yadahref")||$linkOrForm.attr("href");var nextPage=yada.getUrlParameter(nextPageUrl,"page"),nextSize=yada.getUrlParameter(nextPageUrl,"size");null==nextPageUrl&&(nextPage=$("input[name=page]",$linkOrForm).val()||1,nextSize=$("input[name=size]",$linkOrForm).val()||32);const currentUrl=window.location.href;var newUrl=yada.addOrUpdateUrlParameter(currentUrl,pageParam,nextPage);newUrl=yada.addOrUpdateUrlParameter(newUrl,sizeParam,nextSize),newUrl=yada.addOrUpdateUrlParameter(newUrl,loadPreviousParam,!0);const updateTargetSelector=$linkOrForm.attr("data-yadaUpdateOnSuccess"),$container=yada.extendedSelect($linkOrForm,updateTargetSelector).parent();var containerId=$container.attr("id");if(null!=containerId){const scrollPos=$container.scrollTop();newUrl=yada.addOrUpdateUrlParameter(newUrl,"yadaContainer",containerId),newUrl=yada.addOrUpdateUrlParameter(newUrl,"yadaScroll",scrollPos)}history.pushState({},"",newUrl)},yada.openModalOnHash=function(targetUrl,paramNames,separator,validator){var hashValue=document.location.hash;if(null!=hashValue&&hashValue.length>1)try{var data=yada.hashPathToMap(paramNames,hashValue,separator);"function"==typeof validator&&validator(data)&&yada.ajax(targetUrl,data)}catch(e){console.error(e)}},yada.enableAjaxSelectOptions=function(){$(".s_chain select").change(function(){var selectedValue=$("option:selected",this).val(),$triggerSelectContainer=$(this).parents(".s_chain"),triggerContainerId=$triggerSelectContainer.attr("id");if(triggerContainerId){var $targetSelectContainer=$triggerSelectContainer.siblings("[data-trigger-id="+triggerContainerId+"]");if($targetSelectContainer){var targetUrl=$targetSelectContainer.attr("data-url"),targetExclude=$targetSelectContainer.attr("data-exclude-id"),selectedOption=$targetSelectContainer.attr("data-selected-option"),data={triggerId:selectedValue,excludeId:targetExclude};yada.ajax(targetUrl,data,function(responseText){$("select",$targetSelectContainer).children().remove(),$("select",$targetSelectContainer).append(responseText),selectedOption&&$('select option[value="'+selectedOption+'"]',$targetSelectContainer).prop("selected",!0),$("select",$targetSelectContainer).prop("disabled",!1)},null,null,getLoaderOption($(this)))}}})},yada.handlePostLoginHandler=function(responseHtml,responseText){var isError=yada.isNotifyError(responseHtml);yada.handleNotify(responseHtml),null!=yada.postLoginHandler?isError||yada.postLoginHandler(responseText,responseHtml):(console.error("YadaWarning: deprecated page reload after ajax login"),yada.loaderOn(),window.location.href=yada.removeHash(window.location.href)),yada.postLoginHandler=null},yada.openLoginModal=function(url,data,handler,type){return $("#loginModal").length>0&&(yada.postLoginHandler=handler,$("#loginModal").on("shown.bs.modal",function(e){$("#username").focus()}),$("#loginModal").modal("show"),!0)},yada.openLoginModalAjax=function(loginFormUrl,handler,errorTitle,errorText){yada.postLoginHandler=handler,$.get(loginFormUrl,function(responseText,statusText){openLoginModalIfPresent($("<div class='yadaAjaxResponseHtml'>").html(responseText))||yada.showErrorModal(errorTitle,errorText)})},yada.callYadaCallbackIfPresent=function(responseHtml){var scriptNodes=$(responseHtml).find("script#yadaCallback");return scriptNodes.length>0&&($("#callbackJavascript").append(scriptNodes),yadaCallback(),!0)};const ajaxTriggerInViewportObserver=new IntersectionObserver(entries=>{entries.forEach(entry=>{entry.intersectionRatio>0&&(ajaxTriggerInViewportObserver.unobserve(entry.target),makeAjaxCall(null,$(entry.target)))})});function handleDragenterDragover(event){this.classList.add("yadaDragOver"),event.preventDefault()}function handleDragleaveDragend(event){this.classList.remove("yadaDragOver"),event.preventDefault()}function handleDrop($dropTarget,handler,event){let files=event.originalEvent.dataTransfer.files;files&&function(event,files,$dropTarget,handler){if(null==files||0==files.length)return;const singleFileOnly=$dropTarget.attr("data-yadaSingleFileOnly");if(null!=singleFileOnly&&files.length>1)return void yada.showErrorModal(yada.messages.singleFileOnly.title,yada.messages.singleFileOnly.message);const accept=$dropTarget.attr("data-yadaDropUploadAccept");if(null!=accept&&""!=accept){const patterns=accept.split(/\s*,\s*/);for(let file of files){const name=file.name.toLowerCase(),type=(file.type||"").toLowerCase();if(!patterns.some(pattern=>(pattern=pattern.trim().toLowerCase()).startsWith(".")?name.endsWith(pattern):pattern.endsWith("/*")?type.startsWith(pattern.slice(0,-1)):type===pattern))return void yada.showErrorModal(yada.messages.uploadAccept.title,yada.messages.uploadAccept.message)}}makeAjaxCall(event,$dropTarget,handler)}(event,files,$dropTarget,handler)}yada.enableAjaxTriggerInViewport=function($element){null!=$element&&""!=$element||($element=$("body"));var $target=$element.parent();0==$target.length&&($target=$element),$("[data-yadaTriggerInViewport]",$target).each(function(){null!=($(this).attr("data-yadaHref")||$(this).attr("href"))&&ajaxTriggerInViewportObserver.observe(this)})},yada.enableAjaxLinks=function(handler,$element){null!=$element&&""!=$element||($element=$("body"));var $target=$element.parent();0==$target.length&&($target=$element),$('a.yadaAjax, button.yadaAjax:not([type="submit"])',$target).each(function(){$(this).removeClass("yadaAjax"),yada.enableAjaxLink($(this),handler)}),$(".s_ajaxLink",$target).each(function(){$(this).removeClass("s_ajaxLink"),yada.enableAjaxLink($(this),handler)})},yada.enableAjaxCheckboxes=function(handler,$element){null!=$element&&""!=$element||($element=$("body"));var $target=$element.parent();0==$target.length&&($target=$element),$("input[type='checkbox'].yadaAjax",$target).each(function(){$(this).removeClass("yadaAjax"),yada.enableAjaxCheckbox($(this),handler)})},yada.enableAjaxCheckbox=function($checkbox,handler){$checkbox.length>1?$checkbox.each(function(){yada.enableAjaxCheckbox($(this),handler)}):($checkbox.not(".yadaAjaxed").change(function(e){var $form=($checkbox=$(this)).parents("form.yadaAjaxed");if(!($form.length>0))return makeAjaxCall(e,$checkbox,handler);$form.submit()}),$checkbox.removeClass("yadaAjax"),$checkbox.not(".yadaAjaxed").addClass("yadaAjaxed"))},yada.enableAjaxSelects=function(handler,$element){null!=$element&&""!=$element||($element=$("body"));var $target=$element.parent();0==$target.length&&($target=$element),$("select.yadaAjax",$target).each(function(){$(this).removeClass("yadaAjax"),yada.enableAjaxSelect($(this),handler)})},yada.enableAjaxSelect=function($select,handler){$select.length>1?$select.each(function(){yada.enableAjaxSelect($(this),handler)}):($select.not(".yadaAjaxed").change(function(e){return makeAjaxCall(e,$select=$(this),handler)}),$select.removeClass("yadaAjax"),$select.not(".yadaAjaxed").addClass("yadaAjaxed"))},yada.enableAjaxLink=function($link,handler){$link.length>1?$link.each(function(){yada.enableAjaxLink($(this),handler)}):($link.not(".yadaAjaxed").click(function(e){return handlePaginationHistoryAttribute($link=$(this),$link),makeAjaxCall(e,$link,handler)}),$link.removeClass("yadaAjax"),$link.removeClass("s_ajaxLink"),$link.not(".yadaAjaxed").addClass("yadaAjaxed"))};var handleDropProxy=null;function makeAjaxCall(e,$element,handler,allowDefault){if(e&&1==!allowDefault&&e.preventDefault(),$element.hasClass("yadaAjaxDisabled"))return!1;var joinedHandler=function(responseText,responseHtml){showFeedbackIfNeeded($element),deleteOnSuccess($element),responseHtml=updateOnSuccess($element,responseHtml);var handlerNames=$element.attr("data-yadaSuccessHandler");void 0===handlerNames&&(handlerNames=$element.attr("data-successHandler")),null!=handlerNames&&yada.executeFunctionListByName(handlerNames,$element,responseText,responseHtml,$element[0]),null!=handler&&handler(responseText,responseHtml,$element[0])},data=[],multipart=!1,method=null,url=null;const droppedFiles=e?.originalEvent?.dataTransfer?.files;if(droppedFiles){url=$element.attr("data-yadaDropUpload"),multipart=!0,data=new FormData;for(let i=0;i<droppedFiles.length;i++){let file=droppedFiles[i];data.append("multipartFile",file)}}if(null!=url&&""!=url||(url=$element.attr("data-yadaHref")),null!=url&&""!=url||(url=$element.attr("href")),null==url||""==url)return yada.log("No url for ajax call"),!1;if(!execSubmitHandlers($element))return!1;var confirmText=$element.attr("data-yadaConfirm")||$element.attr("data-confirm"),value=[];$element.is("select")?$("option:selected",$element).each(function(){value.push($(this).val())}):$element.is("input")&&("checkbox"==$element.prop("type")?value.push($element.prop("checked")):value.push($element.val()));const yadaFormGroup=$element.attr("data-yadaFormGroup");if(null!=yadaFormGroup){const $formGroup=$("form[data-yadaFormGroup="+yadaFormGroup+"]");$formGroup.length>0&&(multipart=$formGroup.filter("[enctype='multipart/form-data']").length>0,addAllFormsInGroup($formGroup,data=multipart?new FormData:[]))}const yadaRequestData=$element[0].yadaRequestData;if(data=mergeData(data,yadaRequestData),value.length>0){const name=$element.attr("name")||"value",toAdd={};toAdd.name=name,toAdd.value=value,data=mergeData(data,toAdd)}if(multipart?method="POST":data=$.param(data),null!=confirmText&&""!=confirmText){var title=$element.attr("data-yadaTitle"),okButton=$element.attr("data-yadaOkButton")||$element.attr("data-okButton")||yada.messages.confirmButtons.ok,cancelButton=$element.attr("data-yadaCancelButton")||$element.attr("data-cancelButton")||yada.messages.confirmButtons.cancel,okShowsPreviousModal=null==$element.attr("data-yadaOkShowsPrevious")||"true"==$element.attr("data-yadaOkShowsPrevious");yada.confirm(title,confirmText,function(result){1==result&&yada.ajax(url,data,null==joinedHandler?joinedHandler:joinedHandler.bind($element),method,getTimeoutValue($element),getLoaderOption($element))},okButton,cancelButton,okShowsPreviousModal)}else yada.ajax(url,data,null==joinedHandler?joinedHandler:joinedHandler.bind($element),method,null,getLoaderOption($element));return!0}function getTimeoutValue($element){var timeout=$element.attr("data-yadaTimeout");return null==timeout&&(timeout=$element.attr("data-timeout")),timeout}function deleteOnSuccess($element){var deleteSelector=$element.attr("data-yadaDeleteOnSuccess");if(null!=deleteSelector){var selectors=deleteSelector.split(",");const toDelete=[];for(var count=0;count<selectors.length;count++){var selector=selectors[count];toDelete.push(yada.extendedSelect($element,selector))}for(count=0;count<toDelete.length;count++)toDelete[count].remove();return!0}return!1}function updateOnSuccess($element,responseHtml){return function($element,responseHtml,attributeName,jqueryFunction){if(isNotification(responseHtml))return responseHtml;var selector=$element.attr(attributeName);if(null==selector)return responseHtml;var $replacement=responseHtml.children().clone(!0,!0);(function($element){$("[data-yadaDropUpload].yadaDropTarget",$element).each(function(){const $dropTarget=$(this);$dropTarget.removeClass("yadaDropTarget"),$dropTarget.off("dragenter dragover",null,handleDragenterDragover),$dropTarget.off("dragleave dragend drop",null,handleDragleaveDragend),null!=handleDropProxy&&$dropTarget.off("drop",null,handleDropProxy)})})($replacement),yada.enableDropUpload(null,$replacement),initObservers($replacement);var $return=$replacement,selectors=selector.split(","),$replacementArray=null;selectors.length>1&&0==($replacementArray=$(".yadaFragment",responseHtml)).length&&($replacementArray=$("._yadaReplacement_",responseHtml));null!=$replacementArray&&$replacementArray.length>1&&($return=[]);for(var fragmentCount=0,focused=!1,count=0;count<selectors.length;count++){selector=selectors[count].trim();null!=$replacementArray&&$replacementArray.length>0&&(initObservers($replacement=$replacementArray.eq(fragmentCount).clone(!0,!0)),0==count&&1==$replacementArray.length?$return=$replacement:$return.push($replacement),fragmentCount=(fragmentCount+1)%$replacementArray.length);jqueryFunction=$.fn.replaceWith;for(var isReplace=!0,jqueryFunctions=[{jqfunction:$.fn.replaceWith,prefix:"$replaceWith"},{jqfunction:$.fn.replaceWith,prefix:"$replace"},{jqfunction:$.fn.append,prefix:"$append"},{jqfunction:$.fn.prepend,prefix:"$prepend"}],i=0;i<jqueryFunctions.length;i++){const toCheck=jqueryFunctions[i];if(yada.startsWith(selector,toCheck.prefix+"(")&&selector.indexOf(")")>toCheck.prefix.length){jqueryFunction=toCheck.jqfunction,selector=yada.extract(selector,toCheck.prefix+"(",")"),yada.startsWith(toCheck.prefix,"$replace")||(isReplace=!1);break}}if(jqueryFunction.call(yada.extendedSelect($element,selector),$replacement),!isReplace||null!=selector&&""!=selector.trim()||($element=$replacement),!focused){const $toFocus=$("[data-yadaAjaxResultFocus]:not([readonly]):not([disabled])",$replacement);$toFocus.length>0&&($toFocus.get(0).focus(),focused=!0)}}return $return}($element,responseHtml,"data-yadaUpdateOnSuccess",$.fn.replaceWith)}function showFeedbackIfNeeded($element){null!=$element.attr("data-yadaShowAjaxFeedback")&&yada.showAjaxFeedback()}function execSubmitHandlers($element){var submitHandlerNames=$element.attr("data-yadaSubmitHandler");return 1==yada.executeFunctionListByName(submitHandlerNames,$element)}function mergeData(data,mergeFrom){if(null==mergeFrom)return data;const multipart=data instanceof FormData;return multipart||data instanceof Array?(Object.keys(mergeFrom).forEach(function(name){const value=mergeFrom[name];if(multipart)data.set(name,value);else{const obj={};obj[name]=value,$.extend(!0,data,[obj])}}),data):(console.error("YadaError: data should be Array or FormData in mergeData()."),data)}function addAllFormsInGroup($formGroup,data,$formToExclude){const multipart=data instanceof FormData;multipart||data instanceof Array?$formGroup.each(function(){var $eachForm=$(this);if(!$eachForm.is($formToExclude))if(multipart)for(var iterator=new FormData(this).entries(),iterElem=iterator.next();!iterElem.done;){var pair=iterElem.value;data.set(pair[0],pair[1]),iterElem=iterator.next()}else $.extend(!0,data,$eachForm.serializeArray())}):console.error("YadaError: data should be Array or FormData in addAllFormsInGroup().")}function showFullPage(html){document.open(),document.write(html),document.close()}function getLoaderOption($element){const ajaxElementLoaderSelector=$element.attr("data-yadaAjaxElementLoader");return null!=ajaxElementLoaderSelector?yada.extendedSelect($element,ajaxElementLoaderSelector):function($element){return $element.hasClass("noLoader")||$element.hasClass("noloader")||$element.hasClass("yadaNoLoader")||$element.hasClass("yadaNoloader")||$element.hasClass("yadanoloader")}($element)||null}function showNewModal($loadedModalDialog,responseHtml,responseText){var stickyModal=$loadedModalDialog.hasClass(yada.stickyModalMarker);const $modalObject=$(responseHtml).find(".modal").first();if($modalObject.addClass("yadaAjaxModal"),stickyModal){var $container=$("<div class='modal-open'></div>");$container.append($modalObject),$("body").prepend($container),$modalObject.on("hidden.bs.modal",function(e){$container.remove()})}else $("body").prepend($modalObject),$modalObject.on("hidden.bs.modal",function(e){$modalObject.remove()});if(responseText.indexOf("<head>")>-1){var htmlDoc=(new DOMParser).parseFromString(responseText,"text/html"),headNodes=$(htmlDoc.head).children();$("head").append(headNodes),function(headNodes,$modalObject){$modalObject.on("hidden.bs.modal",function(e){if(null!=headNodes)try{headNodes.remove()}finally{}})}(headNodes,$modalObject)}!$modalObject.is(":visible")?setTimeout(function(){if($modalObject.modal("show"),stickyModal){var $background=$(".modal-backdrop.fade.show").last(),z=$background.css("z-index");$modalObject.css("z-index",z-1),$background.css("z-index",z-2)}$modalObject.on("shown.bs.modal",function(e){ajaxCounter<1&&yada.loaderOff()})},100):ajaxCounter<1&&yada.loaderOff(),setTimeout(function(){var hashValue=window.location.hash;if(hashValue.length>1&&!isNaN(hashValue.substring(1)))try{$modalObject.animate({scrollTop:$(hashValue).offset().top},1e3)}catch(e){}else $modalObject.scrollTop()>0&&$modalObject.animate({scrollTop:0},500)},500)}function extractError(responseText){if("string"==typeof responseText){var trimmedText=responseText.trim();if(yada.startsWith(trimmedText,"yadaError:"))return trimmedText.substring(10)}return null}function isNotification(responseHtml){return/class=["'][^"']*yadaNotify[^"']*["']/.test(responseHtml)}yada.enableDropUpload=function(handler,$element){null!=$element&&""!=$element||($element=$("body")),$element.length>1?$element.each(function(){yada.enableDropUpload(handler,$(this))}):$("[data-yadaDropUpload]:not(.yadaDropTarget)",$element).each(function(){const $dropTarget=$(this);$dropTarget.addClass("yadaDropTarget");null!=$dropTarget.data("yadadropupload")&&(handleDropProxy=jQuery.proxy(handleDrop,null,$dropTarget,handler),$dropTarget.on("dragenter dragover",handleDragenterDragover),$dropTarget.on("dragleave dragend drop",handleDragleaveDragend),$dropTarget.on("drop",handleDropProxy))})},yada.isAjaxTriggerKey=function(keyEvent){const key=keyEvent.key;if(null==key)return!0;const input=keyEvent.target,ajaxTriggerKeys=$(input).attr("data-yadaAjaxTriggerKeys");if(null==ajaxTriggerKeys)return!0;const triggerKeys=ajaxTriggerKeys.split("|");for(var i=0;i<triggerKeys.length;i++)if(key==triggerKeys[i])return!0;return!1},yada.enableAjaxInputs=function(){if(!this.enableAjaxInputsDone){var selector="input.yadaAjax:not([type=radio]), input[data-yadaHref]:not([type=radio])";$(document).on("keyup",selector,function(e){if(yada.isAjaxTriggerKey(e)){const $input=$(this);yada.dequeueFunctionCall(this,function(){makeAjaxCall(e,$input,null,!0)})}}),$(selector).each(function(){const $input=$(this),ajaxTriggerKeys=$input.attr("data-yadaAjaxTriggerKeys");if(null==ajaxTriggerKeys||yada.stringContains(ajaxTriggerKeys,"Enter")){const $form=$input.closest("form").not(".yadaEnterNoSubmit");$form.addClass("yadaEnterNoSubmit"),$form.on("submit",function(e){1==$form.data("yadaDoNotSubmitNow")&&(e.preventDefault(),$form.data("yadaDoNotSubmitNow",!1),yada.log("Form submission prevented"),ajaxCounter<1&&yada.loaderOff())}),$form.on("keydown",function(keyEvent){if("Enter"==keyEvent.key){const $target=$(keyEvent.target);if(!$target.hasClass("yadaAjax")&&null==$target.attr("data-yadaHref"))return;if(!($("[type=submit]:enabled",$form).length>0))return;const targetAjaxTriggerKeys=$target.attr("data-yadaAjaxTriggerKeys");(null==targetAjaxTriggerKeys||yada.stringContains(targetAjaxTriggerKeys,"Enter"))&&$form.data("yadaDoNotSubmitNow",!0)}})}}),selector="input.yadaAjax[type=radio], input[data-yadaHref][type=radio]",$(document).on("input",selector,function(e){makeAjaxCall(e,$(this),null,!0)}),this.enableAjaxInputsDone=!0,$(selector).addClass("yadaAjaxed")}},yada.showAjaxFeedback=function(){0==$("#yadaAjaxFeedback").length&&$("body").append("<div id='yadaAjaxFeedback' class='yadaAjaxFeedbackOk'><span class='yadaIcon yadaIcon-ok'></span></div>"),$("#yadaAjaxFeedback").fadeIn(200,function(){$("#yadaAjaxFeedback").fadeOut(800)})},yada.enableAjaxForms=function(handler,$element){null!=$element&&""!=$element||($element=$("body"));var $target=$element.parent();0==$target.length&&($target=$element),yada.enableSubmitButtons($target),$("form.yadaAjax",$target).each(function(){$(this).removeClass("yadaAjax"),yada.enableAjaxForm($(this),handler)}),$(".s_ajaxForm",$target).each(function(){$(this).removeClass("s_ajaxForm"),yada.enableAjaxForm($(this),handler)})},yada.enableSubmitButtons=function($element){$element.find("button[type='submit']").not(".yadaClickedButtonHandler").each(function(){$(this).click(function(){clickedButton=this}),$(this).addClass("yadaClickedButtonHandler")})},yada.disableAjaxForm=function($form){$form.off("submit"),$form.removeClass("yadaAjaxed")},yada.enableAjaxForm=function($form,handler){$form.length>1?$form.each(function(){yada.enableAjaxForm($(this),handler)}):(yada.enableSubmitButtons($form),$form.filter("[data-yadaConfirm]").not(".yadaAjaxed").each(function(){var $thisForm=$(this),$button=$thisForm.find('button[type="submit"][data-yadaConfirm]');0==$button.length&&$thisForm.submit(function(e){if(1!=$thisForm[0].yadaConfirmed){var confirmText=($thisForm=$(this)).attr("data-yadaConfirm");if(null!=confirmText&&""!=confirmText){e.preventDefault();var title=$button.attr("data-yadaTitle"),okButton=$button.attr("data-yadaOkButton")||$button.attr("data-okButton")||yada.messages.confirmButtons.ok,cancelButton=$button.attr("data-yadaCancelButton")||$button.attr("data-cancelButton")||yada.messages.confirmButtons.cancel;return yada.confirm(title,confirmText,function(result){1==result&&($thisForm[0].yadaConfirmed=!0,$thisForm.submit())},okButton,cancelButton),!1}}else $thisForm[0].yadaConfirmed=!1})}),$form.not(".yadaAjaxed").submit(function(e){var $form=$(this);if(0==execSubmitHandlers($form))return!1;if(!e.isDefaultPrevented()){var $formGroup=$form,yadaFormGroup=$form.attr("data-yadaFormGroup");if(null!=yadaFormGroup&&($formGroup=$("form[data-yadaFormGroup="+yadaFormGroup+"]")),!$form.hasClass("yadaAjaxButtonOnly")||null!=clickedButton&&$(clickedButton).hasClass("yadaAjax")){e.preventDefault();var loaderOption=getLoaderOption($form),action=$(this).attr("action"),multipart="multipart/form-data"==$form.attr("enctype");$(".ck-editor__editable").each(function(){this.ckeditorInstance.updateSourceElement()});var data=multipart?new FormData(this):$(this).serializeArray();$formGroup.length>1&&addAllFormsInGroup($formGroup,data,$form);for(var $childForm=$form[0].yadaChildForm;null!=$childForm;){if(multipart)for(var iterator=new FormData($childForm).entries(),iterElem=iterator.next();!iterElem.done;){var pair=iterElem.value;data.append(pair[0],pair[1]),iterElem=iterator.next()}else $.merge(data,$childForm.serializeArray());$childForm=$childForm[0].yadaChildForm}var buttonName=null,buttonValue=null,buttonHistoryAttribute=!1;if(null!=clickedButton){if(0==execSubmitHandlers($(clickedButton)))return!1;buttonName=$(clickedButton).attr("name"),buttonValue=$(clickedButton).attr("value")||"",multipart&&null!=buttonName&&!data.has(buttonName)?data.append(buttonName,buttonValue):multipart||null==buttonName||null!=data[buttonName]||data.push({name:buttonName,value:buttonValue});var buttonAction=$(clickedButton).attr("formaction");null!=buttonAction&&(action=buttonAction),loaderOption=getLoaderOption($(clickedButton))??loaderOption,buttonHistoryAttribute=handlePaginationHistoryAttribute($(clickedButton),$(clickedButton).closest("form"))}multipart||(data=$.param(data));var localClickedButton=clickedButton,method=$form.attr("method")||"POST";return buttonHistoryAttribute||handlePaginationHistoryAttribute($form,$form),yada.ajax(action,data,function(responseText,responseHtml){showFeedbackIfNeeded($form),responseHtml=null!=$(localClickedButton).attr("data-yadaUpdateOnSuccess")?updateOnSuccess($(localClickedButton),responseHtml):updateOnSuccess($form,responseHtml);var formHandlerNames=$form.attr("data-yadaSuccessHandler");void 0===formHandlerNames&&(formHandlerNames=$form.attr("data-successHandler"));var buttonHandlerNames=$(localClickedButton).attr("data-yadaSuccessHandler"),runFormHandler=!0;null!=buttonHandlerNames&&(runFormHandler&=yada.executeFunctionListByName(buttonHandlerNames,$form,responseText,responseHtml,this,localClickedButton)),1==runFormHandler&&null!=formHandlerNames&&yada.executeFunctionListByName(formHandlerNames,$form,responseText,responseHtml,this,localClickedButton),null!=handler&&handler(responseText,responseHtml,this,localClickedButton),deleteOnSuccess($(localClickedButton))||deleteOnSuccess($form)}.bind(this),method,getTimeoutValue($form),loaderOption),clickedButton=null,!1}yada.addFormGroupFields($form,$formGroup)}}),$form.not(".yadaAjaxed").find("button[type='submit']").each(function(){var $button=$(this),confirmText=$button.attr("data-yadaConfirm")||$button.attr("data-confirm");if(null!=confirmText&&""!=confirmText){var title=$button.attr("data-yadaTitle"),okButton=$button.attr("data-yadaOkButton")||$button.attr("data-okButton")||yada.messages.confirmButtons.ok,cancelButton=$button.attr("data-yadaCancelButton")||$button.attr("data-cancelButton")||yada.messages.confirmButtons.cancel;$button.click(function(){return $button=$(this),yada.confirm(title,confirmText,function(result){1==result&&($button.off("click"),$button.click())},okButton,cancelButton),!1})}}),$form.not(".yadaAjaxed").addClass("yadaAjaxed"))},yada.downloadData=function(data,filename,mimeType){var blob=new Blob([data],{type:mimeType});yada.downloadBlob(blob,filename)},yada.downloadBlob=function(blob,filename){var link=document.createElement("a");link.href=URL.createObjectURL(blob),link.download=filename,link.style="display: none;",document.body.appendChild(link),link.click()},yada.ajax=function(url,data,successHandler,method,timeout,loaderOption,asJson,responseType){if("GET"==successHandler||"POST"==successHandler)return void console.error("YadaError: you are forgetting the successHandler in the yada.ajax call; use null for no handler.");null==method&&(method="GET"),null==timeout&&(timeout=0);var processData=!(data instanceof FormData),contentType=void 0;1==asJson?(processData=!1,contentType="application/json;charset=UTF-8",data=JSON.stringify(data)):contentType=!(data instanceof FormData)&&contentType;var $elementLoaderContainers;!0===loaderOption?yada.loaderOff():null==loaderOption||!1===loaderOption?yada.loaderOn():"string"==typeof loaderOption||loaderOption instanceof HTMLElement?$elementLoaderContainers=$(loaderOption):loaderOption instanceof jQuery&&($elementLoaderContainers=loaderOption),$elementLoaderContainers?.each(function(){"static"===$(this).css("position")&&$(this).css("position","relative")}),$elementLoaderContainers?.append($('<div class="yadaElementLoaderOverlay"><div class="yadaElementLoaderIcon"></div></div>'));var xhrFields={};null!=responseType&&(xhrFields.responseType=responseType),ajaxCounter++,$.ajax({type:method,url:url,data:data,processData:processData,contentType:contentType,xhrFields:xhrFields,error:function(jqXHR,textStatus,errorThrown){$(".yadaElementLoaderOverlay",$elementLoaderContainers).remove(),--ajaxCounter<1&&yada.loaderOff();var responseText=null!=jqXHR.responseText?jqXHR.responseText.trim():jqXHR.responseText;if(503==jqXHR.status&&null!=responseText&&yada.startsWith(responseText,"<html"))showFullPage(responseText);else if(404==jqXHR.status)yada.showErrorModal(yada.messages.notfoundError.title,yada.messages.notfoundError.message);else if("timeout"===textStatus)yada.showErrorModal(yada.messages.connectionError.title,yada.messages.connectionError.message);else if("Forbidden"===errorThrown)yada.showErrorModal(yada.messages.forbiddenError.title,yada.messages.forbiddenError.message);else{var title=yada.messages.serverError.title,message=extractError(responseText);null!=message&&""!=message||(message=yada.messages.serverError.message),yada.showErrorModal(title,message+(null!=textStatus&&"error"!=textStatus?" ("+textStatus+")":""))}},success:function(responseText,statusText,jqXHR){$(".yadaElementLoaderOverlay",$elementLoaderContainers).remove(),ajaxCounter--;var responseTrimmed="",responseObject=null;if(responseText instanceof Blob){var contentDisposition=jqXHR.getResponseHeader("Content-Disposition"),filename=yada.getAfter(contentDisposition,"filename=");return yada.downloadBlob(responseText,filename),void(ajaxCounter<1&&yada.loaderOff())}if("string"==typeof responseText?responseTrimmed=responseText.trim():"object"==typeof responseText&&(responseObject=responseText),yada.startsWith(responseTrimmed,"/yada/")&&console.warn("Yada path detected in ajax result: you may need to remove @ResponseBody"),1==yada.showAjaxErrorIfPresent(responseTrimmed,statusText,responseObject))return void(ajaxCounter<1&&yada.loaderOff());if("reload"==responseTrimmed)return void yada.reload();if(yada.startsWith(responseTrimmed,'{"redirect":')){const newTab=function(responseTrimmed){var redirectObject=JSON.parse(responseTrimmed),targetUrl=yada.getAfter(redirectObject.redirect,"redirect:");if("true"!=redirectObject.newTab){const currentServer=window.location.origin,redirectServer=yada.getServerAddress(targetUrl),currentPathSearch=window.location.pathname+window.location.search,redirectPathSearch=yada.removeHash(targetUrl),redirectHashValue=(yada.getHashValue(window.location.hash),yada.getHashValue(targetUrl));return window.location.href=targetUrl,currentServer!=redirectServer&&""!=redirectServer||currentPathSearch==redirectPathSearch&&""!=redirectHashValue&&window.location.reload(!0),!1}ajaxCounter<1&&yada.loaderOff();var win=window.open(targetUrl,"_blank");win?win.focus():alert("Please allow popups for this website");return!0}(responseTrimmed);if(!newTab)return}var responseHtml=$("<div class='yadaAjaxResponseHtml'>").html(responseTrimmed);const getOut=function(responseTrimmed,responseHtml,responseText,url,data,successHandler,method){if("loginSuccess"==responseTrimmed)return $("#loginModal").modal("hide"),yada.loaderOff(),yada.handlePostLoginHandler(responseHtml,responseText),!0;if(openLoginModalIfPresent(responseHtml))return yada.loaderOff(),!0;if(("string"==typeof responseText||responseText instanceof String)&&-1!==responseText.indexOf("s_loginRequested"))return yada.openLoginModal(url,data,successHandler,method),yada.loaderOff(),!0;var pwdChange=$(responseHtml).find("body.yadaChangePassword");if(pwdChange.length>0)return $("#loginModal").remove(),showFullPage(responseText),yada.loaderOff(),!0;return!1}(responseTrimmed,responseHtml,responseText,url,data,successHandler,method);if(!getOut)if(yada.handleModalConfirm(responseHtml,url,data,successHandler,method))ajaxCounter<1&&yada.loaderOff();else{if(yada.initHandlersOn(responseHtml),null!=successHandler&&(yada.isNotifyError(responseHtml)&&1!=successHandler.executeAnyway||successHandler(responseText,responseHtml)),$(".yadafullPage",responseHtml).length>0||$(".s_fullPage",responseHtml).length>0)return showFullPage(responseText),void(ajaxCounter<1&&yada.loaderOff());if(!yada.handleNotify(responseHtml)){var $loadedModalDialog=$(responseHtml).find(".modal > .modal-dialog").first();1!=$loadedModalDialog.length?("closeModal"==responseTrimmed&&$(".modal:visible").modal("hide"),ajaxCounter<1&&yada.loaderOff()):function($loadedModalDialog,responseHtml,responseText){$("#loginModal").remove();var $existingModals=$(".modal.show.yadaAjaxModal:has(.modal-dialog:not(."+yada.stickyModalMarker+"))");0==$existingModals.length&&($existingModals=$(".modal.in.yadaAjaxModal:has(.modal-dialog:not(."+yada.stickyModalMarker+"))"));$existingModals.length>0?($existingModals.modal("hide"),$existingModals.on("hidden.bs.modal",function(e){$existingModals.remove(),showNewModal($loadedModalDialog,responseHtml,responseText)})):showNewModal($loadedModalDialog,responseHtml,responseText)}($loadedModalDialog,responseHtml,responseText)}}},timeout:yada.devMode?0:timeout,traditional:!0,xhr:function(){$(".loader .progress-bar").css("width",0);var xhr=$.ajaxSettings.xhr();return xhr.upload.onprogress=function(evt){$(".loader .progress-bar").css("width",evt.loaded/evt.total*100+"%")},xhr}})},yada.handleModalConfirm=function(responseHtml,url,data,successHandler,type){var $modalConfirm=$(responseHtml).find(".s_modalConfirm .modal");if($modalConfirm.length>0){const $currentModals=$(".modal:not(."+yada.stickyModalMarker+"):visible"),clonedModalContentArray=$currentModals.map(function(){return $(this).find(".modal-content").first().clone(!0,!0)}).get();return $currentModals.modal("hide"),0==$("#yada-confirm .modal").length&&console.error("[yada] No confirm modal found: did you include it?"),$("#yada-confirm .modal").children().remove(),$("#yada-confirm .modal").append($(".modal-dialog",$modalConfirm)),$("#yada-confirm .modal").modal("show"),$("#yada-confirm .okButton").click(function(){yada.postLoginHandler=null,"string"==typeof data?data=yada.addUrlParameterIfMissing(data,"yadaconfirmed","true",!1):(null==data&&(data={}),data.yadaconfirmed=!0),yada.ajax(url,data,successHandler,type)}),$("#yada-confirm .cancelButton").click(function(){$("#yada-confirm .modal").one("hidden.bs.modal",function(e){$currentModals.each(function(index){0==$(this).closest("html").length&&$(".modal-content",this).replaceWith(clonedModalContentArray[index]),$(this).modal("show")}),$("#yada-confirm .modal").off("hidden.bs.modal")})}),!0}return!1},yada.showAjaxErrorIfPresent=function(responseText,statusText,errorObject){var errorMessage=null;return null!=(errorMessage="object"==typeof responseText&&null!=responseText.error?responseText.error:null!=errorObject&&null!=errorObject.yadaError?errorObject.yadaError.error:extractError(responseText))&&(yada.showErrorModal("Error",""!=errorMessage?errorMessage:"Generic Error"),!0)},yada.isNotifyError=function(responseHtml){return $(".yadaNotify span.glyphicon.error",responseHtml).not(".hidden").length>0},yada.handleNotify=function(responseHtml){return!!isNotification(responseHtml)&&($(".modal:visible").modal("hide"),$("#yada-notification").children().remove(),$("#yada-notification").append(notification),setTimeout(function(){$("#yada-notification").on("show.bs.modal",function(e){notification.hasClass("yadaLoaderKeep")||ajaxCounter<1&&yada.loaderOff()}),$("#yada-notification").modal("show")},200),!0)},yada.getEmbeddedResult=function(html){var result={};return $(".yadaResponseData table tr",html).each(function(){var key=$(".s_key",this).text(),value=$(".s_value",this).text();result[key]=value}),result}}(window.yada=window.yada||{}),function(yada){"use strict";yada.cookieBannerAcceptClass="cookieBannerAcceptClass";function bodyClickHandler(e){"privacyLink"!=e.target.className&&(document.body.removeEventListener("click",bodyClickHandler),bannerDismiss())}function scrollHandler(e){window.top.removeEventListener("scroll",scrollHandler),bannerDismiss()}function bannerDismiss(){document.cookie="yadacba=true; expires=Thu, 01 Jan 2100 00:00:00 UTC; path=/";var banner=document.getElementById("yadaCookieBanner");banner&&banner.parentNode.removeChild(banner)}yada.cookieBanner=function(infoText,acceptButtonText,noHideOnScroll,noHideOnClick){if(document.cookie.indexOf("yadacba=true")<0){null==infoText&&(infoText="This site uses cookies. Do not use this site if you don't agree to the use of cookies."),null==acceptButtonText&&(acceptButtonText="Accept");var bannerHtml='<div id="yadaCookieBanner"><p>'+infoText+"</p>";""!=acceptButtonText&&(bannerHtml+='<a class="'+yada.cookieBannerAcceptClass+'" href="#">'+acceptButtonText+"</a>"),bannerHtml+="</div>",document.body.insertAdjacentHTML("afterbegin",bannerHtml),1==noHideOnClick?$("."+yada.cookieBannerAcceptClass).click(bodyClickHandler):document.body.addEventListener("click",bodyClickHandler),1!=noHideOnScroll&&window.addEventListener("scroll",scrollHandler)}}}(window.yada=window.yada||{}),function(yada){"use strict";function makeExtraButtonHandler(extraButtonDef,$button,dataTable,$table){$button.click(function(e){e.preventDefault();var isRowIcon=$(this).hasClass("yadaRowCommandButton"),buttonUrl=extraButtonDef.url,ids=[],id=yada.getHashValue($(this).attr("href")),totElements=1;if(isRowIcon)ids=[id];else{var $checks=$table.find("tbody [type='checkbox']:checked");totElements=$checks.length,ids=$checks.map(function(){var id=$(this).parents("tr").attr("id");return null==id&&alert('Internal Error-ID missing in row: did you forget "DT_RowId" in the Model?'),yada.getHashValue(id)}).get()}var noLoader=extraButtonDef.noLoader||!1;if("function"==typeof extraButtonDef.url)if(isRowIcon){var rowData=dataTable.row(this.parentElement).data();buttonUrl=buttonUrl(rowData)}else{rowData=dataTable.rows();buttonUrl=buttonUrl(rowData,ids)}var idName=null==extraButtonDef.idName?"id":extraButtonDef.idName,param=ids.length>1?ids:ids[0];if(!1===extraButtonDef.ajax)return"function"!=typeof extraButtonDef.url&&(buttonUrl=yada.addOrUpdateUrlParameter(buttonUrl,idName,param)),void(null!=extraButtonDef.windowName?window.open(buttonUrl,extraButtonDef.windowName,extraButtonDef.windowFeatures):window.location.replace(buttonUrl));var requestData={};""!=idName&&(requestData[idName]=param);var handler=function(responseText,responseHtml){dataTable.draw(!1),yada.datatableDrawOnModalClose(dataTable),recursiveEnableAjaxForm(responseText,responseHtml)};if(1!=extraButtonDef.confirm)yada.ajax(buttonUrl,requestData,handler,null,null,noLoader);else{const confirmTitle=extraButtonDef.confirmTitle||null;var confirmMessage=null;if(totElements<2){var rowIndex=dataTable.row(this.parentElement).index();isRowIcon||(rowIndex=dataTable.row($table.find("tbody [type='checkbox']:checked").parent()).index());var nameColumn=extraButtonDef.confirmNameColumn||3;const rowName=dataTable.cell(rowIndex,nameColumn).data();confirmMessage=(confirmMessage=extraButtonDef.confirmOneMessage||"Do you want to delete {0}?").replace("{0}",rowName)}else confirmMessage=extraButtonDef.confirmManyMessage||`Do you want to delete ${totElements} elements?`;yada.confirm(confirmTitle,confirmMessage,function(result){1==result&&yada.ajax(buttonUrl,requestData,handler,null,null,noLoader)},extraButtonDef.confirmButtonText,extraButtonDef.abortButtonText)}})}function recursiveEnableAjaxForm(responseText,responseHtml){yada.enableAjaxForm($("form.yadaAjaxForm",responseHtml),recursiveEnableAjaxForm),yada.enableAjaxForm($("form.s_ajaxForm",responseHtml),recursiveEnableAjaxForm)}yada.dataTableCrud=function($table,dataUrl,dataAttributes,editDef,deleteDef,order,pageLength,languageUrl,extraButtons,removeCheckbox){if(null!=$table&&"object"==typeof $table&&1==$table.length&&"object"==typeof $table[0])if(null!=dataUrl&&"string"==typeof dataUrl)if(Array.isArray(dataAttributes)&&0!=dataAttributes.length)if(null==editDef||"object"==typeof editDef&&!Array.isArray(editDef))if(null==deleteDef||"object"==typeof deleteDef&&!Array.isArray(deleteDef))if(Array.isArray(order)&&0!=order.length)if("number"==typeof pageLength)if(null==languageUrl||"string"==typeof languageUrl)if(null==extraButtons||Array.isArray(extraButtons)&&0!=extraButtons.length&&"object"==typeof extraButtons[0]&&!Array.isArray(extraButtons[0]))if(null==removeCheckbox||"boolean"==typeof removeCheckbox){var tableId=$table.attr("id");if(null!=tableId){var totColumns=$("th",$table).length,neededColumns=dataAttributes.length+2+(removeCheckbox?0:1);totColumns!=neededColumns&&yada.showErrorModal("Internal Error","Table '"+$table[0].id+"' has "+totColumns+" columns but "+neededColumns+" where expected - (ignored)");var columnDef=[{data:null,defaultContent:"",className:"control",orderable:!1,searchable:!1,visible:!1}];removeCheckbox||columnDef.push({data:null,name:"_yadaSelectionColumn",orderable:!1,searchable:!1,render:function(data,type,row){return"display"===type?'<input type="checkbox" class="yadaCheckInCell s_rowSelector"/>':data},width:"50px",className:"yadaCheckInCell"});for(var i=0;i<dataAttributes.length;i++){var field=dataAttributes[i],fieldDef={data:field,defaultContent:"---",name:field,orderable:!0,searchable:!0};"object"==typeof field&&null==(fieldDef=field).name&&"string"==typeof fieldDef.data&&(fieldDef.name=fieldDef.data),columnDef.push(fieldDef)}columnDef.push({data:null,className:"yadaCommandButtonCell",name:"_yadaCommandColumn",orderable:!1,searchable:!1,width:"50px",render:function(data,type,row){var rowId=yada.getHashValue(data.DT_RowId);if("display"===type){for(var buttons="",i=0;null!=extraButtons&&i<extraButtons.length;i++){var displayIconOnRow=extraButtons[i].showRowIcon;null==displayIconOnRow&&(displayIconOnRow=extraButtons[i].noRowIcon),"function"==typeof displayIconOnRow&&(displayIconOnRow=displayIconOnRow(data,row)),null==displayIconOnRow&&(displayIconOnRow=!0),null!=displayIconOnRow&&null!=extraButtons[i].noRowIcon&&null==extraButtons[i].showRowIcon&&(displayIconOnRow=!displayIconOnRow),displayIconOnRow?buttons+='<a class="yadaTableExtraButton'+i+' yadaRowCommandButton" href="#'+rowId+'" title="'+extraButtons[i].title+'">'+extraButtons[i].icon+"</a>":"disabled"==displayIconOnRow&&(buttons+='<span class="yadaTableExtraButton'+i+' yadaRowCommandButton disabled" title="'+extraButtons[i].title+'">'+extraButtons[i].icon+"</span>")}return null!=editDef&&(buttons+='<a class="s_editRow yadaRowCommandButton" href="#'+rowId+'" title="'+editDef.title+'"><i class="yadaIcon yadaIcon-edit"></i></a>'),null!=deleteDef&&(buttons+='<a class="s_deleteRow yadaRowCommandButton" href="#'+rowId+'" title="'+deleteDef.title+'"><i class="yadaIcon yadaIcon-delete"></i></a>'),buttons}return data}});var dataTable=$table.DataTable({responsive:!0,pageLength:pageLength,orderMulti:order.length>1,order:order,columns:columnDef,serverSide:!0,ajax:function(data,callback,settings){const addedData=$("form.yada_dataTables_"+tableId).serializeArray();let extraParam=data.extraParam={};addedData.forEach(paramObj=>{const paramName=paramObj.name,paramValue=paramObj.value;extraParam[paramName]||(extraParam[paramName]=[]),extraParam[paramName].push(paramValue)});const noLoader=$table.hasClass("noLoader")||$table.hasClass("yadaNoLoader");yada.ajax(dataUrl,jQuery.param(data),callback,"POST",null,noLoader)},language:{url:languageUrl}});if(dataTable.on("draw.dt",function(){var thisDataTable=$(this).DataTable();$("a.s_deleteRow",this).click(function(e){e.preventDefault();var id=yada.getHashValue($(this).attr("href")),idName=deleteDef.idName||"id",nameColumn=deleteDef.nameColumn||3,requestData={},noLoader=deleteDef.noLoader||!1;requestData[idName]=id;var $row=$(this).parents("tr"),confirmTitle=deleteDef.confirmTitle||null,confirmButtonText=deleteDef.confirmButtonText||"Delete",abortButtonText=deleteDef.abortButtonText||"Cancel",confirmMessage=deleteDef.confirmOneMessage||"Do you want to delete {0}?",rowName=thisDataTable.cell($row,nameColumn).data();confirmMessage=confirmMessage.replace("{0}",rowName),yada.confirm(confirmTitle,confirmMessage,function(result){1==result&&yada.ajax(deleteDef.url,requestData,function(){thisDataTable.draw(!1)},null,null,noLoader)},confirmButtonText+' "'+rowName+'"',abortButtonText)}),$("a.s_editRow",this).click(function(e){e.preventDefault();var id=yada.getHashValue($(this).attr("href")),idName=editDef.idName||"id";if(1==editDef.noAjax){const targetUrl=yada.addOrUpdateUrlParameter(editDef.url,idName,id);return void(window.location.href=targetUrl)}var requestData={},noLoader=editDef.noLoader||!1;requestData[idName]=id;yada.ajax(editDef.url,requestData,function(responseText,responseHtml){yada.datatableDrawOnModalClose(thisDataTable),recursiveEnableAjaxForm(responseText,responseHtml)},null,null,noLoader)});for(var i=0;null!=extraButtons&&i<extraButtons.length;i++)makeExtraButtonHandler(extraButtons[i],$("a.yadaTableExtraButton"+i,this),thisDataTable,$(this));$(".s_columnSelector",this).change(function(){var checked=$(this).prop("checked");$(this).parents("table").find("td input.yadaCheckInCell[type='checkbox']").prop("checked",checked).change()}),$(".s_rowSelector",this).change(function(){var totChecked=0;if($(".s_rowSelector").each(function(index){var checked=$(this).prop("checked");totChecked+=checked?1:0}),1==totChecked)$(this).parents("div.yadaTableBlock").find("div.yadaTableToolbar .yadaTableSinglerowButton").removeClass("disabled"),$(this).parents("div.yadaTableBlock").find("div.yadaTableToolbar .yadaTableMultirowButton:not(.yadaTableSinglerowButton)").addClass("disabled"),$(this).parents("div.yadaTableBlock").find("div.yadaTableToolbar .s_singlerowButton").removeClass("disabled"),$(this).parents("div.yadaTableBlock").find("div.yadaTableToolbar .s_multirowButton:not(.s_singlerowButton)").addClass("disabled");else if(totChecked>1)$(this).parents("div.yadaTableBlock").find("div.yadaTableToolbar .yadaTableMultirowButton").removeClass("disabled"),$(this).parents("div.yadaTableBlock").find("div.yadaTableToolbar .yadaTableSinglerowButton:not(.yadaTableMultirowButton)").addClass("disabled"),$(this).parents("div.yadaTableBlock").find("div.yadaTableToolbar .s_multirowButton").removeClass("disabled"),$(this).parents("div.yadaTableBlock").find("div.yadaTableToolbar .s_singlerowButton:not(.s_multirowButton)").addClass("disabled");else{$(this).parents("div.yadaTableBlock").find("div.yadaTableToolbar .btn:not(.s_addButton):not(.yadaTableAlwaysButton)").addClass("disabled")}})}),null!=editDef)$table.parents(".yadaTableBlock").find(".yadaTableToolbar a.s_addButton").click(function(e){e.preventDefault();var noLoader=editDef.noLoader||!1;yada.ajax(editDef.url,null,function(responseText,responseHtml){yada.datatableDrawOnModalClose(dataTable),recursiveEnableAjaxForm(responseText,responseHtml)},null,null,noLoader)}),$table.parents(".yadaTableBlock").find(".yadaTableToolbar a.s_editButton").click(function(e){e.preventDefault();var id=yada.getHashValue($table.find("tbody [type='checkbox']:checked").parents("tr").attr("id")),idName=editDef.idName||"id";if(1==editDef.noAjax){const targetUrl=yada.addOrUpdateUrlParameter(editDef.url,idName,id);return void(window.location.href=targetUrl)}var requestData={};requestData[idName]=id;var noLoader=editDef.noLoader||!1;yada.ajax(editDef.url,requestData,function(responseText,responseHtml){yada.datatableDrawOnModalClose(dataTable),recursiveEnableAjaxForm(responseText,responseHtml)},null,null,noLoader)});if(null!=deleteDef)$table.parents(".yadaTableBlock").find(".yadaTableToolbar a.s_deleteButton").click(function(e){e.preventDefault();var $checks=$table.find("tbody [type='checkbox']:checked"),totElements=$checks.length,ids=$checks.map(function(){var id=$(this).parents("tr").attr("id");return null==id&&alert('Internal Error-ID missing in row: did you forget "DT_RowId" in the Model?'),yada.getHashValue(id)}).get(),confirmTitle=deleteDef.confirmTitle||null,confirmMessage=deleteDef.confirmManyMessage||"Do you want to delete {0} elements?";confirmMessage=confirmMessage.replace("{0}",totElements);var confirmButtonText=deleteDef.confirmButtonText||"Delete",abortButtonText=deleteDef.abortButtonText||"Cancel",noLoader=deleteDef.noLoader||!1;yada.confirm(confirmTitle,confirmMessage,function(result){if(1==result){var requestData={};requestData[deleteDef.idName||"id"]=ids;var handler=function(){dataTable.draw(!1)};handler.executeAnyway=!0,yada.ajax(deleteDef.url,requestData,handler,null,null,noLoader)}},confirmButtonText+" "+totElements,abortButtonText)});return null!=extraButtons&&function(extraButtons,dataTable,$table){for(var sortedExtraButtons=extraButtons.sort(function(a,b){return Math.sign(a.toolbarPosition-b.toolbarPosition)}),i=0;i<sortedExtraButtons.length;i++){var btndef=sortedExtraButtons[i];if(null!=btndef.toolbarClass){var buttonHtml='<a class="btn btn-primary '+("yadaTableAlwaysButton"!=btndef.toolbarClass?"disabled":"")+" "+btndef.toolbarClass+" s_toolbarExtraButton"+i+'" href="#" title="'+btndef.title+'">'+btndef.icon+" <span>"+btndef.toolbarText+"</span></a> ",pos=btndef.toolbarPosition,$yadaTableToolbar=$table.parents(".yadaTableBlock").find(".yadaTableToolbar"),$existing=$("a.btn",$yadaTableToolbar);null==pos||$existing.length<=pos?$yadaTableToolbar.append(buttonHtml):$existing.eq(pos).before(buttonHtml),makeExtraButtonHandler(btndef,$(".s_toolbarExtraButton"+i,$yadaTableToolbar),dataTable,$table)}}}(extraButtons,dataTable,$table),dataTable}console.error("yada.datatables: $table must have a valid id attribute")}else console.error("yada.datatables: removeCheckbox must be a boolean or null");else console.error("yada.datatables: extraButtons must be a non-empty array of objects or null");else console.error("yada.datatables: languageUrl must be a string or null");else console.error("yada.datatables: pageLength must be an integer");else console.error("yada.datatables: order must be a non-empty array");else console.error("yada.datatables: deleteDef must be an object or null");else console.error("yada.datatables: editDef must be an object or null");else console.error("yada.datatables: dataAttributes must be a non-empty array");else console.error("yada.datatables: dataUrl must be a string")},yada.datatableDrawOnModalClose=function(dataTable){setTimeout(function(){$(".modal.yadaAjaxModal").on("hide.bs.modal",function(){dataTable.draw(!1),$(".modal.yadaAjaxModal").unbind("hide.bs.modal")})},0)}}(window.yada=window.yada||{}),function(yada){"use strict";yada.suggestionList=function(event){const input=event.target,$input=$(input),$dropdown=$input.closest(".dropdown"),inputValue=$.trim(input.value),suggestionUrl=$input.attr("data-yadaSuggestionListUrl"),requestIdNameOverride=$input.attr("data-yadaSuggestionRequestIdNameOverride");if(0==$dropdown.length)return void console.error("Missing dropdown element around input tag - aborting suggestions");const key=event.key;if("ArrowDown"==key||"ArrowUp"==key){const $suggestionList=$(input).closest(".dropdown").find(".jsYadaSuggestionList:visible"),tot=$("a",$suggestionList).length;if(tot>0){var toFocus="ArrowDown"==key?0:tot-1;$("a",$suggestionList).get(toFocus).focus()}return}if(yada.isAjaxTriggerKey(event))return;const data={prefix:inputValue};yada.dequeueFunctionCall(input,function(){yada.ajax(suggestionUrl,data,function(responseText,responseHtml){const $newSuggestionList=responseHtml.find(".jsYadaSuggestionList");$dropdown.find(".jsYadaSuggestionList").replaceWith($newSuggestionList),$("a",$newSuggestionList).click(function(){const clickedValue=$(this).text();$input.val(clickedValue);const yadaRequestData=input.yadaRequestData||{},suggestionIdValue=$(this).attr("data-id");if(suggestionIdValue){yadaRequestData[requestIdNameOverride||$(this).attr("data-idname")||"id"]=suggestionIdValue,input.yadaRequestData=yadaRequestData}var e=jQuery.Event("keyup");e.key="Enter",$input.trigger(e)});const $toggler=$dropdown.find("div[data-bs-toggle=dropdown]"),dropdownApi=new bootstrap.Dropdown($toggler[0]);$("a",$newSuggestionList).length>0?dropdownApi.show():dropdownApi.hide()},null,null,!0)})},yada.updateInputCounter=function($inputTag,$counterDiv){const maxlength=$inputTag.attr("maxlength");var currentLength=$inputTag.val().length;null!=maxlength?($("span:first-child",$counterDiv).text(currentLength),$("span:last-child",$counterDiv).text(maxlength),$inputTag.on("input",function(){currentLength=this.value.length,$("span:first-child",$counterDiv).text(currentLength)})):console.error("Missing maxlength attribute on input tag with data-yadaTagId="+$inputTag.attr("data-yadaTagId"))},yada.initYadaDialect=function(){function changeNumericField($inputTag,valueToAdd){const min=Number($inputTag.attr("min")||Number.MIN_SAFE_INTEGER),max=Number($inputTag.attr("max")||Number.MAX_SAFE_INTEGER);var mousePressed=!0;function changeValue(){var value=Number($inputTag.val());valueToAdd>0&&value>max-valueToAdd||valueToAdd<0&&value<min-valueToAdd||$inputTag.val(value+valueToAdd)}function reschedule(){mousePressed&&(changeValue(),setTimeout(reschedule,100))}$(this).on("mouseup mouseleave mousedrag",function(){$(this).off("mouseup mouseleave mousedrag"),mousePressed=!1;const e=jQuery.Event("keyup");e.key="Enter",$inputTag.trigger(e)}),changeValue(),setTimeout(function(){reschedule()},500)}$(document).on("mousedown",".yadaInputNumericIncrement",function(){const $inputTag=$(this).siblings("input.yadaInputNumber"),step=Number($inputTag.attr("step")||1);changeNumericField.bind(this)($inputTag,step)}),$(document).on("mousedown",".yadaInputNumericDecrement",function(){const $inputTag=$(this).siblings("input.yadaInputNumber"),step=Number($inputTag.attr("step")||1);changeNumericField.bind(this)($inputTag,-step)}),$(document).on("input","input.yadaInputNumber",function(){const $inputTag=$(this),minText=$inputTag.attr("min"),maxText=$inputTag.attr("max"),value=Number($inputTag.val());if(null!=minText){const min=Number(minText);value<min&&$inputTag.val(min)}if(null!=maxText){const max=Number(maxText);value>max&&$inputTag.val(max)}})}}(window.yada=window.yada||{}),function(yada){"use strict";yada.baseLoaded=!0,yada.devMode=!1,yada.baseUrl=null,yada.resourceDir=null;var loaderStart=0;yada.messages=yada.messages||{},yada.messages.connectionError=yada.messages.connectionError||{title:"Connection Error",message:"Failed to contact server - please try again later"},yada.messages.forbiddenError=yada.messages.forbiddenError||{title:"Authorization Error",message:"You don't have permission to access the requested page"},yada.messages.notfoundError=yada.messages.notfoundError||{title:"Not Found",message:"The page you requested was not found on the server"},yada.messages.serverError=yada.messages.serverError||{title:"Server Error",message:"Something is wrong - please try again later"},yada.messages.confirmButtons=yada.messages.confirmButtons||{ok:"Ok",cancel:"Cancel"},yada.messages.singleFileOnly=yada.messages.singleFileOnly||{title:"File Upload Error",message:"Only one file can be uploaded at a time"},yada.messages.uploadAccept=yada.messages.uploadAccept||{title:"File Upload Error",message:"Invalid file type"};var siteMatcher=RegExp("(?:http.?://)?([^/:]*).*");const findSelector="yadaFind:",parentSelector="yadaParents:",siblingsSelector="yadaSiblings:",closestFindSelector="yadaClosestFind:",siblingsFindSelector="yadaSiblingsFind:",sessionStorageKeyTimezone="yada.timezone.sent",scrollTopParamName="scrolltop";function initHandlers(){$("body").on("click",":not(form).yadaShowLoader",yada.loaderOn),$("body").on("submit",".yadaShowLoader",yada.loaderOn),$("body").on("click",".s_showLoaderClick",yada.loaderOn),$("body").on("submit",".s_showLoaderForm",yada.loaderOn),yada.enableScrollTopButton(),yada.initHandlersOn(),$(window).bind("pageshow",function(event){event.originalEvent.persisted&&yada.loaderOff()})}yada.stickyModalMarker="yadaStickyModal",$(document).ready(function(){handleScrollTop(),initHandlers(),"function"==typeof yada.initYadaDialect&&yada.initYadaDialect();if(!sessionStorage.getItem(sessionStorageKeyTimezone)){const data={timezone:Intl.DateTimeFormat().resolvedOptions().timeZone};jQuery.post("/yadaTimezone",data,function(){sessionStorage.setItem(sessionStorageKeyTimezone,!0)})}}),yada.initHandlersOn=function($element){yada.enableParentForm($element),yada.enableShowPassword($element),yada.enableRefreshButtons($element),yada.enableConfirmLinks($element),yada.enableHelpButton($element),yada.enableTooltip($element),yada.makeCustomPopover($element),"function"==typeof yada.initAjaxHandlersOn&&yada.initAjaxHandlersOn($element),yada.enableHashing($element),yada.enableFormGroup($element)},yada.log=function(message){yada.devMode&&console.log("[yada] "+message)},yada.loaderOn=function(){loaderStart=Date.now(),$(".loader").show()},yada.loaderOff=function(){var elapsedMillis=Date.now()-loaderStart;elapsedMillis>200?$(".loader").hide():setTimeout(function(){$(".loader").hide()},200-elapsedMillis)},yada.executeFunctionListByName=function(functionList,thisObject){for(var args=Array.prototype.slice.call(arguments,2),result=!0,functionArray=yada.listToArray(functionList),i=0;i<functionArray.length;i++){const functionResult=yada.executeFunctionByName(functionArray[i],thisObject,...args);if(null==functionResult){result=null;break}result&&=functionResult}return null==result&&functionArray.length>1&&(result=yada.executeFunctionByName(functionList,thisObject,...args)),null==result&&yada.log("Invalid function list: "+functionList),result},yada.executeFunctionByName=function(functionName,thisObject){for(var context=window,args=Array.prototype.slice.call(arguments,2),namespaces=functionName.split("."),func=namespaces.pop(),i=0;i<namespaces.length&&null!=context;i++)context=context[namespaces[i]];var functionObject=context?context[func]:null;if(null==functionObject)try{var functionBody=functionName.trim();yada.startsWith(functionName,"function(")&&(functionBody=functionName.replace(new RegExp("(?:function\\s*\\(\\)\\s*{)?([^}]+)}?"),"$1")),functionObject=new Function("responseText","responseHtml","link",functionBody)}catch(error){return null}const returnValue=functionObject?.apply(thisObject,args);return returnValue??!0},yada.enableHashing=function($element){null==$element&&($element=$("body")),$("[data-yadaHash]",$element).not(".yadaHashed").click(function(){var hashValue=$(this).attr("data-yadaHash");const newUrl=yada.replaceHash(window.location.href,hashValue);history.pushState({yadaHash:!0,hashValue:hashValue},null,newUrl)}).addClass("yadaHashed")},yada.enableTooltip=function($element){null==$element&&($element=$("body")),$(".s_tooltip",$element).tooltip&&$(".s_tooltip",$element).tooltip()},yada.enableHelpButton=function($element){null==$element&&($element=$("body")),$(".yadaHelpButton",$element).popover&&$(".yadaHelpButton",$element).popover()},yada.reload=function(){yada.stringContains(window.location.href,"#")?window.location.reload(!0):window.location.replace(window.location.href)},yada.enableRefreshButtons=function($element){null==$element&&($element=$("body")),$(".yadaRefresh",$element).not(".yadaRefreshed").each(function(){var handlerName=$(this).attr("data-yadaRefreshHandler");void 0===handlerName&&(handlerName=$(this).attr("data-handler"));var dataHandler=window[handlerName];"function"==typeof dataHandler&&($(this).click(dataHandler),dataHandler()),$(this).addClass("yadaRefreshed")})},yada.enableScrollTopButton=function(){const $scrollTopButton=$(".yadaScrollTop");$scrollTopButton.length>0&&($scrollTopButton.off().click(function(e){e.preventDefault(),window.scrollTo({top:0,left:0,behavior:"smooth"})}),$(document).on("scroll",function(){const visible=$scrollTopButton.is(":visible");$(this).scrollTop()>800?visible||$scrollTopButton.fadeIn():visible&&$scrollTopButton.fadeOut()}))},yada.dequeueFunctionCall=function(domElement,functionToCall){var callTimeout=200;null!=domElement.yadaDequeueFunctionCallRunning&&(callTimeout=2e3),clearTimeout(domElement.yadaDequeueFunctionTimeoutHandler),domElement.yadaDequeueFunctionTimeoutHandler=setTimeout(function(){domElement.yadaDequeueFunctionCallRunning=!0,functionToCall.bind(domElement)(),domElement.yadaDequeueFunctionCallRunning=null},callTimeout)};const dequeueFunctionData=new Map;function handleScrollTop(){const scrollTopValue=yada.getUrlParameter(window.location.href,scrollTopParamName);null!=scrollTopValue&&($(window).scrollTop(scrollTopValue),history.replaceState(null,"",yada.removeUrlParameter(window.location.href,scrollTopParamName)))}function handleConfirmation(e){var $element=$(e.target),confirmText=$element.attr("data-yadaConfirm")||$element.attr("data-confirm");if(null!=confirmText){e.preventDefault();var title=$element.attr("data-yadaTitle"),okButton=$element.attr("data-yadaOkButton")||$element.attr("data-okButton")||yada.messages.confirmButtons.ok,cancelButton=$element.attr("data-yadaCancelButton")||$element.attr("data-cancelButton")||yada.messages.confirmButtons.cancel,okShowsPreviousModal=null==$element.attr("data-yadaOkShowsPrevious")||"true"==$element.attr("data-yadaOkShowsPrevious");yada.confirm(title,confirmText,function(result){if(1==result)if($element.is("a")){var href=$element.attr("href");href&&(window.location.href=href)}else $element.off("submit",handleConfirmation),$element.submit()},okButton,cancelButton,okShowsPreviousModal)}}function escapeRegExp(string){return string.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function makePopoverInsertedFunction(trigger,popoverObject,contentInstanceId,listener){return function(e){const $popoverElement=$("#"+contentInstanceId).closest(".popover");$popoverElement.find("button.yadaclose").click(function(){popoverObject.hide()}),$popoverElement.find("[data-bs-toggle=popover]").click(function(){popoverObject.hide()}),null!=listener&&yada.executeFunctionByName(listener,trigger,e,$popoverElement,popoverObject)}}function makePopoverShownFunction(trigger,popoverObject,contentInstanceId,listener){return function(e){const $popoverElement=$("#"+contentInstanceId).closest(".popover");$(".popover.show").not($popoverElement).popover("hide"),null!=listener&&yada.executeFunctionByName(listener,trigger,e,$popoverElement,popoverObject)}}function showNotificationModal(title,message,severity,redirectUrl){$(".modal").modal("hide"),yada.loaderOff();$("#yada-notification .modal-title").text(title),$("#yada-notification .modal-body").html("<p>"+message+"</p>");var icon=$('<i class="yadaIcon yadaIcon-'+{ok:"ok",info:"info",error:"error"}[severity]+" "+severity+'"></i>');$("#yada-notification h4 i.yadaIcon").replaceWith(icon),$("#yada-notification:hidden").modal("show"),null!=redirectUrl&&$("#yada-notification").on("hidden.bs.modal",function(e){window.location.replace(redirectUrl)})}function hideAllModals($notThese){const $modals=$("#loginModal:visible").add($(".modal.show."+yada.markerAjaxModal+":visible")).add($("#yada-notification:visible")).add($("#yada-confirm:visible")).not($notThese);return $modals.css("display","none"),$modals}function checkMatchingPasswords($oneForm){var $pwd=$("input[name=password]",$oneForm),$check=$("input[name=confirmPassword]",$oneForm),$submit=$('button[type="submit"]',$oneForm);if(null!=$pwd&&null!=$check){var v1=$pwd.val(),v2=$check.val();""!=v1&&""!=v2&&v1==v2?resetPasswordError($oneForm):($submit.attr("disabled","disabled"),""==v1&&""==v2||($oneForm.addClass("has-error"),$oneForm.addClass("yada-password-mismatch")))}}function resetPasswordError($oneForm){$("input[name=password]",$oneForm),$("input[name=confirmPassword]",$oneForm);$('button[type="submit"]',$oneForm).removeAttr("disabled"),$oneForm.removeClass("has-error"),$oneForm.removeClass("yada-password-mismatch")}function removeFormField(form,field){if(field.disabled||("radio"==field.type||"checkbox"==field.type)&&!field.checked)return;const fieldName=field.name;Array.prototype.forEach.call(form.elements,function(element){""!=fieldName&&element.name===fieldName&&element.parentNode.removeChild(element)})}function addMissingFormField(form,field){const existingField=form.elements[field.name];null!=existingField&&1!=existingField.disabled||form.appendChild(field)}function getYadaEventHandlers($element){const allHandlers=$element.attr("data-yadaEventHandlers"),result=[];if(yada.stringNotEmpty(allHandlers)){const segments=allHandlers.split(/ *, */);for(var i=0;i<segments.length;i++){const listener={},nameValue=segments[i].split(/ *: */);2==nameValue.length&&(listener.event=nameValue[0],listener.handler=nameValue[1],result[nameValue[0]]=listener)}}return result}function copyUsingFallback(){try{const textArea=document.createElement("textarea");textArea.value=text,textArea.style.position="fixed",textArea.style.top="0",textArea.style.left="0",textArea.style.width="2em",textArea.style.height="2em",textArea.style.padding="0",textArea.style.border="none",textArea.style.outline="none",textArea.style.boxShadow="none",textArea.style.background="transparent",document.body.appendChild(textArea),textArea.focus(),textArea.select();const successful=document.execCommand("copy");document.body.removeChild(textArea),successful&&showFeedback?yada.showAjaxFeedback():successful||yada.log("Could not copy text using execCommand")}catch(err){yada.log("Could not copy text using fallback method: "+err)}}yada.dequeueFunctionCallByKey=function(key,functionToCall,delay=200){if("function"!=typeof functionToCall)throw new Error("functionToCall must be a valid function.");let data=dequeueFunctionData.get(key);data||(data={timeoutHandler:null,isRunning:!1},dequeueFunctionData.set(key,data)),data.timeoutHandler&&clearTimeout(data.timeoutHandler),data.timeoutHandler=setTimeout(()=>{try{functionToCall()}finally{dequeueFunctionData.delete(key)}},delay)},$.fn.yadaInputFilter=function(inputFilter){return this.on("input keydown keyup mousedown mouseup select contextmenu drop",function(){inputFilter(this.value)?(this.oldValue=this.value,this.oldSelectionStart=this.selectionStart,this.oldSelectionEnd=this.selectionEnd):this.hasOwnProperty("oldValue")?(this.value=this.oldValue,this.setSelectionRange(this.oldSelectionStart,this.oldSelectionEnd)):this.value=""})},yada.addScrollTop=function(url){return yada.addOrUpdateUrlParameter(url,scrollTopParamName,$(window).scrollTop())},yada.hasUrlParameter=function(url,param){return null!=yada.getUrlParameter(url,param)},yada.removeUrlParameter=function(url,param,value){null==value&&(value="[^&]*");var regex=new RegExp("[?|&]"+param+"="+value+"&?","g");url=url.replace(regex,"&"),yada.endsWith(url,"&")&&(url=url.substring(0,url.length-1));var pos=url.indexOf("&");return pos>-1&&-1==url.indexOf("?")&&(url=yada.replaceAt(url,pos,"?")),url},yada.removeUrlParameters=function(url,param){var regex=new RegExp("[?|&]"+param+"=[^&]+&?","g");url=url.replace(regex,"&"),yada.endsWith(url,"&")&&(url=url.substring(0,url.length-1));var pos=url.indexOf("&");return-1==url.indexOf("?")&&pos>-1&&(url=yada.replaceAt(url,pos,"?")),url},yada.addUrlParameter=function(url,param,value,addQuestionMark){addQuestionMark=0!=addQuestionMark;var anchor="";if(url){var anchorPos=url.indexOf("#");anchor=anchorPos>-1?url.substring(anchorPos):"",-1==(url=anchorPos>-1?url.substring(0,anchorPos):url).indexOf("?")&&1==addQuestionMark?url+="?":url+="&"}else 1==addQuestionMark&&(url="?");return url+=encodeURIComponent(param),null!=value&&(url=url+"="+encodeURIComponent(value)),url+anchor},yada.addOrUpdateUrlParameter=function(url,param,value,addQuestionMark){return null==param||""==param?url:yada.hasUrlParameter(url,param)?yada.updateUrlParameter(url,param,value):yada.addUrlParameter(url,param,value,addQuestionMark)},yada.addUrlParameterIfMissing=function(url,param,value,addQuestionMark){return yada.hasUrlParameter(url,param)?url:yada.addUrlParameter(url,param,value,addQuestionMark)},yada.updateUrlParameter=function(url,param,value){var regex=new RegExp("([?|&]"+param+"=)[^&]+");return url.replace(regex,"$1"+value)},yada.getUrlParameter=function(url,varName){var queryStr=url+"&",regex=new RegExp(".*?[&\\?]"+varName+"=(.*?)[&#].*"),val=queryStr.replace(regex,"$1");return val==queryStr?null:unescape(val)},yada.getUrlParameters=function(url){return url=yada.getAfter(url,"?",0),url=yada.removeHash(url),new URLSearchParams(url)},yada.replaceAt=function(str,index,character){return str.substr(0,index)+character+str.substr(index+character.length)},yada.removeQuery=function(url){return url.replace(/\?.*/,"")},yada.removePathVariable=function(url,precedingSegment){var regex=new RegExp("/"+precedingSegment+"(/[^/?]*)?");return url.replace(regex,"/"+precedingSegment)},yada.setPathVariable=function(url,precedingSegment,newValue){var regex=new RegExp("/"+precedingSegment+"(/[^/?]*)?");return url.replace(regex,"/"+precedingSegment+"/"+newValue)},yada.hasPathVariableWithValue=function(url,precedingSegment){var value=yada.getPathVariable(url,precedingSegment);return null!=value&&value.length>0},yada.hasPathVariable=function(url,precedingSegment){return null!=yada.getPathVariable(url,precedingSegment)},yada.getPathVariable=function(url,precedingSegment){for(var segments=yada.removeQuery(yada.removeHash(url)).split("/"),found=!1,i=1;i<segments.length;i++)if(segments[i]===precedingSegment)found=!0;else if(found&&segments[i-1]===precedingSegment)return segments[i];return found?"":null},yada.getPathVariableNumeric=function(url,precedingSegment){for(var segments=url.split(/[\/\?#]/),i=1;i<segments.length;i++)if(segments[i-1]===precedingSegment)return isNaN(segments[i])?null:segments[i];return null},yada.checkSession=function(rootUrl){$.get(rootUrl+"ajaxCheckSessionActive",function(responseText){"expired"==responseText&&(window.location.href=rootUrl+"timeout")})},yada.enableConfirmLinks=function($element){null==$element&&($element=$("body"));$("a[data-yadaConfirm], a[data-confirm], form[data-yadaConfirm]",$element.parent()).not(".s_dataConfirmed").not(".yadaAjax").not(".yadaAjaxed").each(function(){$(this).is("a")?$(this).click(handleConfirmation):$(this).is("form")&&$(this).submit(handleConfirmation),$(this).addClass("s_dataConfirmed")})},yada.getIdWithHash=function(element,attributeName){var id=$(element).attr(attributeName);return yada.startsWith(id,"#")||(id="#"+id),id},yada.getRandomId=function(prefix){return(prefix||"")+Math.floor(99999999999*Math.random()).toString(16)},yada.getServerAddress=function(url){return url.replace(siteMatcher,"$1")},yada.joinUrls=function(left,right){return null==right?left:yada.endsWith(left,"/")&&yada.startsWith(right,"/")?left+right.substring(1):yada.endsWith(left,"/")||yada.startsWith(right,"/")?left+right:left+"/"+right},yada.joinManyUrls=function(one,two,three,four,five){var result=yada.joinUrls(one,two);return result=yada.joinUrls(result,three),result=yada.joinUrls(result,four),result=yada.joinUrls(result,five)},yada.getResourcePath=function(){return null==yada.baseUrl||null==yada.resourceDir?(yada.showErrorModal("Internal Error","yada library not initialized in yada.getResourcePath()"),""):yada.baseUrl+yada.resourceDir},yada.getHashValue=function(str){return null!=str&&""!=str?str.split("#")[1]:str},yada.replaceHash=function(someUrl,newHashValue){return yada.removeHash(someUrl)+"#"+yada.removeHash(newHashValue)},yada.removeHash=function(someUrl){return someUrl.split("#")[0]},yada.removeCurrentHash=function(replaceState){const cleanUrl=window.location.origin+window.location.pathname+window.location.search;1==replaceState?history.replaceState("",document.title,cleanUrl):history.pushState("",document.title,cleanUrl)},yada.hashToMap=function(windowLocationHash){var result={},hashString=yada.getHashValue(windowLocationHash);if(null!=hashString&&hashString.length>0)for(var segments=hashString.split(";"),i=0;i<segments.length;i++){var parts=segments[i].split("=");result[parts[0]]=parts[1]}return result},yada.hashPathToMap=function(propertyList,windowLocationHash,separator){var result={},segments=[],hashString=yada.getHashValue(windowLocationHash);null!=hashString&&hashString.length>0&&(segments=hashString.split(separator));for(var i=0;i<propertyList.length;i++){var name=propertyList[i];i<segments.length?result[name]=segments[i]:result[name]=""}return result},yada.arrayContains=function(array,value){if(!(value instanceof Object))return array.includes(value);const valueKeys=Object.keys(value);for(let i=0;i<array.length;i++){const existing=array[i];if(existing instanceof Object){const existingKeys=Object.keys(existing);if(0===$(valueKeys).not(existingKeys).length&&0===$(existingKeys).not(valueKeys).length)return!0}}return!1},yada.stringNotEmpty=function(str){return null!=str&&"string"==typeof str&&str.trim().length>0},yada.titleCase=function(sentence){return sentence.replace(/\w[^\s-.]*/g,function(txt){return txt.charAt(0).toUpperCase()+txt.substr(1).toLowerCase()})},yada.templateReplace=function(template,replacements){for(name in replacements)if(null!=name){var placeholder="\\$\\{"+name+"\\}",value=replacements[name];if("object"!=typeof value)template=template.replace(new RegExp(placeholder,"g"),value);else for(var i=0;i<value?.length;i++)template=i<value.length-1?template.replace(new RegExp(placeholder,"g"),value[i]+"${"+name+"}"):template.replace(new RegExp(placeholder,"g"),value[i])}return template},yada.getAfter=function(str,toFind,fromIndex){if(null==str)return str;var pos=str.indexOf(toFind,fromIndex);return pos>=0?str.substring(pos+toFind.length):str},yada.listToArray=function(str){return null==str?[]:"string"!=typeof str?[str.toString()]:str.split(/, */)},yada.stringContains=function(str,toFind){return null!=str&&"string"==typeof str&&str.indexOf(toFind)>=0},yada.getLast=function(source,separatorChar){var regexp=new RegExp("\\s*"+separatorChar+"\\s*");return source.split(regexp).pop()},yada.startsWith=function(str,prefix){return null!=str&&"string"==typeof str&&0===str.lastIndexOf(prefix,0)},yada.endsWith=function(str,suffix){return null!=str&&"string"==typeof str&&str.substr(-suffix.length)===suffix},yada.extract=function(str,prefix,suffix){const matched=new RegExp(escapeRegExp(prefix)+"(.*?)"+escapeRegExp(suffix)).exec(str);return null!=matched&&matched.length>1&&null!=matched[1]?matched[1]:""},yada.increment=function(elementSelector){return yada.numberAdd(elementSelector,1)},yada.numberAdd=function(elementSelector,toAdd,limit,removeAtLimit){var result=0;return $(elementSelector).each(function(){var element=$(this),text=element.text(),val=parseInt(text,10);isNaN(val)&&(val=0),val+=toAdd;var remove=!1;null!=limit&&(toAdd>0&&val>limit||toAdd<0&&val<limit)?(val=limit,remove=removeAtLimit):result++,1==remove?element.text(""):element.text(val)}),result},yada.makeCustomPopover=function($element){if("object"!=typeof bootstrap||"function"!=typeof bootstrap.Popover)return yada.handleCustomPopoverB3($element);$element=$element||$("body"),$("[data-yadaPopover]",$element).not(".yadaPopovered").each(function(){const trigger=this,$trigger=$(trigger);$trigger.addClass("yadaPopovered");const htmlIdWithHash=yada.getIdWithHash(trigger,"data-yadaPopover");if(null!=htmlIdWithHash&&""!=htmlIdWithHash&&"#"!=htmlIdWithHash){const contentInstanceId=yada.getRandomId("yada"),htmlTemplate=(document.querySelector(htmlIdWithHash)?.content||$(htmlIdWithHash,$element)[0]?.content).cloneNode(!0);var container="body",$modalContainer=$trigger.closest(".modal-body").first();$modalContainer.length>0&&(container=$modalContainer[0]);var defaultTitle=$trigger.attr("data-yadaTitle");null==defaultTitle&&(defaultTitle=htmlTemplate.querySelector("label:first-child")?.innerHTML);const content=$("<div id='"+contentInstanceId+"'>").append($(htmlTemplate).children(":not(label:first-child)")).prop("outerHTML"),popoverObject=new bootstrap.Popover(trigger,{html:!0,title:"<div>"+defaultTitle+"</div>",container:container,content:content,sanitize:!1}),yadaEventListeners=getYadaEventHandlers($trigger),insertedListener=yadaEventListeners["inserted.bs.popover"]?.handler,shownListener=yadaEventListeners["shown.bs.popover"]?.handler;null!=insertedListener&&$trigger.off("inserted.bs.popover").on("inserted.bs.popover",makePopoverInsertedFunction(trigger,popoverObject,contentInstanceId,insertedListener)),null!=shownListener&&$trigger.off("shown.bs.popover").on("shown.bs.popover",makePopoverShownFunction(trigger,popoverObject,contentInstanceId,shownListener))}})},yada.handleCustomPopover=function($element){return yada.log("yada.handleCustomPopover() is deprecated in favor of yada.makeCustomPopover()"),yada.makeCustomPopover($element)},yada.handleCustomPopoverB3=function($element){function makePopoverShownHandler(divId,shownFunction){return function(){var popoverButton=$(this),popoverDiv=$('[data-yadaid="'+divId+'"]');$(".popover.in").not(popoverDiv).popover("hide"),popoverButton.tooltip("hide"),$("button.yadaclose",popoverDiv).click(function(){$(popoverDiv).popover("hide")}),"function"==typeof shownFunction&&shownFunction(popoverButton,popoverDiv)}}function makePopoverClosedHandler(divId,hiddenFunction){return function(){if("function"==typeof hiddenFunction){var popoverButton=$(this),popoverDiv=$('[data-yadaid="'+divId+'"]');hiddenFunction(popoverButton,popoverDiv)}}}null==$element&&($element=$("body")),$("[data-yadaCustomPopoverId]",$element).not(".s_customPopovered").each(function(){$(this).addClass("s_customPopovered");var dataIdWithHash=yada.getIdWithHash(this,"data-yadaCustomPopoverId"),dataTitle=$(this).attr("data-title"),hasTitle=null==$(this).attr("title"),dataId=yada.getHashValue(dataIdWithHash),shownFunction=null;try{shownFunction=eval(dataId+"Shown")}catch(e){}var hiddenFunction=null;try{hiddenFunction=eval(dataId+"Hidden")}catch(e){}var popoverId=yada.getRandomId("cp");$(this).popover({html:!0,title:function(){var titleContent="<span>"+dataTitle+"</span>";return null==dataTitle&&(titleContent=$(dataIdWithHash).children("div:first").clone(!0)),$("<div class='"+dataId+"Title'>").append('<button type="button" class="yadaclose" aria-hidden="true"><i class="fa fa-times fa-lg"></i></button>').append(titleContent)},content:function(){var contentDiv=$(dataIdWithHash).children("div").eq(1);return 0==contentDiv.length?"Internal Error: no popover definition found with id = "+dataId:$("<div class='"+dataId+"Content'>").append(contentDiv.clone(!0))},template:'<div data-yadaid="'+popoverId+'" class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-title"></h3><div class="popover-content"></div></div>'}),$(this).on("shown.bs.popover",makePopoverShownHandler(popoverId,shownFunction)),$(this).on("hidden.bs.popover",makePopoverClosedHandler(popoverId,hiddenFunction))})},yada.confirm=function(title,message,callback,okButtonText,cancelButtonText,okShowsPreviousModal){var $currentModals=hideAllModals($("#yada-confirm")),okClicked=!1,cancelClicked=!1;yada.loaderOff(),title&&$("#yada-confirm .modal-header .confirm-title").html(title),$("#yada-confirm .modal-body p").html(message);var previousOkButtonText=$("#yada-confirm .okButton").text();okButtonText&&$("#yada-confirm .okButton").text(okButtonText);var previousCancelButtonText=$("#yada-confirm .cancelButton").text();cancelButtonText&&$("#yada-confirm .cancelButton").text(cancelButtonText),$("#yada-confirm .okButton").off().click(function(){okClicked=!0,cancelClicked=!1,"function"==typeof callback&&callback(!0)}),$("#yada-confirm .cancelButton").off().click(function(){cancelClicked=!0,okClicked=!1,"function"==typeof callback&&callback(!1)});var $modal=$("#yada-confirm .modal");0==$modal.length&&yada.log("No confirm modal found: did you include it?"),$modal.modal("show"),$modal.off("hidden.bs.modal").on("hidden.bs.modal",function(e){$("#yada-confirm .okButton").text(previousOkButtonText),$("#yada-confirm .cancelButton").text(previousCancelButtonText),cancelClicked||okClicked&&1==okShowsPreviousModal?$currentModals.length>0&&$currentModals.css("display","block"):($currentModals.css("display","block"),$currentModals.modal("hide"))})},yada.showErrorModal=function(title,message,redirectUrl){showNotificationModal(title,message,"error",redirectUrl)},yada.showOkModal=function(title,message,redirectUrl){showNotificationModal(title,message,"ok",redirectUrl)},yada.showInfoModal=function(title,message,redirectUrl){showNotificationModal(title,message,"info",redirectUrl)},yada.localStorageAvailable=function(){try{var storage=window.localStorage,x="__storage_test__";return storage.setItem(x,x),storage.removeItem(x),!0}catch(e){return!1}},yada.setCookie=function(name,value,expiryDays,domain){var expires="";if(null!=expiryDays){var d=new Date;d.setDate(d.getDate()+expiryDays),expires=";expires="+d.toGMTString()}domain=null!=domain?";domain="+domain:"",document.cookie=name+"="+value+domain+" ;path=/ "+expires},yada.getCookie=function(cname){for(var name=cname+"=",ca=document.cookie.split(";"),i=0;i<ca.length;i++){for(var c=ca[i];" "==c.charAt(0);)c=c.substring(1);if(0==c.indexOf(name))return c.substring(name.length,c.length)}return""},yada.deleteCookie=function(name,domain){yada.setCookie(name,"",0,domain)},yada.changeLanguagePathVariable=function(language){var currentPath=window.location.pathname,regex=new RegExp("/[^/]+"),verifyLanguage=currentPath.match(regex);currentPath=3!=verifyLanguage[0].length?"/"+language+currentPath:currentPath.replace(regex,"/"+language),window.location.pathname=currentPath},yada.enableShowPassword=function(element){null==element&&(element=$("body"));var $target=$(".yadaShowPassword",element).not(".yadaShowPassworded");$target.click(function(e){e.preventDefault();var $hiddenField=$(this).parent().parent().find("input[type='password']");$hiddenField.attr("type","text").length>0?$(this).parents("form").find('button[type="submit"]').click(function(e){$hiddenField.attr("type","password")}):$(this).parent().parent().find("input[type='text']").attr("type","password")}),$target.addClass("yadaShowPassworded")},yada.enablePasswordMatch=function($forms){$('button[type="submit"]',$forms).attr("disabled","disabled"),$("input[name=password], input[name=confirmPassword]",$forms).on("keyup",function(e){checkMatchingPasswords($(this).parents("form"))}).on("blur",function(){checkMatchingPasswords($(this).parents("form"))})},yada.extendedSelect=function($fromElement,selector){if(null==selector||""==selector.trim())return $fromElement;var fromChildren=yada.startsWith(selector,findSelector),fromParents=yada.startsWith(selector,parentSelector),fromSiblings=yada.startsWith(selector,siblingsSelector),fromClosestFind=yada.startsWith(selector,closestFindSelector),fromSiblingsFind=yada.startsWith(selector,siblingsFindSelector);if(0==fromChildren&&0==fromParents&&0==fromSiblings&&0==fromClosestFind&&0==fromSiblingsFind)return $(selector);if(fromChildren)return selector=selector.replace(findSelector,"").trim(),$fromElement.find(selector);if(fromParents)return selector=selector.replace(parentSelector,"").trim(),$fromElement.parent().closest(selector);if(fromSiblings)return selector=selector.replace(siblingsSelector,"").trim(),$fromElement.siblings(selector);if(fromClosestFind){var splitSelector=(selector=selector.replace(closestFindSelector,"").trim()).split(" ",2);return $fromElement.parent().closest(splitSelector[0]).find(splitSelector[1])}if(fromSiblingsFind){splitSelector=(selector=selector.replace(siblingsFindSelector,"").trim()).split(" ",2);return $fromElement.siblings(splitSelector[0]).find(splitSelector[1])}return $fromElement},yada.addFormGroupFields=function($form,$formGroup){$formGroup.length>1&&$formGroup.each(function(){let $eachForm=$(this);$eachForm.is($form)||$eachForm.find(":input").each(function(){let $field=$(this);$field.css("display","none"),removeFormField($form.get(0),$field.get(0)),$field.appendTo($form)})})},yada.enableFormGroup=function($root){null==$root&&($root=$("body"));var $target=$root.parent();0==$target.length&&($target=$root),$("form",$target).not(".yadaAjax").not(".yadaAjaxed").submit(function(){const $form=$(this);var yadaFormGroup=$form.attr("data-yadaFormGroup");if(null!=yadaFormGroup&&null==this.yadaFormGroupAdded){const $formGroup=$("form[data-yadaFormGroup="+yadaFormGroup+"]");yada.addFormGroupFields($form,$formGroup)}}),$("[data-yadaFormGroup]",$target).not("form").not(".yadaAjax").not(".yadaAjaxed").click(function(e){const $clickedElement=$(this);var yadaFormGroup=$clickedElement.attr("data-yadaFormGroup");if(null!=yadaFormGroup){const $formGroupForms=$("form[data-yadaFormGroup="+yadaFormGroup+"]");if(0==$formGroupForms.length)return;e.preventDefault();const $toSubmit=$formGroupForms.first();yada.addFormGroupFields($toSubmit,$formGroupForms),$toSubmit[0].yadaFormGroupAdded=!0;const newAction=$clickedElement.attr("href")||$clickedElement.attr("data-href");if(null!=newAction)for(var nameValue of($toSubmit.attr("action",newAction),yada.getUrlParameters(newAction).entries())){const name=nameValue[0],value=nameValue[1],input=document.createElement("input");input.setAttribute("name",name),input.setAttribute("value",value),input.setAttribute("type","hidden"),removeFormField($toSubmit.get(0),input),$toSubmit.append(input)}return $toSubmit.submit(),!1}})},yada.enableParentForm=function($element){null==$element&&($element=$("body"));var $target=$element.parent();0==$target.length&&($target=$element),$("form[data-yadaParentForm]",$target).each(function(){$(this).submit(function(e){e.preventDefault();var $thisForm=$(this),parentFormSelector=$thisForm.attr("data-yadaParentForm"),$parentFormArray=yada.extendedSelect($thisForm,parentFormSelector);if(null!=$parentFormArray){for(var i=0;i<$parentFormArray.length;i++){var parentForm=$parentFormArray[i];"form"==parentForm.nodeName.toLowerCase()&&(parentForm.yadaChildForm=$thisForm)}$parentFormArray.submit()}})}),$("form").not(".yadaAjax").not(".yadaAjaxed").submit(function(e){var $form=$(this),$childForm=this.yadaChildForm;if(null!=$childForm){var $newform=$($form).clone(!0);return $(document.body).append($newform),$childForm.children().filter("input, textarea, select").clone().appendTo($newform),e.preventDefault(),$newform.submit(),!1}})},yada.findFromParent=function(parentSelector,childSelector,$html){var $root=$html.find(parentSelector);return 0==$root.length&&($root=$html.filter(parentSelector)),$root.find(childSelector)},yada.toTimeAgo=function(dateInThePast,locale){const date=dateInThePast instanceof Date?dateInThePast:new Date(dateInThePast),formatter=new Intl.RelativeTimeFormat(locale),ranges={years:31536e3,months:2592e3,weeks:604800,days:86400,hours:3600,minutes:60,seconds:1},secondsElapsed=(date.getTime()-Date.now())/1e3;for(let key in ranges)if(ranges[key]<Math.abs(secondsElapsed)){const delta=secondsElapsed/ranges[key];return formatter.format(Math.round(delta),key)}},yada.copyToClipboard=function(extendedSelector,event,showFeedback){event.stopPropagation();const element=yada.extendedSelect($(event.target),extendedSelector)[0];if(!element)return void yada.log('Element with selector "'+extendedSelector+'" not found');const text=element.textContent;navigator.clipboard&&navigator.clipboard.writeText?navigator.clipboard.writeText(text).then(function(){showFeedback&&yada.showAjaxFeedback()}).catch(function(err){copyUsingFallback()}):copyUsingFallback()}}(window.yada=window.yada||{}),jQuery.fn.findWithSelf=function(...args){return this.pushStack(this.find(...args).add(this.filter(...args)))},function(yada){"use strict";function facebookLoginResult(response,serverUrl){if("connected"===response.status){var accessToken=response.authResponse.accessToken;yada.loaderOn(),FB.api("/me",function(response){response.first_name,response.last_name,response.email;!function(serverUrl,accessToken){yada.ajax(serverUrl,{accessToken:accessToken},function(responseText,statusText){})}(serverUrl,accessToken)})}else yada.postLoginHandler=null,yada.loaderOff()}yada.fbInitDone=!1,yada.facebookInit=function(){yada.fbInitDone=!0},yada.facebookLogout=function(){FB.getLoginStatus(function(response){response&&"connected"===response.status&&FB.logout()})},yada.afterFacebookLoginButton=function(serverUrl){FB.getLoginStatus(function(response){facebookLoginResult(response,serverUrl)})},yada.googleSignOut=function(){if(null!=gapi&&null!=gapi.auth2){var authInstance=gapi.auth2.getAuthInstance();null!=authInstance&&authInstance.signOut()}};yada.enableGoogleLoginButtonDEPRECATED=function(serverUrl,id,options){options.onsuccess=function(googleUser){handleGoogleLoginButton(serverUrl,googleUser)},gapi.signin2.render(id,options)},yada.enableFacebookLoginButton=function(serverUrl,handler){$(".facebookLoginButton").click(function(e){e.preventDefault(),yada.loaderOn(),$("#loginModal").modal("hide"),FB.login(function(response){yada.postLoginHandler=handler,facebookLoginResult(response,serverUrl)},{scope:"email",auth_type:"rerequest"})})}}(window.yada=window.yada||{}),0!=$(".mySlides").length){var slideIndex=1;function plusSlides(n){showSlides(slideIndex+=n)}function currentSlide(n){showSlides(slideIndex=n)}function showSlides(n){var i,slides=document.getElementsByClassName("mySlides"),dots=document.getElementsByClassName("dot");for(n>slides.length&&(slideIndex=1),n<1&&(slideIndex=slides.length),i=0;i<slides.length;i++)slides[i].style.display="none",slides[i].className="mySlides fade product-slider";for(i=0;i<dots.length;i++)dots[i].className=dots[i].className.replace(" active","");slides[slideIndex-1].style.display="block",slides[slideIndex-1].className+=" current",dots.length>0&&(dots[slideIndex-1].className+=" active ")}showSlides(slideIndex)}function mobileViewFunction1(viewSize){if(viewSize.matches){var i=0;$(".product-block").each(function(){$(this).is(".hidden-xs")||(0==i%2&&$(this).addClass("block-odd"),i++)});var mobileDropDownHeight=$(window).height()-$(".logo-containar").outerHeight()-$("#mobileMenu").outerHeight();$("#mobile-dropdown").height(mobileDropDownHeight)}}var viewSize=window.matchMedia("(max-width: 865px)");function mobileDropdown(){$("#mobile-dropdown").is(".show")?($("#mobile-dropdown").removeClass("show"),$("#mobileMenu").html("menu"),$("#mobileMenu").removeClass("icon-x")):($("#mobile-dropdown").addClass("show"),$("#mobileMenu").html(""),$("#mobileMenu").addClass("icon-x"))}mobileViewFunction1(viewSize),viewSize.addListener(mobileViewFunction1),$("body").on("click",".mobile-nav-list",function(){$('.mobile-nav-sub-menu[data-id="'+$(this).attr("data-value")+'"]').is(".show")?$('.mobile-nav-sub-menu[data-id="'+$(this).attr("data-value")+'"]').removeClass("show"):($(".mobile-nav-sub-menu").removeClass("show"),$('.mobile-nav-sub-menu[data-id="'+$(this).attr("data-value")+'"]').addClass("show"))}),$("body").on("click",".search-content-block",function(){$(".search-box").addClass("search-box-content"),$(".search-content-block").addClass("search-content-block-hidden")}),$("body").on("click",".close-search",function(){$(".search-box").removeClass("search-box-content"),$(".search-content-block").removeClass("search-content-block-hidden")}),$("body").on("click",".close-searchMob",function(){$(".mobile-nav-sub-menu").removeClass("show")}),$(document).mouseup(function(e){var container=$(".language-dropdown-content");container.is(e.target)||0!==container.has(e.target).length||$(".language-dropdown").addClass("hidden-lg hidden-md hidden-sm hidden-xs")}),$(document).ready(function(){$(".language-item").on("click",function(e){$(".language-item").removeClass("selected-language"),$(this).addClass("selected-language")})}),function($){$.fn.SHorizontal=function(options){if("rollTo"!=options){if("get"==options)return this.children("div."+class_item_selected).index();if("getText"==options)return this.children("div."+class_item_selected).children(".internal").attr("data-href");var opts=$.extend({},$.fn.SHorizontal.defaults,options);this.off(SHorizontal_rollto).on(SHorizontal_rollto,function(event,to){to&&rollTo($(this),to)}),(style={position:"relative"})[xform]="rotateY("+opts.rotation+"deg)",style[xform+"-style"]="preserve-3d",this.css(style).addClass("SHorizontal-container");var item="";if(opts.items.length){var center_index=0;center_index="first"==opts.center?0:"last"==opts.center?opts.items.length-1:"center"==opts.center?parseInt(opts.items.length/2):$.isNumeric(opts.center)&&opts.center>=0&&opts.center<opts.items.length?opts.center:opts.center>=opts.items.length?opts.items.length-1:0;var distance=parseInt(this.height()/2);$.isNumeric(opts.distance)&&(distance=opts.distance);var decadeWidth=decadeWidth=$(".decade-container").outerWidth();decadeWidth=Math.round(decadeWidth/8),window.matchMedia("(max-width: 991px)").matches||(decadeWidth=45);for(var style="position: absolute;left:"+decadeWidth+"%;width: auto;height:auto;top: 22%;",i=0;i<opts.items.length;i++){var displayed="";Math.abs(i-center_index)>opts.displayed_length&&(displayed="display:none;");var angle=opts.angle*(center_index-i);opts.angle,opts.displayed_length;item+='<div class="SHorizontal-item '+(i==center_index?class_item_selected:"")+'" style="'+("transform:rotateY("+angle+"deg) translate3d(0,0,"+distance+"px);-webkit-transform:rotateY("+angle+"deg) translate3d(0,0,"+distance+"px);")+displayed+style+'opacity:1;">'+opts.items[i]+"</div>"}return this.html(item).data("cur-angle",center_index*opts.angle).off(start).on(start,function(e){return $(this).addClass("w-roll-touched").data("initialtouch",getEventPos(e).x),!1}).off(move).on(move,function(e){var ini=$(this);if(ini.is(".w-roll-touched")){var deltaY=ini.data("initialtouch")-getEventPos(e).x,mainAngle=parseInt(ini.data("cur-angle"))-parseInt(deltaY/2),maxAngle=(opts.items.length-1)*opts.angle;if(mainAngle<0)mainAngle=-25*(excess=0-mainAngle)/(excess+25);else if(mainAngle>maxAngle){var excess;mainAngle=maxAngle+25*(excess=mainAngle-maxAngle)/(excess+25)}ini.children("div").each(function(){var curr=$(this),options={},currAngle=mainAngle-curr.index()*opts.angle;options.display="",Math.abs(currAngle)>opts.displayed_length*opts.angle&&(options.display="none");opts.angle,opts.displayed_length;options[xform]="rotateY("+currAngle+"deg) translateZ("+distance+"px)",options.opacity=1,curr.css(options)})}return!1}).off(end).on(end,function(e){var ini=$(this);if(ini.is(".w-roll-touched")){var deltaY=ini.data("initialtouch")-getEventPos(e).x,mainAngle=parseInt(ini.data("cur-angle"))-parseInt(deltaY/2),maxAngle=(opts.items.length-1)*opts.angle,index=Math.round(mainAngle/opts.angle);if(mainAngle<0)mainAngle=-25*(excess=0-mainAngle)/(excess+25),index=0;else if(mainAngle>maxAngle){var excess;mainAngle=maxAngle+25*(excess=mainAngle-maxAngle)/(excess+25),index=opts.items.length-1}ini.data("cur-angle",mainAngle),rollTo(ini,index)}return ini.removeClass("w-roll-touched"),!1})}return this}var args=Array.prototype.slice.call(arguments,1);function rollTo(objek,index){index<0?index=0:index>=opts.items.length&&(index=opts.items.length-1);var fromAngle=parseInt(objek.data("cur-angle")),toAngle=index*opts.angle,deltaAngle=toAngle-fromAngle;animationStep(10,1,function(step,curStep,objek){var t,mainAngle=(t=curStep,-deltaAngle*(t/=step)*(t-2)+fromAngle);objek.children("div").each(function(){var curr=$(this),options={},currAngle=mainAngle-curr.index()*opts.angle;options.display="",Math.abs(currAngle)>opts.displayed_length*opts.angle&&(options.display="none");opts.angle,opts.displayed_length;options[xform]="rotateY("+currAngle+"deg) translateZ("+distance+"px)",options.opacity=1,curr.css(options)})},function(objek){objek.children("div").each(function(){var curr=$(this).removeClass(class_item_selected),options={},currAngle=toAngle-curr.index()*opts.angle;options.display="",Math.abs(currAngle)>opts.displayed_length*opts.angle&&(options.display="none");opts.angle,opts.displayed_length;options[xform]="rotateY("+currAngle+"deg) translateZ("+distance+"px)",options.opacity=1,curr.css(options),0==currAngle&&curr.addClass(class_item_selected)}),objek.data("cur-angle",toAngle),objek.trigger("horizontal_scroll",[index])},objek)}this.trigger(SHorizontal_rollto,args)},$.fn.SHorizontal.defaults={items:[],center:"first",distance:"auto",displayed_length:2,angle:30,rotation:0,item_height:20};var xform="transform";["webkit","Moz","O","ms"].every(function(prefix){var e=prefix+"Transform";void 0!==document.body.style[e]&&(xform=e)});var start="touchstart mousedown",move="touchmove mousemove",end="touchend mouseup mouseleave",SHorizontal_rollto="SHorizontal.rollTo",class_item_selected="SHorizontal-item-selected";function animationStep(step,curStep,stepFunc,doneFunc,objek){curStep<=step?("function"==typeof stepFunc&&stepFunc(step,curStep,objek),curStep+=1,window.requestAnimationFrame(function(){animationStep(step,curStep,stepFunc,doneFunc,objek)})):"function"==typeof doneFunc&&doneFunc(objek)}function getEventPos(e){return e.originalEvent?e.originalEvent.changedTouches&&e.originalEvent.changedTouches.length>=1?{x:e.originalEvent.changedTouches[0].pageX,y:e.originalEvent.changedTouches[0].pageY}:{x:e.originalEvent.clientX,y:e.originalEvent.clientY}:e.changedTouches&&e.changedTouches.length>=1?{x:e.changedTouches[0].pageX,y:e.changedTouches[0].pageY}:{x:e.clientX,y:e.clientY}}}(jQuery),function(dnc){"use strict";function handleSearch(){var $input=$("#searchField"),$button=$("#searchButton"),text=$input.val();if(text.length>=3){var url=$button.attr("href")+"/"+encodeURIComponent(text);window.location.href=url}}function handleSearchMob(){var $input=$("#searchFieldMob"),$button=$("#searchButtonMob"),text=$input.val();if(text.length>=3){var url=$button.attr("href")+"/"+encodeURIComponent(text);window.location.href=url}}dnc.loadingMore=!1,dnc.autoLoadMore=function(){var el=$(".loadMore:visible");if(1!=dnc.loadingMore&&0!=el.length){var rect=el[0].getBoundingClientRect(),elemTop=rect.top,elemBottom=rect.bottom;elemTop<window.innerHeight&&elemBottom>=0&&(dnc.loadingMore=!0,setTimeout(function(){$("a",el).trigger("click")},777))}},$(".language-item").on("click",function(e){yada.loaderOn();var newLanguage=$(this).attr("data-lang");yada.changeLanguagePathVariable(newLanguage)}),$("#searchButton").click(function(e){return e.preventDefault(),handleSearch(),!1}),$("#searchField").keypress(function(e){if(13==event.which)return e.preventDefault(),handleSearch(),!1}),$("#searchButtonMob").click(function(e){return e.preventDefault(),handleSearchMob(),!1}),$("#searchFieldMob").keypress(function(e){if(13==event.which)return e.preventDefault(),handleSearchMob(),!1}),$(document).ready(function(){$(".loader").click(function(){yada.loaderOff()})}),dnc.replaceMunari=function($element){$element.each(function(){const origText=this.innerHTML;if(null!=origText){const replacedText=origText.replace(/Munari/g,"Munari<sup>®</sup>");!origText!==replacedText&&(this.innerHTML=replacedText)}})},dnc.replaceMunari($(".product-tag-block")),dnc.replaceMunari($("a.product-details-short-text")),dnc.replaceMunari($(".designer-name")),dnc.replaceMunari($(".designerName")),dnc.replaceMunari($(".main-designer-name")),dnc.replaceMunari($(".manifesto-description"));dnc.initializeLocalizedForm=function(){$("#formLanguageChooser input").change(function(){var $checkbox=$(this),$label=$(this).parent(),checked=$checkbox.prop("checked"),language=$label.text().trim().toLowerCase();$("form .localized."+language).toggle(checked),yada.setCookie("languageChooser_"+language,checked,365),0==$("#formLanguageChooser input:checked").length&&$("#formLanguageChooser .checkbox:not(."+language+") input").click()}),$("#formLanguageChooser input").each(function(){var $checkbox=$(this),language=$checkbox.parent().text().trim().toLowerCase(),checked="true"==yada.getCookie("languageChooser_"+language);$checkbox.prop("checked",checked),checked&&$("form .localized."+language).show()}),0==$("#formLanguageChooser input:checked").length&&($("#formLanguageChooser .en input").prop("checked",!0),$("form .localized.en").show())},dnc.slideImageSizeCheck=function(){$(".carousel img").each(function(){var $image=$(this),newImage=new Image;newImage.src=$image[0].src;var proportions=newImage.width/newImage.height;proportions>.89&&proportions<1.1?$image.css("width","75%"):proportions>1.3&&proportions<1.4&&$image.css("width","100%")})};dnc.isCookieBannerNewsletterSet=function(){return document.cookie.indexOf("bannerNewsletter=true")>=0},dnc.setCookieBannerNewsletter=function(){var date=new Date;date.setDate(date.getDate()+7),document.cookie="bannerNewsletter=true; expires="+date.toGMTString()+"; path=/"}}(window.dnc=window.dnc||{}),
/*! jQuery UI - v1.10.3 - 2013-12-20
* http://jqueryui.com
* Includes: jquery.ui.widget.js
* Copyright 2013 jQuery Foundation and other contributors; Licensed MIT */
function($,undefined){var uuid=0,slice=Array.prototype.slice,_cleanData=$.cleanData;$.cleanData=function(elems){for(var elem,i=0;null!=(elem=elems[i]);i++)try{$(elem).triggerHandler("remove")}catch(e){}_cleanData(elems)},$.widget=function(name,base,prototype){var fullName,existingConstructor,constructor,basePrototype,proxiedPrototype={},namespace=name.split(".")[0];name=name.split(".")[1],fullName=namespace+"-"+name,prototype||(prototype=base,base=$.Widget),$.expr[":"][fullName.toLowerCase()]=function(elem){return!!$.data(elem,fullName)},$[namespace]=$[namespace]||{},existingConstructor=$[namespace][name],constructor=$[namespace][name]=function(options,element){if(!this._createWidget)return new constructor(options,element);arguments.length&&this._createWidget(options,element)},$.extend(constructor,existingConstructor,{version:prototype.version,_proto:$.extend({},prototype),_childConstructors:[]}),(basePrototype=new base).options=$.widget.extend({},basePrototype.options),$.each(prototype,function(prop,value){var _super,_superApply;$.isFunction(value)?proxiedPrototype[prop]=(_super=function(){return base.prototype[prop].apply(this,arguments)},_superApply=function(args){return base.prototype[prop].apply(this,args)},function(){var returnValue,__super=this._super,__superApply=this._superApply;return this._super=_super,this._superApply=_superApply,returnValue=value.apply(this,arguments),this._super=__super,this._superApply=__superApply,returnValue}):proxiedPrototype[prop]=value}),constructor.prototype=$.widget.extend(basePrototype,{widgetEventPrefix:existingConstructor?basePrototype.widgetEventPrefix:name},proxiedPrototype,{constructor:constructor,namespace:namespace,widgetName:name,widgetFullName:fullName}),existingConstructor?($.each(existingConstructor._childConstructors,function(i,child){var childPrototype=child.prototype;$.widget(childPrototype.namespace+"."+childPrototype.widgetName,constructor,child._proto)}),delete existingConstructor._childConstructors):base._childConstructors.push(constructor),$.widget.bridge(name,constructor)},$.widget.extend=function(target){for(var key,value,input=slice.call(arguments,1),inputIndex=0,inputLength=input.length;inputIndex<inputLength;inputIndex++)for(key in input[inputIndex])value=input[inputIndex][key],input[inputIndex].hasOwnProperty(key)&&undefined!==value&&($.isPlainObject(value)?target[key]=$.isPlainObject(target[key])?$.widget.extend({},target[key],value):$.widget.extend({},value):target[key]=value);return target},$.widget.bridge=function(name,object){var fullName=object.prototype.widgetFullName||name;$.fn[name]=function(options){var isMethodCall="string"==typeof options,args=slice.call(arguments,1),returnValue=this;return options=!isMethodCall&&args.length?$.widget.extend.apply(null,[options].concat(args)):options,isMethodCall?this.each(function(){var methodValue,instance=$.data(this,fullName);return instance?$.isFunction(instance[options])&&"_"!==options.charAt(0)?(methodValue=instance[options].apply(instance,args))!==instance&&undefined!==methodValue?(returnValue=methodValue&&methodValue.jquery?returnValue.pushStack(methodValue.get()):methodValue,!1):void 0:$.error("no such method '"+options+"' for "+name+" widget instance"):$.error("cannot call methods on "+name+" prior to initialization; attempted to call method '"+options+"'")}):this.each(function(){var instance=$.data(this,fullName);instance?instance.option(options||{})._init():$.data(this,fullName,new object(options,this))}),returnValue}},$.Widget=function(){},$.Widget._childConstructors=[],$.Widget.prototype={widgetName:"widget",widgetEventPrefix:"",defaultElement:"<div>",_createWidget:function(options,element){element=$(element||this.defaultElement||this)[0],this.element=$(element),this.uuid=uuid++,this.eventNamespace="."+this.widgetName+this.uuid,this.options=$.widget.extend({},this.options,options),this._create(),this._init()},_trigger:function(type,event,data){this.options[type];data=data||{},(event=$.Event(event)).type=(type===this.widgetEventPrefix?type:this.widgetEventPrefix+type).toLowerCase(),event.target=this.element[0]}}}(jQuery),function($){"use strict";var DEFAULT_SETTINGS={cursor:"move",decelerate:!0,triggerHardware:!1,y:!0,x:!0,slowdown:.9,maxvelocity:40,throttleFPS:60,movingClass:{up:"kinetic-moving-up",down:"kinetic-moving-down",left:"kinetic-moving-left",right:"kinetic-moving-right"},deceleratingClass:{up:"kinetic-decelerating-up",down:"kinetic-decelerating-down",left:"kinetic-decelerating-left",right:"kinetic-decelerating-right"}};window.requestAnimationFrame||(window.requestAnimationFrame=window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(callback,element){window.setTimeout(callback,1e3/60)}),$.support=$.support||{},$.extend($.support,{touch:"ontouchend"in document});var selectStart=function(){return!1},decelerateVelocity=function(velocity,slowdown){return 0===Math.floor(Math.abs(velocity))?0:velocity*slowdown},capVelocity=function(velocity,max){var newVelocity=velocity;return velocity>0?velocity>max&&(newVelocity=max):velocity<0-max&&(newVelocity=0-max),newVelocity},setMoveClasses=function(settings,classes){this.removeClass(settings.movingClass.up).removeClass(settings.movingClass.down).removeClass(settings.movingClass.left).removeClass(settings.movingClass.right).removeClass(settings.deceleratingClass.up).removeClass(settings.deceleratingClass.down).removeClass(settings.deceleratingClass.left).removeClass(settings.deceleratingClass.right),settings.velocity>0&&(this.addClass(classes.right),!0),settings.velocity<0&&(this.addClass(classes.left),!1),settings.velocityY>0&&this.addClass(classes.down),settings.velocityY<0&&this.addClass(classes.up)},move=function($scroller,settings){var scroller=$scroller[0];settings.x&&scroller.scrollWidth>0?(scroller.scrollLeft=settings.scrollLeft=scroller.scrollLeft+settings.velocity,Math.abs(settings.velocity)>0&&(settings.velocity=settings.decelerate?decelerateVelocity(settings.velocity,settings.slowdown):settings.velocity)):settings.velocity=0,settings.y&&scroller.scrollHeight>0?(scroller.scrollTop=settings.scrollTop=scroller.scrollTop+settings.velocityY,Math.abs(settings.velocityY)>0&&(settings.velocityY=settings.decelerate?decelerateVelocity(settings.velocityY,settings.slowdown):settings.velocityY)):settings.velocityY=0,setMoveClasses.call($scroller,settings,settings.deceleratingClass),"function"==typeof settings.moved&&settings.moved.call($scroller,settings),Math.abs(settings.velocity)>0||Math.abs(settings.velocityY)>0?window.requestAnimationFrame(function(){move($scroller,settings)}):stop($scroller,settings)},attachListeners=function($this,settings){$this[0];$.support.touch?$this.bind("touchstart",settings.events.touchStart).bind("touchend",settings.events.inputEnd).bind("touchmove",settings.events.touchMove):$this.mousedown(settings.events.inputDown).mouseup(settings.events.inputEnd).mousemove(settings.events.inputMove),$this.click(settings.events.inputClick).scroll(settings.events.scroll).bind("selectstart",selectStart).bind("dragstart",settings.events.dragStart)},initElements=function(options){this.addClass("kinetic-active").each(function(){var self=this,$this=$(this);if(!$this.data("kinetic-settings")){var xpos,ypos,lastMove,elementFocused,settings=$.extend({},DEFAULT_SETTINGS,options),prevXPos=!1,prevYPos=!1,mouseDown=!1,throttleTimeout=1e3/settings.throttleFPS;settings.velocity=0,settings.velocityY=0;var resetMouse=function(){xpos=!1,ypos=!1,mouseDown=!1};$(document).mouseup(resetMouse).click(resetMouse);var calculateVelocities=function(){settings.velocity=capVelocity(prevXPos-xpos,settings.maxvelocity),settings.velocityY=capVelocity(prevYPos-ypos,settings.maxvelocity)},useTarget=function(target,ev){return $.isFunction(settings.filterTarget)?!1!==settings.filterTarget.call(self,target,ev):!(ev.which&&ev.which>1)},start=function(clientX,clientY){mouseDown=!0,settings.velocity=prevXPos=0,settings.velocityY=prevYPos=0,xpos=clientX,ypos=clientY},inputmove=function(clientX,clientY){(!lastMove||new Date>new Date(lastMove.getTime()+throttleTimeout))&&(lastMove=new Date,mouseDown&&(xpos||ypos)&&(elementFocused&&($(elementFocused).blur(),elementFocused=null,$this.focus()),settings.decelerate=!1,settings.velocity=settings.velocityY=0,$this[0].scrollLeft=settings.scrollLeft=settings.x?$this[0].scrollLeft-(clientX-xpos):$this[0].scrollLeft,$this[0].scrollTop=settings.scrollTop=settings.y?$this[0].scrollTop-(clientY-ypos):$this[0].scrollTop,prevXPos=xpos,prevYPos=ypos,xpos=clientX,ypos=clientY,calculateVelocities(),setMoveClasses.call($this,settings,settings.movingClass),"function"==typeof settings.moved&&settings.moved.call($this,settings)))};settings.events={touchStart:function(e){var touch;useTarget(e.target,e)&&(touch=e.originalEvent.touches[0],start(touch.clientX,touch.clientY),e.stopPropagation())},touchMove:function(e){var touch;mouseDown&&(touch=e.originalEvent.touches[0],inputmove(touch.clientX,touch.clientY),e.preventDefault&&e.preventDefault())},inputDown:function(e){useTarget(e.target,e)&&(start(e.clientX,e.clientY),elementFocused=e.target,"IMG"===e.target.nodeName&&e.preventDefault(),e.stopPropagation())},inputEnd:function(e){xpos&&prevXPos&&!1===settings.decelerate&&(settings.decelerate=!0,calculateVelocities(),xpos=prevXPos=mouseDown=!1,move($this,settings)),elementFocused=null,e.preventDefault&&e.preventDefault();parseInt($(".scrollableArea").css("left"))},inputMove:function(e){mouseDown&&(inputmove(e.clientX,e.clientY),e.preventDefault&&e.preventDefault())},scroll:function(e){"function"==typeof settings.moved&&settings.moved.call($this,settings),e.preventDefault&&e.preventDefault()},inputClick:function(e){if(Math.abs(settings.velocity)>0)return e.preventDefault(),!1},dragStart:function(e){if(elementFocused)return!1}},attachListeners($this,settings),$this.data("kinetic-settings",settings).css("cursor",settings.cursor),settings.triggerHardware&&$this.css({"-webkit-transform":"translate3d(0,0,0)","-webkit-perspective":"1000","-webkit-backface-visibility":"hidden"})}})};$.kinetic={settingsKey:"kinetic-settings",callMethods:{start:function(settings,options){var $this=$(this);(settings=$.extend(settings,options))&&(settings.decelerate=!1,move($this,settings))},end:function(settings,options){$(this);settings&&(settings.decelerate=!0)},stop:function(settings,options){var $this=$(this);stop($this,settings)},detach:function(settings,options){var $this=$(this);detachListeners($this,settings),$this.removeClass("kinetic-active").css("cursor","")},attach:function(settings,options){var $this=$(this);attachListeners($this,settings),$this.addClass("kinetic-active").css("cursor","move")}}},$.fn.kinetic=function(options){return"string"==typeof options?callOption.apply(this,arguments):initElements.call(this,options),this}}(window.jQuery||window.Zepto),function($,window,document,undefined){$.widget("thomaskahn.smoothTouchScroll",{options:{scrollableAreaClass:"scrollableArea",scrollWrapperClass:"scrollWrapper",continuousScrolling:!0,startAtElementId:""},_create:function(){var self=this,o=this.options,el=this.element;el.data("scrollWrapper",el.find("."+o.scrollWrapperClass)),el.data("scrollableArea",el.find("."+o.scrollableAreaClass)),0===el.data("scrollableArea").length&&0===el.data("scrollWrapper").length?(el.wrapInner("<div class='"+o.scrollableAreaClass+"'>").wrapInner("<div class='"+o.scrollWrapperClass+"'>"),el.data("scrollWrapper",el.find("."+o.scrollWrapperClass)),el.data("scrollableArea",el.find("."+o.scrollableAreaClass))):0===el.data("scrollWrapper").length?(el.wrapInner("<div class='"+o.scrollWrapperClass+"'>"),el.data("scrollWrapper",el.find("."+o.scrollWrapperClass))):0===el.data("scrollableArea").length&&(el.data("scrollWrapper").wrapInner("<div class='"+o.scrollableAreaClass+"'>"),el.data("scrollableArea",el.find("."+o.scrollableAreaClass))),el.data("scrollXPos",0),el.data("scrollableAreaWidth",0),el.data("startingPosition",0),el.data("rightScrollingInterval",null),el.data("leftScrollingInterval",null),el.data("previousScrollLeft",0),el.data("getNextElementWidth",!0),el.data("swapAt",null),el.data("startAtElementHasNotPassed",!0),el.data("swappedElement",null),el.data("originalElements",el.data("scrollableArea").children()),el.data("visible",!0),el.data("enabled",!0),el.data("scrollableAreaHeight",el.data("scrollableArea").height()),el.data("enabled")&&el.data("scrollWrapper").kinetic({cursor:"move",decelerate:!0,triggerHardware:!1,y:!1,x:!0,axisTolerance:7,slowdown:.9,maxvelocity:120,throttleFPS:60,moved:function(settings){self._trigger("touchMoved")},stopped:function(settings){self._trigger("touchStopped")}}),$(window).bind("resize",function(){self._trigger("windowResized")})},_init:function(){this.element;this.recalculateScrollableArea(),this._trigger("initializationComplete")},recalculateScrollableArea:function(){var tempScrollableAreaWidth=0,foundStartAtElement=!1,o=this.options,el=this.element;el.data("scrollableArea").children().length?el.data("scrollableArea").children().each(function(){o.startAtElementId.length>0&&$(this).attr("id")===o.startAtElementId&&(el.data("startingPosition",tempScrollableAreaWidth),foundStartAtElement=!0),tempScrollableAreaWidth+=$(this).outerWidth(!0)}):tempScrollableAreaWidth+=el.data("scrollableArea").outerWidth(!0),foundStartAtElement||el.data("startAtElementId",""),el.data("scrollableAreaWidth",tempScrollableAreaWidth),el.data("scrollableArea").width(el.data("scrollableAreaWidth")),el.data("scrollWrapper").scrollLeft(el.data("startingPosition")),el.data("scrollXPos",el.data("startingPosition"))}})}(jQuery,window,document);