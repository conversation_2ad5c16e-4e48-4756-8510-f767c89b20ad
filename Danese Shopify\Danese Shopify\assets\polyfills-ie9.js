!function(a){function t(a){var t=a.attr("placeholder");n(a,t),a.focus(function(n){a.data("changed")!==!0&&a.val()===t&&a.val("")}).blur(function(n){""===a.val()&&a.val(t)}).change(function(t){a.data("changed",""!==a.val())})}function n(a,t){""===a.val()?a.val(t):a.data("changed",!0)}function e(t){var n=c(t);t.after(n),""===t.val()?t.hide():n.hide(),a(t).blur(function(a){""===t.val()&&(t.hide(),n.show())}),a(n).focus(function(a){t.show().focus(),n.hide()})}function c(t){return a("<input>").attr({placeholder:t.attr("placeholder"),value:t.attr("placeholder"),id:t.attr("id"),readonly:!0}).addClass(t.attr("class"))}function o(t){t.find(":input[placeholder]").each(function(){a(this).data("changed")!==!0&&a(this).val()===a(this).attr("placeholder")&&a(this).val("")})}"placeholder"in document.createElement("input")||a(document).ready(function(){a(":input[placeholder]").not(":password").each(function(){t(a(this))}),a(":password[placeholder]").each(function(){e(a(this))}),a("form").submit(function(t){o(a(this))})})}(jQuery);