
    if ($('.mySlides').length != 0) {

        var slideIndex = 1;
        showSlides(slideIndex);

        function plusSlides(n) {
            showSlides(slideIndex += n);
        }

        function currentSlide(n) {
            showSlides(slideIndex = n);
        }

        function showSlides(n) {
            var i;
            var slides = document.getElementsByClassName("mySlides");
            var dots = document.getElementsByClassName("dot");
            if (n > slides.length) {
                slideIndex = 1
            }
            if (n < 1) {
                slideIndex = slides.length
            }
            for (i = 0; i < slides.length; i++) {
                slides[i].style.display = "none";
                 slides[i].className = "mySlides fade product-slider";
            }
            for (i = 0; i < dots.length; i++) {
                dots[i].className = dots[i].className.replace(" active", "");
            }
            
            slides[slideIndex - 1].style.display = "block";
            slides[slideIndex - 1].className += " current";
            if (dots.length > 0)
            {
               
                dots[slideIndex - 1].className += " active ";
            }
        }
    }
    
    function mobileViewFunction1(viewSize) {
        if (viewSize.matches) {
            var i = 0;
            $(".product-block").each(function () {
                if ($(this).is('.hidden-xs')) {
                }
                else
                {
                    var reminder = i % 2;
                    if (reminder == 0) {
                        $(this).addClass("block-odd");
                    }
                    // else if (reminder == Math.round(reminder)) {
                    // }
                    i++;
                }
            });
            var mobileDropDownHeight = $(window).height()- $('.logo-containar').outerHeight() - $('#mobileMenu').outerHeight();
            $('#mobile-dropdown').height(mobileDropDownHeight);
        }
        else
        {}
    }

    var viewSize = window.matchMedia("(max-width: 865px)");
    mobileViewFunction1(viewSize); // Call listener function at run time
    viewSize.addListener(mobileViewFunction1); // Attach listener function on state changes


    $("body").on('click', '.mobile-nav-list', function () {
        if ($('.mobile-nav-sub-menu[data-id="' + $(this).attr('data-value') + '"]').is('.show'))
            $('.mobile-nav-sub-menu[data-id="' + $(this).attr('data-value') + '"]').removeClass("show");
        else
        {
            $(".mobile-nav-sub-menu").removeClass("show");
            $('.mobile-nav-sub-menu[data-id="' + $(this).attr('data-value') + '"]').addClass("show");
        }
    });

    $("body").on('click', '.search-content-block', function () {
        $('.search-box').addClass("search-box-content");
        $('.search-content-block').addClass("search-content-block-hidden");
    });

    $("body").on('click', '.close-search', function () {

        $('.search-box').removeClass("search-box-content");
        $('.search-content-block').removeClass("search-content-block-hidden");

    });

    $("body").on('click', '.close-searchMob', function () {
    	
    	$('.mobile-nav-sub-menu').removeClass("show");
    	
    });
    
    function mobileDropdown() {
        if ($("#mobile-dropdown").is('.show'))
        {
            $("#mobile-dropdown").removeClass("show");
            $("#mobileMenu").html("menu");
            $("#mobileMenu").removeClass("icon-x");
        }
        else
        {
            $("#mobile-dropdown").addClass("show");
            $("#mobileMenu").html("");
            $("#mobileMenu").addClass("icon-x");
        }
    }

    $(document).mouseup(function (e)
    {
        var container = $('.language-dropdown-content');
        // if the target of the click isn't the container nor a descendant of the container
        if (!container.is(e.target) && container.has(e.target).length === 0)
            $('.language-dropdown').addClass('hidden-lg hidden-md hidden-sm hidden-xs');
    });

    $(document).ready(function () {
        $('.language-item').on('click', function (e) {
            $('.language-item').removeClass('selected-language');
            $(this).addClass('selected-language');
        });
        var isPrev=false;
        var isNext=false;
         
       
    });
    

(function ($) {
    $.fn.SHorizontal = function (options) {
        if (options == 'rollTo') {
            var args = (Array.prototype.slice.call(arguments, 1));
            this.trigger(SHorizontal_rollto, args);
            return;
        }
        if (options == 'get') {
            return this.children('div.' + class_item_selected).index();
        }
        if (options == 'getText') {
            return this.children('div.' + class_item_selected).children(".internal").attr("data-href");
        }
        var opts = $.extend({}, $.fn.SHorizontal.defaults, options);
        this.off(SHorizontal_rollto).on(SHorizontal_rollto, function (event, to) {
            if (to) {
                rollTo($(this), to);
            }
        });
        var style = {};
        style['position'] = 'relative';
        style[xform] = 'rotateY(' + opts.rotation + 'deg)';
        style[xform + '-style'] = 'preserve-3d';
        this.css(style).addClass('SHorizontal-container');
        var item = '';
        if (opts.items.length) {
            var center_index = 0;
            if (opts.center == 'first') {
                center_index = 0;
            } else if (opts.center == 'last') {
                center_index = opts.items.length - 1;
            } else if (opts.center == 'center') {
                center_index = parseInt(opts.items.length / 2);
            } else if ($.isNumeric(opts.center) && (opts.center >= 0) && (opts.center < opts.items.length)) {
                center_index = opts.center;
            } else if (opts.center >= opts.items.length) {
                center_index = opts.items.length - 1;
            } else {
                center_index = 0;
            }
            var distance = parseInt(this.height() / 2);
            if ($.isNumeric(opts.distance)) {
                distance = opts.distance;
            }
            
            var decadeWidth = decadeWidth = $('.decade-container').outerWidth();
                    decadeWidth = Math.round(decadeWidth/8);
//                    decadeWidth = Math.round((decadeWidth*60)/200);
            var viewSize = window.matchMedia("(max-width: 991px)");
                if (!viewSize.matches) 
                    decadeWidth = 45;
                
            var style = 'position: absolute;left:'+decadeWidth+'%;width: auto;height:auto;top: 22%;';
            for (var i = 0; i < opts.items.length; i++) {
                var displayed = "";
                if (Math.abs(i - center_index) > opts.displayed_length) {
                    displayed = "display:none;";
                }
                var angle = opts.angle * (center_index - i);
                var max_angle = opts.angle * opts.displayed_length;
                var transform = 'transform:rotateY(' + angle + 'deg) translate3d(0,0,' + distance + 'px);-webkit-transform:rotateY(' + angle + 'deg) translate3d(0,0,' + distance + 'px);';
                item += '<div class="SHorizontal-item ' + ((i == center_index) ? class_item_selected : '') + '" style="' + transform + displayed + style + 'opacity:1;">' + opts.items[i] + '</div>';
            }
            return this.html(item).data('cur-angle', (center_index * opts.angle)).off(start).on(start, function (e) {
                var ini = $(this);
                ini.addClass('w-roll-touched').data('initialtouch', getEventPos(e).x);
                return false;
            }).off(move).on(move, function (e) {
                var ini = $(this);
                if (ini.is('.w-roll-touched')) {
                    var deltaY = ini.data('initialtouch') - getEventPos(e).x;
                    var mainAngle = parseInt(ini.data('cur-angle')) - parseInt(deltaY / 2);
                    var maxAngle = (opts.items.length - 1) * opts.angle;
                    if (mainAngle < 0) {
                        var excess = 0 - mainAngle;
                        mainAngle = -(25 * excess / (excess + 25));
                    } else if (mainAngle > maxAngle) {
                        var excess = mainAngle - maxAngle;
                        mainAngle = maxAngle + (25 * excess / (excess + 25));
                    }
                    ini.children('div').each(function () {
                        var curr = $(this);
                        var options = {};
                        var currAngle = mainAngle - (curr.index() * opts.angle);
                        options['display'] = '';
                        if (Math.abs(currAngle) > opts.displayed_length * opts.angle) {
                            options['display'] = 'none';
                        }
                        var max_angle = opts.angle * opts.displayed_length;
                        options[xform] = 'rotateY(' + currAngle + 'deg) translateZ(' + distance + 'px)';
                        options['opacity'] = 1;
                        curr.css(options);
                    });
                }
                return false;
            }).off(end).on(end, function (e) {
                var ini = $(this);
                if (ini.is('.w-roll-touched')) {
                    var deltaY = ini.data('initialtouch') - getEventPos(e).x;
                    var mainAngle = parseInt(ini.data('cur-angle')) - parseInt(deltaY / 2);
                    var maxAngle = (opts.items.length - 1) * opts.angle;
                    var index = Math.round(mainAngle / opts.angle);
                    if (mainAngle < 0) {
                        var excess = 0 - mainAngle;
                        mainAngle = -(25 * excess / (excess + 25));
                        index = 0;
                    } else if (mainAngle > maxAngle) {
                        var excess = mainAngle - maxAngle;
                        mainAngle = maxAngle + (25 * excess / (excess + 25));
                        index = (opts.items.length - 1);
                    }
                    ini.data('cur-angle', mainAngle);
                    rollTo(ini, index);
                }
                ini.removeClass('w-roll-touched')
                return false;
            });
        } else {
            return this;
        }
        function rollTo(objek, index) {
            if (index < 0) {
                index = 0;
            } else if (index >= opts.items.length) {
                index = opts.items.length - 1;
            }
            var fromAngle = parseInt(objek.data('cur-angle'));
            var toAngle = index * opts.angle;
            var deltaAngle = toAngle - fromAngle;
            animationStep(10, 1, function (step, curStep, objek) {
                var mainAngle = easeOutQuad(curStep, fromAngle, deltaAngle, step);
                objek.children('div').each(function () {
                    var curr = $(this);
                    var options = {};
                    var currAngle = mainAngle - (curr.index() * opts.angle);
                    options['display'] = '';
                    if (Math.abs(currAngle) > opts.displayed_length * opts.angle) {
                        options['display'] = 'none';
                    }
                    var max_angle = opts.angle * opts.displayed_length;
                    options[xform] = 'rotateY(' + currAngle + 'deg) translateZ(' + distance + 'px)';
                    options['opacity'] = 1;
                    curr.css(options);
                });
            }, function (objek) {
                objek.children('div').each(function () {
                    var curr = $(this).removeClass(class_item_selected);
                    var options = {};
                    var currAngle = toAngle - (curr.index() * opts.angle);
                    options['display'] = '';
                    if (Math.abs(currAngle) > opts.displayed_length * opts.angle) {
                        options['display'] = 'none';
                    }
                    var max_angle = opts.angle * opts.displayed_length;
                    options[xform] = 'rotateY(' + currAngle + 'deg) translateZ(' + distance + 'px)';
                    options['opacity'] = 1;
                    curr.css(options);
                    if (currAngle == 0) {
                        curr.addClass(class_item_selected);
                    }
                });
                objek.data('cur-angle', toAngle);
                objek.trigger('horizontal_scroll', [index]);
            }, objek);
        };
    };

    $.fn.SHorizontal.defaults = {
        items: [],
        center: 'first',
        distance: 'auto',
        displayed_length: 2,
        angle: 30,
        rotation: 0,
        item_height: 20
    };

    var xform = 'transform';
    ['webkit', 'Moz', 'O', 'ms'].every(function (prefix) {
        var e = prefix + 'Transform';
        if (typeof document.body.style[e] !== 'undefined') {
            xform = e;
        }
    });
    var start = 'touchstart mousedown';
    var move = 'touchmove mousemove';
    var end = 'touchend mouseup mouseleave';
    var SHorizontal_rollto = 'SHorizontal.rollTo';
    var class_item_selected = 'SHorizontal-item-selected';
    function animationStep(step, curStep, stepFunc, doneFunc, objek) {
        if (curStep <= step)
        {
            if (typeof stepFunc == 'function')
                stepFunc(step, curStep, objek);
            curStep = curStep + 1;
            window.requestAnimationFrame(function () {
                animationStep(step, curStep, stepFunc, doneFunc, objek);
            });
        }
        else
        {
            if (typeof doneFunc == 'function')
                doneFunc(objek);
        }
    };

    function getEventPos(e) {
        //jquery event
        if (e.originalEvent) {
            // touch event
            if (e.originalEvent.changedTouches && (e.originalEvent.changedTouches.length >= 1)) {
                return {
                    x: e.originalEvent.changedTouches[0].pageX,
                    y: e.originalEvent.changedTouches[0].pageY
                };
            }
            // mouse event
            return {
                x: e.originalEvent.clientX,
                y: e.originalEvent.clientY
            };
        } else {
            // touch event
            if (e.changedTouches && (e.changedTouches.length >= 1)) {
                return {
                    x: e.changedTouches[0].pageX,
                    y: e.changedTouches[0].pageY
                };
            }
            // mouse event
            return {
                x: e.clientX,
                y: e.clientY
            };
        }
    };

    function easeOutQuad(t, b, c, d) {
        return -c * (t /= d) * (t - 2) + b;
    };
})(jQuery);
    


