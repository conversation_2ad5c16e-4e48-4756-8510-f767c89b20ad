<section class="page__content">
  {% capture contact_form %}
  <div class="contact">
    {% form 'contact', class: 'contact__form' %}
      {% if form.posted_successfully? %}
        <div class="alert alert--success">
          <span class="alert__title">{{ 'contact.form.thank_you' | t }}</span>
        </div>
      {% endif %}

      {% if form.errors %}
        <div class="alert alert--error">
          <span class="alert__title">{{ 'general.forms.errors' | t }}</span>
          {% include 'form-errors' %}
        </div>
      {% endif %}

      <div class="form__control">
        <label class="form__label" for="contact__name">{{ 'contact.form.name' | t }}</label>
        <input type="text" id="contact__name" name="contact[name]">
      </div>

      <div class="form__control">
        <label class="form__label" for="contact__email">{{ 'contact.form.email' | t }}</label>
        <input type="email" id="contact__email" name="contact[email]" required="required">
      </div>

      <div class="form__control">
        <label class="form__label" for="contact__body">{{ 'contact.form.message' | t }}</label>
        <textarea rows="5" cols="75" id="contact__body" name="contact[body]" required="required">{{ form.body }}</textarea>
      </div>

      <input type="submit" class="button button--primary" value="{{ 'contact.form.submit' | t }}">
    {% endform %}
  </div>
  {% endcapture %}

  {% if page.content == blank %}
    <div class="container container--shrink">
      {{ contact_form }}
    </div>
  {% else %}
    <div class="container">
      <div class="grid grid--large">
        <div class="grid__cell 1/2--handheld-and-up 3/5--lap-and-up">
          <div class="rte">
            {{ page.content }}
          </div>
        </div>

        <div class="grid__cell 1/2--handheld-and-up 2/5--lap-and-up">
          {{ contact_form }}
        </div>
      </div>
    </div>
  {% endif %}
</section>

{% if section.settings.map_enabled %}
  <div id="contact-us-map"
       data-company-name="{{ section.settings.map_company_name }}"
       data-street="{{ section.settings.map_street }}"
       data-city="{{ section.settings.map_city }}"
       data-country="{{ section.settings.map_country }}">
  </div>

  {{ '//maps.google.com/maps/api/js?sensor=false&key=AIzaSyDkV2AZxUMLj1Tu4cHUeUf0Ig_1gu12XVU' | script_tag }}
  {{ 'gmap-infobubble.js' | asset_url | script_tag }}

  <script>
    // We cannot use the normal routing we use for other pages, because we cannot know in advance the slug of
    // the contact page
    (function() {
      var loadMap = function() {
        var mapElement = document.getElementById('contact-us-map'),
          geocoder = new google.maps.Geocoder();

        geocoder.geocode({
          address: mapElement.dataset['street'] + ' ' + mapElement.dataset['city'] + ' ' + mapElement.dataset['country']
        }, function(results, status) {
          if (status == google.maps.GeocoderStatus.OK) {
            var googleMap = new google.maps.Map(document.getElementById('contact-us-map'), {
              scrollwheel: false,
              zoom: 17,
              center: results[0].geometry.location,
              mapTypeId: google.maps.MapTypeId.TERRAIN,
              maxZoom: 20
            });

            if (Modernizr.mq('only screen and (max-width: 450px)')) {
              googleMap.panBy(100, -50);
            }

            var infoMarker = '<span class="gmap-company-name">' + mapElement.dataset['companyName'] +'</span>'
              + '<span class="gmap-company-street">' + mapElement.dataset['street'] +'</span>'
              + '<span class="gmap-company-city">' + mapElement.dataset['city'] +'</span>'
              + '<span class="gmap-company-country">' + mapElement.dataset['country'] +'</span>';

            var infoBubble = new InfoBubble({
              map: googleMap,
              content: infoMarker,
              position: results[0].geometry.location,
              shadowStyle: 0,
              padding: 20,
              backgroundColor: {{ settings.background | json }},
              borderRadius: 0,
              arrowSize: 24,
              borderWidth: 0,
              disableAutoPan: true,
              hideCloseButton: true,
              arrowPosition: 0,
              arrowStyle: 2
            });

            infoBubble.open();
          }
        });
      };

      loadMap();

      document.addEventListener('shopify:section:load', function() {
        loadMap();
      });
    })();
  </script>
{% endif %}

{% schema %}
{
  "name": "Contact page",
  "settings": [
    {
      "type": "header",
      "content": "Map"
    },
    {
      "type": "checkbox",
      "id": "map_enabled",
      "label": "Enable",
      "default": false
    },
    {
      "type": "text",
      "id": "map_company_name",
      "label": "Company name"
    },
    {
      "type": "text",
      "id": "map_street",
      "label": "Street",
      "info": "1 Allen Street"
    },
    {
      "type": "text",
      "id": "map_city",
      "label": "Zip Code and City",
      "info": "10007 New York"
    },
    {
      "type": "text",
      "id": "map_country",
      "label": "Country",
      "info": "United States"
    }
  ]
}
{% endschema %}