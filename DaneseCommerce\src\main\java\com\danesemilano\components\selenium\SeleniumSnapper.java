package com.danesemilano.components.selenium;

import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import javax.imageio.ImageIO;
import net.yadaframework.components.YadaUtil;
import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import ru.yandex.qatools.ashot.AShot;
import ru.yandex.qatools.ashot.Screenshot;
import ru.yandex.qatools.ashot.comparison.ImageDiff;
import ru.yandex.qatools.ashot.comparison.ImageDiffer;
import ru.yandex.qatools.ashot.comparison.PointsMarkupPolicy;
import ru.yandex.qatools.ashot.shooting.ShootingStrategies;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Takes screenshots of web pages, either to send to Percy or to save locally
 *
 */
public class SeleniumSnapper {
	private final transient Logger log = LoggerFactory.getLogger(getClass());
	
	private WebDriver webDriver = null;
	private File snapshotDir = null;
	private File diffDir = null;
	private File baselineDir = null;
	private boolean hasBaseline = false; // True if the baseline folder already has some screenshots
	private int totScreenshotDiffs = 0;
	private int totElementDiffs = 0;

	public SeleniumSnapper(File snapshotDir, WebDriver webDriver, File baselineDir) {
		this.snapshotDir = snapshotDir;
		this.diffDir = new File(snapshotDir, "diff");
		this.baselineDir = baselineDir;
		this.webDriver = webDriver;
		String[] baselineFiles = baselineDir.list();
		hasBaseline = baselineFiles!=null && baselineFiles.length>0;
	}

	/**
	 * Make the page black and white so that image diffing is less susceptible to antialias deviations
	 */
	private void makeBw() {
		String script = "window.seleniumSnapperFilter = document.documentElement.style.filter;"
			+ "document.documentElement.style.filter = 'grayscale(100%)';";
		JavascriptExecutor javascriptExecutor = (JavascriptExecutor) webDriver;
		javascriptExecutor.executeScript(script);
	}
	
	/**
	 * Restore colors
	 */
	private void removeBw() {
		String script = "document.documentElement.style.filter = window.seleniumSnapperFilter;";
		JavascriptExecutor javascriptExecutor = (JavascriptExecutor) webDriver;
		javascriptExecutor.executeScript(script);
	}

	/**
	 * @param name - Name of the snapshot, used to create the file name
	 * @param ignoreElementsSelector - Mechanism used to locate elements within a
	 *                                document and mask them E.g.:
	 *                                By.cssSelector("#weather .blinking_element")
	 */
	public boolean snapshot(String name, By ignoreElementsSelector) {
		log.debug("Snapshot \"{}\"", name);
		removeTestBanner();
		makeBw();
		try {
			// Either save the snapshot to the baseline or in the current test folder, creating a diff
			AShot aShot = new AShot().shootingStrategy(ShootingStrategies.viewportPasting(2000));
			if (ignoreElementsSelector != null) {
				aShot.addIgnoredElement(ignoreElementsSelector);
			}
			Screenshot screenshot = aShot.takeScreenshot(webDriver);
			screenshot.getIgnoredAreas().forEach(coords -> coords.grow(5, 5));
			File newSnapshotFile = new File(snapshotDir, YadaUtil.reduceToSafeFilename(name) + ".png");
			File baselineSnapshotFile = new File(baselineDir, newSnapshotFile.getName());
			if (hasBaseline) {
				snapshotDir.mkdirs();
				ImageIO.write(screenshot.getImage(), "png", newSnapshotFile);
				if (baselineSnapshotFile.exists()) {
					// Compare with baseline
					Screenshot baselineScreenshot = new Screenshot(ImageIO.read(baselineSnapshotFile));
					baselineScreenshot.setIgnoredAreas(screenshot.getIgnoredAreas());

					PointsMarkupPolicy diffMarkupPolicy = new PointsMarkupPolicy();
					diffMarkupPolicy.setDiffSizeTrigger(1);
					ImageDiffer imageDiffer = new ImageDiffer().withDiffMarkupPolicy(diffMarkupPolicy);
					ImageDiff imageDiff = imageDiffer.makeDiff(screenshot, baselineScreenshot);
					if (imageDiff.hasDiff()) {
						totScreenshotDiffs++;
						diffDir.mkdirs();
						File markedSnapshotFile = new File(diffDir, newSnapshotFile.getName());
						ImageIO.write(imageDiff.getMarkedImage(), "png", markedSnapshotFile);
					}
				} else {
					log.warn("Baseline snapshot file {} does not exist", baselineSnapshotFile);
				}
			} else {
				// Just write to the baseline folder
				baselineDir.mkdirs();
				ImageIO.write(screenshot.getImage(), "png", baselineSnapshotFile);
			}

			return true;
		} catch (Exception e) {
			log.error("Snapshot failed: ", e);
			return false;
		} finally {
			removeBw();
		}
	}

	private void removeTestBanner() {
		if (webDriver.findElement(By.cssSelector(".envbanner")) != null) {
			((JavascriptExecutor) webDriver).executeScript("document.getElementsByClassName('envbanner')[0].remove();");
		}
	}

	public boolean snapshot(String name) {
		return this.snapshot(name, null);
	}

	public boolean comparePageData(String name) {
		try {
			File newJsonDataFile = new File(snapshotDir, YadaUtil.reduceToSafeFilename(name) + ".json");
			File baselineJsonDataFile = new File(baselineDir, newJsonDataFile.getName());

			JsonObject newJsonData = getVisibleElementCounts();

			if (hasBaseline) {
				snapshotDir.mkdirs();

				saveJsonToFile(newJsonData, newJsonDataFile);

				JsonObject baselineJsonObject = readJsonFromFile(baselineJsonDataFile);
				List<String> diffOfJsonObjects = getDiffOfJsonObjects(baselineJsonObject, newJsonData);

				if (!diffOfJsonObjects.isEmpty()) {
					diffDir.mkdirs();
					totElementDiffs++;
					try (BufferedWriter writer = new BufferedWriter(new FileWriter(new File(diffDir, newJsonDataFile.getName())))) {
						for (String difference : diffOfJsonObjects) {
							writer.write(difference);
							writer.newLine();  // Write new line
						}
					}
				}
			} else {
				// Just write to the baseline folder
				baselineDir.mkdirs();
				saveJsonToFile(newJsonData, baselineJsonDataFile);
			}
			return true;
		} catch (Exception e) {
			log.error("Snapshot failed: ", e);
			return false;
		} finally {
			removeBw();
		}
	}

	private JsonObject getVisibleElementCounts() {
		final JsonObject newJsonData = new JsonObject();

		Map<String, String> selectors = new HashMap<>();
		selectors.put("totalVisibleButtons", "button");
		selectors.put("totalVisibleInputs", "input");
		selectors.put("totalVisibleTables", "table");
		selectors.put("totalVisibleRows", "tr");
		selectors.put("totalVisibleCells", "td, th");
		selectors.put("totalVisibleForms", "form");
		selectors.put("totalVisibleTextAreas", "textarea");
		selectors.put("totalVisibleSelects", "select");
		selectors.put("totalVisibleModals", ".modal");

		selectors.forEach((key, value) -> newJsonData.addProperty(key, countVisibleElements(value)));
		return newJsonData;
	}

	private long countVisibleElements(String cssSelector) {
		return webDriver.findElements(By.cssSelector(cssSelector)).stream()
				.filter(WebElement::isDisplayed)
				.count();
	}

	private JsonObject readJsonFromFile(File file) {
		Gson gson = new Gson();
		try (FileReader reader = new FileReader(file)) {
			return gson.fromJson(reader, JsonObject.class);
		} catch (IOException e) {
			return new JsonObject();
		}
	}

	private void saveJsonToFile(JsonObject jsonData, File file) throws IOException {
		Gson gson = new Gson();
		try (FileWriter writer = new FileWriter(file)) {
			gson.toJson(jsonData, writer);
		}
	}


	public List<String> getDiffOfJsonObjects(JsonObject obj1, JsonObject obj2) {
		List<String> differences = new ArrayList<>();

		int ignoreThreshold = 2;

		for (String key : obj1.keySet()) {
			JsonElement val1 = obj1.get(key);
			JsonElement val2 = obj2.get(key);

			if (val2 == null && val1.getAsInt() > ignoreThreshold) {
				differences.add(String.format("Key '%s' exists in baseline but not in new data.", key));
				continue;
			}

			if (val1 != null && val2 != null && Math.abs(val1.getAsInt() - val2.getAsInt()) > ignoreThreshold) {
				differences.add(
						String.format("Values for key '%s' differ: baseline is %s, new data is %s.", key, val1, val2));
			}
		}

		for (String key : obj2.keySet()) {
			if (!obj1.has(key) && obj2.get(key).getAsInt() > ignoreThreshold) {
				differences.add(String.format("Key '%s' exists in new data but not in baseline.", key));
			}
		}

		return differences;
	}
	
	public int getTotScreenshotDiffs() {
		return totScreenshotDiffs;
	}

	public int getTotElementDiffs() {
		return totElementDiffs;
	}
}
