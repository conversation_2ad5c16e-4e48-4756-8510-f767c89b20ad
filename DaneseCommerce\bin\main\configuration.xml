<?xml version="1.0" encoding="UTF-8"?>
<configuration>
	<header>
		<result>
			<expressionEngine config-class="org.apache.commons.configuration2.tree.xpath.XPathExpressionEngine"/>
		</result>
	</header>
	<override> <!-- Top files overwrite bottom files -->
		<!-- reloadingRefreshDelay is in millisecondi: 20000 = 20 seconds -->
		<system/>
		<env/>
		<!-- build.properties is in WEB-INF, which is the parent folder of the execution context -->
		<properties fileName="../build.properties" config-optional="true"/>
		<properties fileName="net.yadaframework.yadaweb.properties"/>
		<properties fileName="net.yadaframework.yadawebsecurity.properties" config-optional="true"/>
		<properties fileName="net.yadaframework.yadawebcms.properties" config-optional="true"/>
		<properties fileName="net.yadaframework.yadawebcommerce.properties" config-optional="true"/>
		
		<xml config-name="dev" config-at="config" fileName="conf.webapp.dev.xml" 
			reloadingRefreshDelay="4000" config-optional="true" config-reload="true">
		</xml>
		
		<xml config-name="col" config-at="config" fileName="conf.webapp.col.xml" 
			reloadingRefreshDelay="4000" config-optional="true" config-reload="true">
		</xml>
		
		<xml config-name="prod" config-at="config" fileName="conf.webapp.prod.xml" 
			reloadingRefreshDelay="4000" config-optional="true" config-reload="true">
		</xml>
		
	</override>
</configuration>
