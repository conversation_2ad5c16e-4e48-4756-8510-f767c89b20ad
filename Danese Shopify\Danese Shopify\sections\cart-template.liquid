<header class="page__header {% if cart.item_count == 0 %}page__header--no-border{% endif %}">

  {% include 'breadcrumb' %}

 

  <div class="page__header-wrapper">

    <div class="container">

      {% if cart.item_count == 0 %}

        <h1 class="page__title">

          {{ 'cart.general.empty_title' | t }}

        </h1>

 

        <div class="page__description page__description--centered">

          <p>{{ 'cart.general.empty_subtitle' | t }}</p>

 

          <div class="page__header-actions">

            <a href="{{ shop.url }}" class="button button--primary">{{ 'cart.general.empty_button' | t }}</a>

          </div>

        </div>

      {% else %}

        <h1 class="page__title">

          {{ 'cart.general.title' | t }}

        </h1>

      {% endif %}

    </div>

  </div>

</header>

 

 

{% if cart.item_count > 0 %}

  <form action="/cart" method="POST">

    <div class="cart--mobile">

      <ul class="cart__items">

        {% for line_item in cart.items %}

          <li class="cart-item" data-variant-id="{{ line_item.id }}" data-index="{{ forloop.index }}">

            <div class="cart-item__left">

              <a href="{{ line_item.url }}" class="cart-item__link">

                <img class="cart-item__image" src="{{ line_item.image | img_url: '180x' }}">

              </a>

            </div>

 

            <div class="cart-item__right">

              <a class="cart-item__title">{{ line_item.title }}</a>

 

              {% if line_item.properties.size > 0 %}

                <ul class="cart-item__properties">

                  <li class="cart-item__property">{{ property.first }}: {{ property.last }}</li>

                </ul>

              {% endif %}

 

              <span class="cart-item__subtotal" data-money-convertible>{{ line_item.line_price | money }}</span>

              <input type="text" pattern="[0-9]*" class="cart-item__quantity-input" name="quantity" value="{{ line_item.quantity }}">

              <a class="cart-item__remove" href="/cart/change?quantity=0&line={{ forloop.index }}">{{ 'cart.items.remove' | t }}</a>

            </div>

          </li>

        {% endfor %}

      </ul>

    </div>

 

   <div class="cart--desktop">

      <table>

        <thead>

          <tr>

            <th>{{ 'cart.items.product' | t }}</th>

            <th></th>

            <th class="cart-item__price-header">{{ 'cart.items.price' | t }}</th>

            <th>{{ 'cart.items.quantity' | t }}</th>

            <th>{{ 'cart.items.subtotal' | t }}</th>

            <th></th>

          </tr>

        </thead>

 

        <tbody>

          {% for line_item in cart.items %}

            <tr data-variant-id="{{ line_item.id }}" data-index="{{ forloop.index }}">

              <td class="cart-item__product">

                <a href="{{ line_item.url }}" class="cart-item__link">

                  <img class="cart-item__image" src="{{ line_item.image | img_url: '320x320' }}">

                </a>

              </td>

 

              <td class="cart-item__info table__left">

                {% if section.settings.show_vendor %}

                  <span class="cart-item__brand">{{ line_item.vendor }}</span>

                {% endif %}

 

                <a class="cart-item__title" href="{{ line_item.url }}">{{ line_item.product.title }}</a>

 

                {% if line_item.variant.title != 'Default Title' %}

                  <span class="cart-item__variant">{{ line_item.variant.title }}</span>

                {% endif %}

 

                {% if line_item.properties.size > 0 %}

                  <ul class="cart-item__properties">

                    <li class="cart-item__property">{{ property.first }}: {{ property.last }}</li>

                  </ul>

                {% endif %}

              </td>

 

              <td class="cart-item__price" data-money-convertible>{{ line_item.price | money }}</td>

 

              <td class="cart-item__quantity">

                <span>{{ line_item.quantity }}</span>

 

                <div class="quantity__actions">

                  <a href="/cart/change?quantity={{ line_item.quantity | minus: 1 }}&line={{ forloop.index }}">

                    {% include 'icon' with 'minus' %}

                  </a>

                  <a href="/cart/change?quantity={{ line_item.quantity | plus: 1 }}&line={{ forloop.index }}">

                    {% include 'icon' with 'plus' %}

                  </a>

                </div>

              </td>

 

              <td class="cart-item__subtotal" data-money-convertible>{{ line_item.line_price | money }}</td>

 

              <td class="cart-item__remove">

                <a href="/cart/change?quantity=0&line={{ forloop.index }}" class="icon-cross-container">

                  {% include 'icon' with 'close' %}

                </a>

              </td>

            </tr>

          {% endfor %}

        </tbody>

      </table>

    </div>

 

    <div class="inner">

      <div class="cart__meta cart__meta--mobile">

        <div class="cart__recap">

          <span class="cart__total cart__total" data-money-convertible>{{ cart.total_price | money }}</span>

          <span class="cart__taxes">{{ 'cart.general.shipping_not_included' | t }}</span>

 

          {% if settings.currency_conversion_enabled %}

            <p class="cart__currency-notice rte">{{ 'cart.general.currency_notice' | t: shop_currency: shop.currency }}</p>

          {% endif %}

        </div>

 

 
           

          

            

            

{% comment %}

           

       Tendina di scelta country: quando è italy, il Codice Fiscale diventa required.

       Versione mobile.


{% endcomment %}                        

{% if section.settings.show_notes %}

<div class="cart__instructions">


<div class="ssncontainer mobile">

          

<p class="cart-attribute__field">

  <label>Please select your country:</label><br>

  <select onchange="enableCF(event)" required class="required" id="please-select-your-country-mobile" name="attributes[Please select your country]">

 	<option value="" hidden style="display:none" {% if cart.attributes["Please select your country"] == "" %} selected{% endif %}>---</option> 

	<option value="Italy"{% if cart.attributes["Please select your country"] == "Italy" %} selected{% endif %}>Italy</option>

    <option value="Other European Country">Other European Country</option>

  </select>

</p>

   

            

   <p class="cart-attribute__field">

  <label class="form__label" for="vat-number-mobile" style="margin-top: 25px; font-size: 0.8em;">Partita IVA (VAT Number)</label>

  <input onkeyup="alignFields(event)" style="margin-bottom: 0;" id="vat-number-mobile" type="text" name="attributes[VAT Number]" value="{{ cart.attributes["VAT Number"] }}">

</p>

     

<p class="cart-attribute__field" style="margin-bottom: 0;">

  <label class="form__label" style="font-size: 0.8em;" for="ssn-number-mobile">Codice Fiscale (National Insurance Number)</label>

  <input pattern="\w+" onkeyup="alignFields(event)" class="ssn-number" style="margin-bottom: 0;" id="ssn-number-mobile" type="text" name="attributes[SSN Number]" value="{{ cart.attributes["SSN Number"] }}">

</p>      

            

<label class="cfwarn" class="form__label" style="display: none; font-size: 0.8em; margin-top: 15px; margin-bottom: 60px; color: red;">Per una corretta evasione dell'ordine si prega di inserire il proprio codice fiscale</label>

 
           

            

            <div class="form__control cart__note">

              <label class="form__label" for="note">{{ 'cart.general.add_note' | t }}</label>

              <textarea onkeyup="alignFields(event)" name="note" cols="30" rows="5">{{ cart.note }}</textarea>

            </div>
		</div>
</div>
   {% endif %}

        <div class="cart__checkout">

          <div class="button-wrapper">

            <button type="submit" name="checkout" class="button button--primary">{{ 'cart.general.checkout' | t }}</button>

          </div>

        </div>

      </div>

  

     

     

  

      

      <div class="cart__meta cart__meta--desktop grid grid--large">

       

        

        

        

        

        

        

           

 

           

            

            

            

{% comment %}
         

       Tendina di scelta country: quando è italy, il Codice Fiscale diventa required.

       Versione desktop.


{% endcomment %}    

            

{% if section.settings.show_notes %}

  <div class="cart__instructions grid__cell 3/5--handheld-and-up">

<div class="ssncontainer desktop">

            

<p class="cart-attribute__field">

  <label>Please select your country:</label><br>

  <select onchange="enableCF(event)" required class="required" id="please-select-your-country-desktop" name="attributes[Please select your country]">
	
	<option value="" hidden style="display:none" {% if cart.attributes["Please select your country"] == "" %} selected{% endif %}>---</option> 
  
    <option value="Italy"{% if cart.attributes["Please select your country"] == "Italy" %} selected{% endif %}>Italy</option>

    <option value="Other European Country">Other European Country</option>

  </select>

</p>

           

   <p class="cart-attribute__field">

  <label for="vat-number-desktop">Partita IVA (VAT Number)</label>

  <input onkeyup="alignFields(event)" style="margin-bottom: 0;" id="vat-number-desktop" type="text" name="attributes[VAT Number]" value="{{ cart.attributes["VAT Number"] }}">

</p>

     

<p class="cart-attribute__field" style="margin-bottom: 0;">

  <label for="ssn-number-desktop">Codice Fiscale (National Insurance Number)</label>

  <input pattern="\w+" onkeyup="alignFields(event)" class="ssn-number" style="margin-bottom: 0;" id="ssn-number-desktop" type="text" name="attributes[SSN Number]" value="{{ cart.attributes["SSN Number"] }}">

</p>      

            

<label class="cfwarn" style="display: none; margin-top: 15px; margin-bottom: 60px; color: red;">

Per una corretta evasione dell'ordine si prega di inserire il proprio codice fiscale</label>

 


 
            <div class="form__control cart__note">

              <label class="form__label" for="note">{{ 'cart.general.add_note' | t }}</label>

              <textarea onkeyup="alignFields(event)" name="note" cols="30" rows="5">{{ cart.note }}</textarea>

            </div>
</div>               

  </div>

{% endif %}

 
 
 
 
 

        <div class="cart__checkout grid__cell {% if section.settings.show_notes %}2/5--handheld-and-up{% endif %}">

          <div class="cart__recap--desktop">

            <span class="cart__total cart__total" data-money-convertible>{{ cart.total_price | money }}</span>

            <span class="cart__taxes">{{ 'cart.general.shipping_not_included' | t }}</span>

 

            {% if settings.currency_conversion_enabled %}

              <p class="cart__currency-notice rte">{{ 'cart.general.currency_notice' | t: shop_currency: shop.currency }}</p>

            {% endif %}

          </div>

 

          <div class="button-wrapper">

            <button type="submit" name="checkout" class="button button--primary">{{ 'cart.general.checkout' | t }}</button>

          </div>

        </div>

      </div>

    </div>

  </form>

{% endif %}

 

 

{% comment %}

       Script per allineamento campi mobile e desktop.    

       Script per tendina di scelta country: quando è italy, il Codice Fiscale diventa required.

{% endcomment %}                             

    

 <script>

  function alignFields(e, sourceField) {
	// Tiene allineati i campi mobile e desktop quando l'utente digita
     if (e!=null) {
     	sourceField = e.target;
     }
	 var sourceContainer = sourceField.closest(".ssncontainer");
	 var sourceIsMobile = sourceContainer.classList.contains("mobile");
	 var destContainer = sourceIsMobile ? document.querySelector(".ssncontainer.desktop") : document.querySelector(".ssncontainer.mobile");
	 var name=sourceField.name;
	 destContainer.querySelector("[name='"+name+"']").value = sourceField.value;
  }

  function enableCF(e, select) {
    if (e!=null) {
		select = e.target;
    }
    var isItaly = select.value=="Italy";
    var container = select.closest(".ssncontainer");
    document.querySelector(".ssn-number").required = isItaly;
    container.querySelector(".cfwarn").style.display = isItaly?"block":"none";
 	alignFields(e, select);
 	
  }
   
   // Questi servono le volte successive quando il select è già selezionato e l'evento non parte.
   enableCF(null, document.getElementById("please-select-your-country-desktop"));
   enableCF(null, document.getElementById("please-select-your-country-mobile"));

</script>

 

 

 

 

{% if section.settings.show_shipping_estimator and cart.item_count > 0 %}

  <section class="shipping-estimator">

    <div class="inner">

      <h3 class="shipping-estimator__title">{{ 'cart.shipping_estimator.title' | t }}</h3>

 

      <div class="shipping-estimator__form">

        <div class="form__control">

          <label class="form__label" for="address_country">{{ 'cart.shipping_estimator.country' | t }}</label>

 

          <div class="styled-select">

            {% include 'icon' with 'dropdown-arrow' %}

            <select id="address_country" name="shipping_address[country]" data-default="{% if shop.customer_accounts_enabled and customer %}{{ customer.default_address.country }}{% elsif section.settings.shipping_estimator_default_country != '' %}{{ section.settings.shipping_estimator_default_country }}{% endif %}">{{ country_option_tags }}</select>

          </div>

        </div>

 

        <div class="form__control" id="address_province_container" style="display: none;">

          <label class="form__label" for="address_province" id="address_province_label">{{ 'cart.shipping_estimator.province' | t }}</label>

 

          <div class="styled-select">

            {% include 'icon' with 'dropdown-arrow' %}

            <select id="address_province" name="shipping_address[province]" data-default="{% if shop.customer_accounts_enabled and customer and customer.default_address.province != '' %}{{ customer.default_address.province }}{% endif %}"></select>

          </div>

        </div>

 

        <div class="form__control form__control--zip">

          <label class="form__label" for="address_zip">{{ 'cart.shipping_estimator.zip' | t }}</label>

          <input type="text" id="address_zip" name="shipping_address[zip]"{% if shop.customer_accounts_enabled and customer %} value="{{ customer.default_address.zip }}"{% endif %} required="required">

        </div>

 

        <button type="submit" class="button button--secondary shipping-estimator__submit">{{ 'cart.shipping_estimator.submit' | t }}</button>

      </div>

 

      <div class="shipping-estimator__results" style="display: none">

        <h4 class="shipping-estimator__results-title"></h4>

        <ul class="shipping-estimator__list"></ul>

      </div>

    </div>

  </section>

{% endif %}

 

{% schema %}

{

  "name": "Cart page",

  "settings": [

    {

      "type": "checkbox",

      "id": "show_notes",

      "label": "Enable order notes",

      "default": true

    },

    {

      "type": "checkbox",

      "id": "show_vendor",

      "label": "Show vendor",

      "default": true

    },

    {

      "type": "checkbox",

      "id": "show_shipping_estimator",

      "label": "Show shipping rates calculator",

      "default": true

    },

    {

      "type": "text",

      "id": "shipping_estimator_default_country",

      "label": "Default country to use",

      "info": "If your customer is logged-in, the country in his default shipping address will be selected.",

      "default": "United States"

    }

  ]

}

{% endschema %}