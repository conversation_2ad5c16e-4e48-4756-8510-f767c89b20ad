package com.danesemilano.persistence.repository;

import java.util.List;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.danesemilano.persistence.entity.Designer;
import com.danesemilano.persistence.entity.Product;

@Repository
@Transactional(readOnly = true) 
public class DesignerRepoDao {
	
	@PersistenceContext
	EntityManager em;
	
	/**
	 * Trova i designer relativi a una lista di prodotti
	 * @param products
	 * @return
	 */
	public List<Designer> findByProducts(List<Product> products) {
		String sql = "select distinct d from Designer d join d.products p where p in :products";
		return em.createQuery(sql, Designer.class)
			.setParameter("products", products)
			.getResultList();
	}
	
	/**
	 * Update a new thumbnail image attachment to a designer
	 * @param designerId
	 * @param yadaAttachedFileId
	 */
	@Transactional(readOnly = false) 
	public void setThumbnailImage(Long designerId, Long yadaAttachedFileId) {
		String sql = "UPDATE Designer SET image_id=:yadaAttachedFileId where id=:designerId";
		em.createNativeQuery(sql)
			.setParameter("designerId", designerId)
			.setParameter("yadaAttachedFileId", yadaAttachedFileId)
			.executeUpdate();
	}
	
	public List<Designer> findAllByProduct_id(Long productId) {
		String sql = "Select * from Designer d join Designer_YadaProduct y where d.id=y.designers_id and products_id=:productId";
		return em.createNativeQuery(sql, Designer.class)
			.setParameter("productId", productId)
			.getResultList();
	}
	
	/**
	 * Trova tutti i designers ordinati alfabeticamente
	 * @return
	 */
	public List<Designer> findAllByPublishedTrueOrderBySurnameAsc() {
		String sql = "from Designer where published=true order by surname asc";
		return em.createQuery(sql, Designer.class)
			.getResultList();
	}
	
	@Transactional(readOnly = false)
	public Designer save(Designer entity) {
		if (entity==null) {
			return null;
		}
		if (entity.getId()==null) {
			em.persist(entity);
			return entity;
		}
		return em.merge(entity);
	}

	public Designer findOne(long id) {
		return em.find(Designer.class, id);
	}	
}
