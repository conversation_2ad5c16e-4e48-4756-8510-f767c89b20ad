package com.danesemilano.persistence.entity;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.ElementCollection;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.MapKeyColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.persistence.Transient;
import jakarta.persistence.UniqueConstraint;
import jakarta.persistence.Version;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.context.i18n.LocaleContextHolder;

import com.fasterxml.jackson.annotation.JsonProperty;

import net.yadaframework.core.CloneableFiltered;
import net.yadaframework.persistence.entity.YadaAttachedFile;

@Entity
public class Designer implements CloneableFiltered, Serializable { 
	private static final long serialVersionUID = 1L;
	
	public static class SimpleJson{}
	
	// For synchronization with external databases
		@Column(columnDefinition="DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")
		@Temporal(TemporalType.TIMESTAMP)
		private Date modified;
		
		// For optimistic locking
		@Version
		private long version;
		
		private boolean published = false;
		
		@Id
		@GeneratedValue(strategy= GenerationType.IDENTITY)
		private Long id;
		
		@Column(length=64)
		private String name;
		
		@Column(length=64)
		private String surname; // Qui mettiamo il cognome oppure la ditta
		
		@ElementCollection
		@Column(length=8192)
		@MapKeyColumn(name="locale", length=32) // th_TH_TH_#u-nu-thai
		private Map<Locale, String> description = new HashMap<>(); // a kind of small description
		
		
//		@OneToMany(cascade=CascadeType.REMOVE, orphanRemoval=true)
//		@JoinTable(name="Designer_images")
//		@OrderBy("sortOrder")
//		protected List<YadaAttachedFile> galleryImages;
		
		/**
		 * The main image to show in lists etc.
		 */
		@OneToOne(cascade= CascadeType.REMOVE, orphanRemoval=true)
		private YadaAttachedFile image;
		
//		//@OneToMany(mappedBy="name", cascade=CascadeType.ALL, fetch = FetchType.EAGER)
//		//@OneToMany
		@ManyToMany(fetch= FetchType.EAGER)
		@Fetch(FetchMode.SUBSELECT)
		@JoinTable(
			uniqueConstraints = @UniqueConstraint(columnNames={"designers_id", "products_id"})
		)
		private List<Product> products;
//		
//		@OneToMany(cascade=CascadeType.REMOVE, orphanRemoval=true)
//		@JoinTable(name="YadaProduct_galleryImages")
//		@OrderBy("sortOrder")
//		protected List<YadaAttachedFile> galleryImages;

		/*
		@Column(length=64)
		protected List<String> images;
		*/
		
		@Transient
		@JsonProperty("DT_RowId")
		public String getDT_RowId() {
			return this.getClass().getSimpleName()+"#"+this.id;
		}
		
		public Long getId() {
			return id;
		}

		public void setId(Long id) {
			this.id = id;
		}

		public Date getModified() {
			return modified;
		}

		public void setModified(Date modified) {
			this.modified = modified;
		}

		public long getVersion() {
			return version;
		}

		public void setVersion(long version) {
			this.version = version;
		}
		
		public String getName() {
			return name;
		}

		public void setName(String name) {
			this.name = name;
		}

		public String getSurname() {
			return surname;
		}

		public void setSurname(String surname) {
			this.surname = surname;
		}

		public Map<Locale, String> getDescription() {
			return description;
		}

		public void setDescription(Map<Locale, String> description) {
			this.description = description;
		}
		
		/**
		 * Returns the localized description in the current request locale
		 * @return
		 */
		public String getLocalDescription() {
			return description.get(LocaleContextHolder.getLocale());
		}
		
		@Transient
		public String getNameSurname() {
			return (StringUtils.trimToEmpty(name) + " " + StringUtils.trimToEmpty(surname)).trim();
		}
		
		/*
		public List<String> getImages() {
			return images;
		}

		public void setImages(List<String> images) {
			this.images = images;
		}
		*/
//		
//		public List<YadaAttachedFile> getGalleryImages() {
//			return galleryImages;
//		}
//
//		public void setGalleryImages(List<YadaAttachedFile> galleryImages) {
//			this.galleryImages = galleryImages;
//		}
		
		@Override
		public Field[] getExcludedFields() {
			// TODO Auto-generated method stub
			return null;
		}
		
//		//@OneToMany(mappedBy="subfamily")
//		//@javax.persistence.OrderBy("pos")
		public List<Product> getProducts() {
			return products;
		}

		public void setProducts(List<Product> products) {
			this.products = products;
		}
		
		@Override
		public int hashCode() {
			if (id!=null) {
				return id.hashCode();
			}
			return super.hashCode();
		}
		
		@Override
		public boolean equals(Object obj) {
			if (obj==null) {
				return false;
			}
			if (obj instanceof Designer && this.id!=null) {
				return this.id.equals(((Designer)obj).getId());
			}
			return super.equals(obj);
		}

//		public List<YadaAttachedFile> getGalleryImages() {
//			return galleryImages;
//		}
//
//		public void setGalleryImages(List<YadaAttachedFile> galleryImages) {
//			this.galleryImages = galleryImages;
//		}

		public YadaAttachedFile getImage() {
			return image;
		}

		public void setImage(YadaAttachedFile image) {
			this.image = image;
		}

		public boolean isPublished() {
			return published;
		}

		public void setPublished(boolean published) {
			this.published = published;
		}
}
