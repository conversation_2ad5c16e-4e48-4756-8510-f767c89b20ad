package com.danesemilano.persistence.entity;

import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.ElementCollection;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.MapKeyColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.UniqueConstraint;

import org.springframework.context.i18n.LocaleContextHolder;

import net.yadaframework.persistence.entity.YadaAttachedFile;
import net.yadaframework.persistence.entity.YadaPersistentEnum;

@Entity
// Had to copy YadaArticle in Danese in order to remove the joined hierarchy
public class Article extends YadaArticle {
	private static final long serialVersionUID = 1L;

	@ElementCollection
	@Column(length=255)
	@MapKeyColumn(name="locale", length=32)
	private Map<Locale, String> language = new HashMap<>();
	
	@ElementCollection
	@Column(length=255)
	@MapKeyColumn(name="locale", length=32) 
	private Map<Locale, String> finish = new HashMap<>();
	
	@ManyToMany
	@JoinTable(
		name="Article_tipology",
		uniqueConstraints = @UniqueConstraint(columnNames={"tipology_id", "Article_id"})
	)
	protected List<YadaPersistentEnum<?>> tipology;
	
	@Column(length=64)
	private String source;
	
	private int quantity=0;
	
	@Column(length=32)
	private String itemSize;
	
	@ElementCollection
	@Column(length=255)
	@MapKeyColumn(name="locale", length=32) 
	private Map<Locale, String> shopUrl = new HashMap<>();
	
	@OneToOne(cascade=CascadeType.REMOVE, orphanRemoval=true)
	private YadaAttachedFile silhouette;

	/**
	 * Returns the localized color in the current request locale
	 * @return
	 */
	public String getLocalColor() {
		return color.get(LocaleContextHolder.getLocale());
	}

	public Map<Locale, String> getLanguage() {
		return language;
	}

	public void setLanguage(Map<Locale, String> language) {
		this.language = language;
	}

	public Map<Locale, String> getFinish() {
		return finish;
	}

	public void setFinish(Map<Locale, String> finish) {
		this.finish = finish;
	}
	
	/**
	 * Returns the localized finish in the current request locale
	 * @return
	 */
	public String getLocalFinish() {
		return finish.get(LocaleContextHolder.getLocale());
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}
	

	public Map<Locale, String> getShopUrl() {
		return shopUrl;
	}

	public void setShopUrl(Map<Locale, String> shopUrl) {
		this.shopUrl = shopUrl;
	}
	
	/**
	 * Returns the localized finish in the current request locale
	 * @return
	 */
	public String getLocalShopUrl() {
		return shopUrl.get(LocaleContextHolder.getLocale());
	}

	public YadaAttachedFile getSilhouette() {
		return silhouette;
	}

	public void setSilhouette(YadaAttachedFile silhouette) {
		this.silhouette = silhouette;
	}

	public int getQuantity() {
		return quantity;
	}

	public void setQuantity(int quantity) {
		this.quantity = quantity;
	}

	public String getItemSize() {
		return itemSize;
	}

	public void setItemSize(String itemSize) {
		this.itemSize = itemSize;
	}

	public List<YadaPersistentEnum<?>> getTipology() {
		return tipology;
	}

	public void setTipology(List<YadaPersistentEnum<?>> tipology) {
		this.tipology = tipology;
	}


}
