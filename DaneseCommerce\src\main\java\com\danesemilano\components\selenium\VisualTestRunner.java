package com.danesemilano.components.selenium;

import jakarta.annotation.PostConstruct;
import net.yadaframework.components.YadaUtil;
import net.yadaframework.components.YadaWebUtil;
import net.yadaframework.core.YadaConfiguration;
import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.io.File;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicBoolean;

@Component
@Scope(value = "prototype") // One new instance each time
public class VisualTestRunner implements AutoCloseable {
    @Autowired
    protected YadaUtil yadaUtil;
    @Autowired
    protected YadaWebUtil yadaWebUtil;
    @Autowired
    protected YadaConfiguration config;

    private final Locale defaultPageLocale = Locale.ITALIAN;
    private final Logger log = LoggerFactory.getLogger(getClass());
    private final boolean isMobileDevice;

    protected WebDriver webDriver;
    protected SeleniumSnapper seleniumSnapper;
    private WebDriverWait wait;

    /**
     * Factory to create instances
     *
     * @return
     */
    public static VisualTestRunner newInstance(Boolean isMobileDevice) {
        return YadaUtil.getBean(VisualTestRunner.class, isMobileDevice);
    }

    /**
     * Do not use this constructor but {@link #newInstance(Boolean isMobileDevice)} instead
     */
    public VisualTestRunner() {
        this.isMobileDevice = false; // default
    }

    public VisualTestRunner(Boolean isMobileDevice) {
        this.isMobileDevice = Boolean.TRUE.equals(isMobileDevice);
    }

    @PostConstruct
    public void init() {
        if (seleniumSnapper == null) {
            initSelenium(isMobileDevice ? "visualtestsite_mobile" : "visualtestsite",
                    isMobileDevice ? "--window-size=360,740" : "--window-size=1920,1080");
        }

        wait = new WebDriverWait(webDriver, Duration.ofSeconds(10));
    }


    protected void initSelenium(String basePath, String additionalOptions) {
        ChromeOptions options = new ChromeOptions();

        options.addArguments("--headless");
        options.addArguments(additionalOptions);

        // This is useless to prevent font mismatch because of antialias
        options.addArguments("--disable-font-antialiasing", "--font-render-hinting=none", "--disable-skia-runtime-opts", "--disable-font-subpixel-positioning", "--disable-lcd-text");
        webDriver = new ChromeDriver(options);

        File visualTestDir = new File(config.getBasePathString(), basePath);
        File baselineDir = new File(visualTestDir, "baseline");
        visualTestDir.mkdirs();
        File snapshotDir = yadaUtil.findAvailableNameHighest(visualTestDir, "test", null, "_");
        this.seleniumSnapper = new SeleniumSnapper(snapshotDir, webDriver, baselineDir);

    }

    protected void openHome(Locale locale) {
        String url = makeUrl("/", locale);
        webDriver.get(url);
    }

    protected void openManifesto(Locale locale) {
        String url = makeUrl("/manifesto", locale);
        webDriver.get(url);
    }

    protected void openHistory(Locale locale) {
        String url = makeUrl("/history", locale);
        webDriver.get(url);
    }

    protected void openHistory1957(Locale locale) {
        openHistory(locale);
        String parentClass;
        if (isMobileDevice) {
            parentClass = "//div[contains(@class, 'page-containar hidden-lg')]//div[text()='1957']";
        } else {
            parentClass = "//div[contains(@class, 'page-containar') and contains(@class, 'visible-lg')]/descendant::div[text()='1957']";
        }
        waitAndClickOnElement(By.xpath(parentClass), true);
        waitVisibleElement(By.xpath(isMobileDevice ? "//img[@src='/contents/media/history/7.jpg']" : "//img[@src='/contents/media/history/_7.jpg']"));
    }

    protected void openHistory1957Next(Locale locale) {
        openHistory1957(locale);
        waitAndClickOnElement(By.className("next-scroll-content"), true);
    }

    protected void openProductsAll(Locale locale) {
        String url = makeUrl("/productsAll", locale);
        webDriver.get(url);
    }

    protected void openProduct(Locale locale, Integer productId) {
        String url = makeUrl("/products/" + productId, locale);
        webDriver.get(url);
    }

    protected void openProductDetails(Locale locale, Integer idProduct) {
        String url = makeUrl("/productDetails?idProduct=" + idProduct, locale);
        webDriver.get(url);
    }

    protected void openDownloadArea(Locale locale) {
        String url = makeUrl("/downloadArea", locale);
        webDriver.get(url);
    }

    protected void openContract(Locale locale, Integer contractId) {
        String url = makeUrl("/contract/" + contractId, locale);
        webDriver.get(url);
    }

    protected void openDesigners(Locale locale) {
        String url = makeUrl("/designers", locale);
		webDriver.get(url);
	}

	protected void openDesignerDetails(Locale locale, Integer idDesigner) {
		String url = makeUrl("/designerDetails?idDesigner=" + idDesigner, locale);
		webDriver.get(url);
		waitAndClickOnElement(
				isMobileDevice ? By.xpath("//span[@class='expand-news expand-news-one icon-plus hide visible-xs']")
						: By.xpath("//span[@class='expand-news icon-plus hidden-xs']"),
				true);
	}

    protected void openNews(Locale locale) {
        String url = makeUrl("/news", locale);
        webDriver.get(url);
        waitAndClickOnElement(isMobileDevice ? By.xpath("//span[@class='expand-news expand-news-one icon-plus hide visible-xs']") : By.xpath("//span[@class='expand-news icon-plus hidden-xs']"), true);
    }

    protected void openNewsletter(Locale locale) {
        String url = makeUrl("/newsletter", locale);
        webDriver.get(url);
    }

    protected void openPrivacyPolicy(Locale locale) {
        String url = makeUrl("/privacy-policy", locale);
        webDriver.get(url);
    }

    /**
     * Given an url relative to the language path, return the full url
     *
     * @param relativeUrl like "/project/123"
     * @param locale
     * @return a full url like "https://www.artemide.com/en/project/123?x=y"
     */
    protected String makeUrl(String relativeUrl, Locale locale) {
        String webappAddress = config.getWebappAddress();
        String urlWithLocale = yadaWebUtil.enhanceUrl(relativeUrl, locale, "seleniumTest", "true");
        return yadaWebUtil.makeUrl(webappAddress, urlWithLocale);
    }

    protected List<Locale> getLocales(Integer chosenLanguages) {
        List<Locale> locales = new ArrayList<>();
        if (chosenLanguages == 1) {
            locales.add(new Locale("it", "IT"));
        } else {
            for (int i = 0; i < chosenLanguages; i++) {
                locales.add(config.getLocales().get(i));
            }
        }
        return locales;
    }

    public void close() {
        webDriver.quit();
    }


    private void waitAndClickOnElement(By by) {
        this.waitAndClickOnElement(by, false);
    }

    private void waitAndClickOnElement(By by, boolean scroll) {
        this.locateElement(by, scroll, true);
    }

    private void locateElement(By by, boolean scroll, boolean click) {
		AtomicBoolean keepScrolling = new AtomicBoolean(true);

		try {
			if (scroll) {
				new Thread(() -> {
					while (keepScrolling.get()) {
						try {
							Thread.sleep(150);
							if (keepScrolling.get()) {
								((JavascriptExecutor) webDriver).executeScript("window.scrollBy(0, 250);");
							}
						} catch (Exception ignored) {
							keepScrolling.set(false);
						}
					}

				}).start();
			}

            WebElement button = waitVisibleElement(by);
            keepScrolling.set(false); // Stop the scrolling
            String scriptAction = "arguments[0].scrollIntoView(true);";
            if (click) {
                scriptAction += " arguments[0].click();";
            }
            ((JavascriptExecutor) webDriver).executeScript(scriptAction, button);

		} finally {
			keepScrolling.set(false);
		}
    }

    private WebElement waitVisibleElement(By selector) {
        return wait.until(ExpectedConditions.visibilityOfElementLocated(selector));
    }

	private void pageSnapshot(Integer chosenLanguages, String name, OpenPageAction pageAction) {
        List<Locale> locales = getLocales(chosenLanguages);
        for (Locale locale : locales) {
			log.debug("Testing " + name + " page: snapshot");
			pageAction.open(locale);
			String snapshotName = name + "_" + locale.getLanguage();
			seleniumSnapper.snapshot(snapshotName, By.tagName("video"));
		}
	}

	public SeleniumSnapper getSeleniumSnapper() {
		return seleniumSnapper;
    }

    public void testRegressionSelenium() {
        openHome(defaultPageLocale);
        waitAndClickOnElement(By.xpath("//a[contains(normalize-space(text()),\"PRIVACY POLICY\")]"), true);
        waitAndClickOnElement(By.xpath("//span[@class='privacy-span-text' and normalize-space(text())='Cookie policy profiling']"), true);
        if (!webDriver.getCurrentUrl().endsWith("privacy-policy#cookie-policy-profiling-block")) {
            throw new RuntimeException("Cookie policy profiling not found");
        }
        
        openHome(defaultPageLocale);
        waitAndClickOnElement(By.xpath("//a[@class='footer-span-left' and text()='NEWSLETTER ']"), true);
        
        waitAndClickOnElement(By.xpath("//form[@id='newsletter-form-desk']//span[@class='selected-item' and normalize-space(text())='Nazione']"), true);
        waitAndClickOnElement(By.xpath("//form[@id='newsletter-form-desk']//li[@data-value='Algeria' and normalize-space(text())='Algeria']"), true);
        waitVisibleElement(By.xpath("//form[@id='newsletter-form-desk']//span[@class='selected-item' and normalize-space(text())='Algeria']"));
        
        waitAndClickOnElement(By.xpath("//a[@class='download' and text()='DOWNLOAD']"), true);
        waitAndClickOnElement(By.xpath("//a[@href='javascript:void(0);' and @class='noLoader' and normalize-space(text())='dati tecnici']"));
        waitVisibleElement(By.xpath("//*[@class='download-technical-data visible-lg visible-md visible-sm']//button[@type='submit' and @class='yadaClickedButtonHandler' and normalize-space(text())='PDF']"));

        openProductsAll(defaultPageLocale);
        waitAndClickOnElement(By.xpath("//a[@href='/it/productDetails?idProduct=138']"), true);
        waitAndClickOnElement(By.xpath("//a[@class='icon-big' and @href='/it/products/21']"), true);
        waitAndClickOnElement(By.xpath("//a[@href='/it/productDetails?idProduct=71']"), true);
        waitVisibleElement(By.xpath("//div[@class='bottoneCustom buttonBuy' and normalize-space(text())='acquista']"));

        openContract(defaultPageLocale, 28);
        waitAndClickOnElement(By.xpath("//a[@href='/it/productDetails?idProduct=18']"), true);

        WebElement activeSlider = webDriver.findElement(By.xpath("//div[@class='mySlides fade product-slider current']"));
        waitAndClickOnElement(By.xpath("//a[@class='next icon-big next-scroll-content noLoader' and @onclick='plusSlides(1)']"), true);
        waitAndClickOnElement(By.xpath("//a[@class='next icon-big next-scroll-content noLoader' and @onclick='plusSlides(1)']"), true);
        WebElement activeSlider2 = webDriver.findElement(By.xpath("//div[@class='mySlides fade product-slider current']"));

        if (activeSlider.equals(activeSlider2)) {
            throw new RuntimeException("Slider not changed");
        }

        openDesigners(defaultPageLocale);
        waitAndClickOnElement(By.xpath("//a[@class='category-title designerName' and @href='/it/designerDetails?idDesigner=20' and normalize-space(.)='PaoloRizzatto']"), true);
        waitVisibleElement(By.xpath("//a[@href='/it/productDetails?idProduct=15']"));

        openNews(defaultPageLocale);
        waitAndClickOnElement(By.xpath("//span[@class='icon-waves']"), true);
        waitVisibleElement(By.xpath("//span[@class='title' and normalize-space(text())='Danese Milano @ Milano Design Week 2022']"));
    }
    
    
    public void testMobileRegressionSelenium() {
        openHome(defaultPageLocale);
        openPrivacyPolicy(defaultPageLocale);
        waitAndClickOnElement(By.xpath("//span[@class='privacy-span-text' and normalize-space(text())='Cookie policy profiling']"), true);

        if (!webDriver.getCurrentUrl().endsWith("privacy-policy?seleniumTest=true#cookie-policy-profiling-block")) {
            throw new RuntimeException("Cookie policy profiling not found");
        }
        
        openNewsletter(defaultPageLocale);

        waitAndClickOnElement(By.xpath("//form[@id='newsletter-form-desk']//span[@class='selected-item' and normalize-space(text())='Nazione']"), true);
        waitAndClickOnElement(By.xpath("//form[@id='newsletter-form-desk']//li[@data-value='Algeria' and normalize-space(text())='Algeria']"), true);
        waitVisibleElement(By.xpath("//form[@id='newsletter-form-desk']//span[@class='selected-item' and normalize-space(text())='Algeria']"));
        
        openDownloadArea(defaultPageLocale);
        waitAndClickOnElement(By.xpath("//p[@class='download-area news-description manifesto-description ']/span[@class='title' and normalize-space(text())='Dati tecnici']"));
        waitVisibleElement(By.xpath("//*[@class='row product-box']//button[@type='submit' and @class='yadaClickedButtonHandler' and normalize-space(text())='PDF']"));

        openProductsAll(defaultPageLocale);
        waitAndClickOnElement(By.xpath("//a[@href='/it/productDetails?idProduct=138']"), true);
        waitAndClickOnElement(By.xpath("//a[@class='icon-big' and @href='/it/products/21']"), true);
        waitAndClickOnElement(By.xpath("//a[@href='/it/productDetails?idProduct=71']"), true);
        waitVisibleElement(By.xpath("//div[@class='bottoneCustom' and normalize-space(text())='acquista']"));

        openContract(defaultPageLocale, 28);
        waitAndClickOnElement(By.xpath("//a[@href='/it/productDetails?idProduct=18']"), true);

        WebElement activeSlider = webDriver.findElement(By.xpath("//div[@class='mySlides fade product-slider current']"));
        waitAndClickOnElement(By.xpath("//a[@class='next next-sm noLoader' and @onclick='plusSlides(1)']"), true);
        waitAndClickOnElement(By.xpath("//a[@class='next next-sm noLoader' and @onclick='plusSlides(1)']"), true);
        WebElement activeSlider2 = webDriver.findElement(By.xpath("//div[@class='mySlides fade product-slider current']"));

        if (activeSlider.equals(activeSlider2)) {
            throw new RuntimeException("Slider not changed");
        }

        openDesigners(defaultPageLocale);
        waitAndClickOnElement(By.xpath("//a[@class='category-title designerName' and @href='/it/designerDetails?idDesigner=20' and normalize-space(.)='PaoloRizzatto']"), true);
        waitVisibleElement(By.xpath("//a[@href='/it/productDetails?idProduct=15']"));

        openNews(defaultPageLocale);
        waitAndClickOnElement(By.xpath("//span[@class='icon-waves']"), true);
        waitVisibleElement(By.xpath("//span[@class='title' and normalize-space(text())='Danese Milano @ Milano Design Week 2022']"));
    }
    
    public void testHomeSnapshot(Integer chosenLanguages) {
        openHome(defaultPageLocale);
        waitAndClickOnElement(By.id("cookieAcceptAll"));
        pageSnapshot(chosenLanguages, "home", this::openHome);
    }

    public void testManifestSnapshot(Integer chosenLanguages) {
        pageSnapshot(chosenLanguages, "manifesto", this::openManifesto);
    }

	public void testHistorySnapshot(Integer chosenLanguages) {
		pageSnapshot(chosenLanguages, "history-1957", this::openHistory1957);
		if (!isMobileDevice) {
			pageSnapshot(chosenLanguages, "History-1957-Next", this::openHistory1957Next);
		}
	}

    public void testProductsAllSnapshot(Integer chosenLanguages) {
        pageSnapshot(chosenLanguages, "productsAll", this::openProductsAll);
    }

    public void testProductSnapshot(Integer chosenLanguages, Integer productId) {
        pageSnapshot(chosenLanguages, "product_" + productId, (locale) -> openProduct(locale, productId));
    }

    public void testProductDetailsSnapshot(Integer chosenLanguages, Integer idProduct) {
        pageSnapshot(chosenLanguages, "productDetails_" + idProduct, (locale) -> openProductDetails(locale, idProduct));
    }

    public void testDownloadAreaSnapshot(Integer chosenLanguages) {
        pageSnapshot(chosenLanguages, "downloadArea", this::openDownloadArea);
    }

    public void testContractSnapshot(Integer chosenLanguages, Integer contractId) {
        pageSnapshot(chosenLanguages, "contract_" + contractId, (locale) -> openContract(locale, contractId));
    }

    public void testDesignersSnapshot(Integer chosenLanguages) {
        pageSnapshot(chosenLanguages, "designers", this::openDesigners);
    }

    public void testDesignerDetailsSnapshot(Integer chosenLanguages, Integer idDesigner) {
        pageSnapshot(chosenLanguages, "designerDetails_" + idDesigner, (locale) -> openDesignerDetails(locale, idDesigner));
    }

    public void testNewsSnapshot(Integer chosenLanguages) {
        pageSnapshot(chosenLanguages, "news", this::openNews);
    }

    public void testNewsletterSnapshot(Integer chosenLanguages) {
        pageSnapshot(chosenLanguages, "newsletter", this::openNewsletter);
    }

    public void testPrivacyPolicySnapshot(Integer chosenLanguages) {
        pageSnapshot(chosenLanguages, "privacy-policy", this::openPrivacyPolicy);
    }

    public boolean isMobileDevice() {
        return isMobileDevice;
    }

    @FunctionalInterface
    public interface OpenPageAction {
        void open(Locale locale);
    }
}
