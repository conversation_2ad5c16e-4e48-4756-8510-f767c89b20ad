package com.danesemilano.components;

import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.stereotype.Component;

import net.yadaframework.security.persistence.entity.YadaUserProfile;
import net.yadaframework.security.web.YadaSession;

/**
 * User session
 */
@Component
@Scope(value="session", proxyMode=ScopedProxyMode.TARGET_CLASS) 
public class DncSession extends YadaSession<YadaUserProfile> {
	// Add any attributes that you want to keep for the duration of the user session

}
