<?xml version="1.0" encoding="UTF-8"?>
<project-modules id="moduleCoreId" project-version="1.5.0">
	<wb-module deploy-name="DaneseCommerce">
		<property name="context-root" value="/"/>
		<wb-resource deploy-path="/WEB-INF/classes" source-path="src/main/resources"/>
		<wb-resource deploy-path="/WEB-INF/classes" source-path="src/main/java"/>
		<wb-resource deploy-path="/" source-path="src/main/webapp"/>
		<dependent-module archiveName="YadaWebCMS.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/YadaWebCMS/YadaWebCMS">
			<dependency-type>uses</dependency-type>
		</dependent-module>
		<dependent-module archiveName="YadaWebSecurity.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/YadaWebSecurity/YadaWebSecurity">
			<dependency-type>uses</dependency-type>
		</dependent-module>
		<dependent-module archiveName="YadaWeb.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/YadaWeb/YadaWeb">
			<dependency-type>uses</dependency-type>
		</dependent-module>
	</wb-module>
</project-modules>
