
html {
    box-sizing: border-box;
}
*, *:before, *:after {
    box-sizing: inherit;
}

main {
	display: flex;
    align-items: center;
}

a {
	color: black;
}

.logo img {
	max-width: 98px;
	margin-right: 15px;
}

.titolo {
	line-height: 26px;
	/* font-weight: 300; */
}

.designer {
	/* font-size: 0.9em;
	line-height: 1.05em; */
	font-size: 22px;
    line-height: 27px;
}
.designer > div > div:not(:last-child) {
	margin-bottom: 0.55em;
}

.dettagli {
	/* font-size: 0.9em; */
	line-height: 1.15em;
}
.dettagli > div > div:not(:first-child) {
	font-size: 0.8em;
}
.dettagli > div > div:last-child {
	margin-top: 0.15em;
}

.boxes {
	font-size: 26px;
	display: flex;
	display: -webkit-flex;
	flex-wrap: wrap;
}

.boxes > div {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 291px;
}

.boxes > div:not(.dettagli) {
	border-right: solid 1px black;
}

/* .boxes > div {
	display: flex;
}
 */
.boxes p {
	margin-bottom: 0;
}

.slider {
	padding: 0 0 25% 0;
	/* embed-responsive */
 	position: relative;
    display: block;
    overflow: hidden;
}
.slider video {
	position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0;
}
.slider img {
	display: none;
}

footer {
	border-top: solid 1px black;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    padding: 4px;
    font-size: 12px;
    text-transform: uppercase;
    font-weight: 400;
}

footer > div {
	margin: 0px 82px;
	white-space: nowrap;
	text-align: center;
}
@media (max-width: 991px){
	footer > div {
		margin: 0px;
	}
}

footer .social a:first-child {
	margin-right: 15px;
} 

/* Solo desktop */
@media (min-width: 992px) {
	.dettagli > div {
		position: relative;
    	left: -24px;
    	line-height: 25px;
    	font-size: 21px;
    }
}
/* Non mobile */
@media (min-width: 768px) {
/* 	.row.boxes > div {
		width: 30%;
	}
	.row.boxes > div:first-child {
		width: 20%;
	}
	.row.boxes > div:last-child {
		width: 20%;
	} */
	main {
		height: 86vh;
	}
	.company {
		transform-origin: left top 0;
		-webkit-transform: rotate(-90deg);   
		-moz-transform: rotate(-90deg);
		-ms-transform: rotate(-90deg);
		-o-transform: rotate(-90deg);
		transform: rotate(-90deg);
	}
	a:hover {
		text-decoration: none;
		font-weight: 700;
		color: black;
	}
}

/* Desktop dimensione media */ 
@media (min-width: 992px) and (max-width: 1199px) {
	.boxes {
		font-size: 22px;
	}
	.boxes > div {
		height: 240px;
	}
	footer {
    	font-size: 12px;
	}
	footer > div {
	    margin: 0px 40px;
	}
}

.shop {
	position: relative;
}
.shop img {
	width: 35px;
    margin-left: 10px;
    position: relative;
    bottom: 6px;
}

/**********/
/* Tablet */

@media (min-width: 768px) and (max-width: 991px) {
	main {
    	height: 800px;
	}
	.boxes {
		font-size: 20px;
	}
	.boxes > div {
		height: 284px;
	}
	.titolo {
		height: 300px;
		/* font-size: 1.6em; */
		font-size: 27px;
		line-height: 25px;
	}
	/* .boxes > div:not(.titolo):not(.slider):not(.designer) {
		border-right: solid 1px black;
	} */
	.boxes > div.designer, .boxes > div.slider {
		/* font-size: 1.2em; */
		font-size: 25px;
    	line-height: 25px;
		border-right: none;
	}
	.boxes > div.dettagli {
		border-right: solid 1px black;
	}
	.safari .boxes > div.dettagli {
		border-right: none;
	}
	.safari .boxes > div.slider {
		border-left: solid 1px black;
	}
	.dettagli {
		/* font-size: 1em; */
		font-size: 25px;
    	line-height: 24px;
    	letter-spacing: 3px;
	}
	.slider img {
	    width: 100%;
	}
	footer > div {
    	margin: 0px 30px 20px 30px;
	}
}

/*********************/
/* Tablet and Mobile */

@media (max-width: 991px) {
	.boxes > div {
		padding: 40px;
	}
	.designer, .titolo {
		border-bottom: solid 1px black;
	}
	/* .titolo {
		border-right: none !important;
	} */
}

/**********/
/* Mobile */

@media (max-width: 767px) {
	.container {
		/* margin: 0 20px; */
		width: 100%;
		display: flex;
    	justify-content: center;
	}
	.boxes > div {
		height: 300px;
	}
	.boxes > div:not(.dettagli) {
		border-right: none !important;
		border-bottom: solid 1px black;
	}
	.boxes {
		font-size: 25px;
		margin-bottom: 0;
		width: 300px;
	}
	.logo img {
		margin-right: 15px;
    	margin-left: auto;
	}
/* 	.row {
		margin-left: 20px;
		margin-right: 20px;
	} */
	.boxes > div.titolo {
		border-right: none;
	}
	/* .boxes > div.titolo {
		padding-bottom: 60px;
	} */
	.titolo {
		/* font-size: 1.4em; */
		font-size: 31px;
		line-height: 26px;
	}
	.designer {
    	/* font-size: 1.1em; */
    	font-size: 25px;
	}
	.boxes > div.slider {
		padding: 0;
	}
	.slider img {
		width: 100%;
		display: block;
	}
	.dettagli {
    	/* font-size: 0.9em; */
    	font-size: 23px;
    	letter-spacing: 4px;
	}
	footer {
		display: block;
		font-size: 15px;
	    text-align: center;
	}
	footer > div {
		margin-bottom: 10px;
	}
	.comingSoon {
		font-size: 13px;
		margin-bottom: 30px;
		width: 100%;
	}
	.company {
		text-align: center;
	}
	.shop {
		width: 100%;
	}
}
@media (min-width: 768px) and  (max-width: 991px){
	.dnc .container {
	    width: 571px;
	}
	.dnc .boxes .col-sm-6 {
		width: 49.9%;
	}
}
@media (min-width: 992px){
	footer.versioneDesktop{
	    display: block;
	}
	footer.versioneMobile{
	    display: none;
	}
	 .dnc .boxes .dettagli {
		width: 24%;
	}
}
@media (max-width: 991px){
	footer.versioneDesktop{
	    display: none;
	}
}
	