package com.danesemilano.persistence.entity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.SortedSet;
import java.util.TreeSet;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.ElementCollection;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.MapKeyColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.OrderBy;
import jakarta.persistence.UniqueConstraint;

import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.SortNatural;
import org.springframework.context.i18n.LocaleContextHolder;

import net.yadaframework.persistence.entity.YadaAttachedFile;
import net.yadaframework.persistence.entity.YadaPersistentEnum;

@Entity
public class Product extends YadaProduct {

	private static final long serialVersionUID = 1L;
	
	@Column(length=255)
	private String productName;
	
	@ManyToMany(mappedBy="products", fetch=FetchType.EAGER)
	@Fetch(FetchMode.SUBSELECT)
	@OrderBy("surname")
	private List<Designer> designers = new ArrayList<>();
	
	@ManyToMany(mappedBy="products", fetch= FetchType.EAGER)
	@SortNatural // Ordino i tag in base al nome nel locale corrente
	private SortedSet<Tag> tags  = new TreeSet<>();
	
	@ElementCollection
	@Column(length=255)
	@MapKeyColumn(name="locale", length=32)
	private Map<Locale, String> edition = new HashMap<>();
	
	@ElementCollection
	@Column(length=8192)
	@MapKeyColumn(name="locale", length=32) 
	private Map<Locale, String>  dimension = new HashMap<>();
	
	@ElementCollection
	@Column(length=255)
	@MapKeyColumn(name="locale", length=32) 
	private Map<Locale, String> color = new HashMap<>();
	
	@ElementCollection
	@Column(length=255)
	@MapKeyColumn(name="locale", length=32) 
	private Map<Locale, String> finish = new HashMap<>();
	
	@Column(length=64)
	private String source;
	
	@ElementCollection
	@Column(length=255)
	@MapKeyColumn(name="locale", length=32) 
	private Map<Locale, String> technicalData  = new HashMap<>();
	
	@ElementCollection
	@Column(length=255)
	@MapKeyColumn(name="locale", length=32) 
	private Map<Locale, String> specs = new HashMap<>();
	
	@ElementCollection
	@Column(length=8192)
	@MapKeyColumn(name="locale", length=32) 
	private Map<Locale, String> notes = new HashMap<>();
	
	@ManyToMany
	@JoinTable(
		name="Product_tipology",
		uniqueConstraints = @UniqueConstraint(columnNames={"tipology_id", "Product_id"})
	)
	protected List<YadaPersistentEnum<?>> tipology = new ArrayList<>();
	
	@ManyToMany
	@JoinTable(
		name="Product_contract",
		uniqueConstraints = @UniqueConstraint(columnNames={"contract_id", "Product_id"})
	)
	protected List<YadaPersistentEnum<?>> contract = new ArrayList<>();
	
	// A collection with cascade="all-delete-orphan" was no longer referenced by the owning entity instance
	@OneToMany // (cascade=CascadeType.REMOVE, orphanRemoval=true)
	@JoinTable(name="Product_files2d")
	@OrderBy("sortOrder")
	private List<YadaAttachedFile> files2d = new ArrayList<>();
	
	@OneToMany 
	@JoinTable(name="Product_files3d")
	@OrderBy("sortOrder")
	private List<YadaAttachedFile> files3d = new ArrayList<>();
	
	@OneToMany 
	@JoinTable(name="Product_files")
	@OrderBy("sortOrder")
	private List<YadaAttachedFile> files = new ArrayList<>();
	
	@OneToOne(cascade = CascadeType.REMOVE, orphanRemoval = true)
	private YadaAttachedFile wireframe;

	@OneToOne(cascade = CascadeType.REMOVE, orphanRemoval = true)
	private YadaAttachedFile silhouette;
	
	//////////////////////////////////////////////////////////////////////////////////
	
	/**
	 * Returns the localized color in the current request locale
	 * @return
	 */
	public String getLocalColor() {
		return color.get(LocaleContextHolder.getLocale());
	}
	
	/**
	 * Returns the localized finish in the current request locale
	 * @return
	 */
	public String getLocalFinish() {
		return finish.get(LocaleContextHolder.getLocale());
	}
	
	/**
	 * Returns the localized technical data in the current request locale
	 * @return
	 */
	public String getLocalTechnicalData() {
		return technicalData.get(LocaleContextHolder.getLocale());
	}
	
	
	/**
	 * Returns the localized specs in the current request locale
	 * @return
	 */
	public String getLocalSpecs() {
		return specs.get(LocaleContextHolder.getLocale());
	}
	
	/**
	 * Returns the localized notes in the current request locale
	 * @return
	 */
	public String getLocalNotes() {
		return notes.get(LocaleContextHolder.getLocale());
	}
	
	/**
	 * Returns the localized edition in the current request locale
	 * @return
	 */
	public String getLocalEdition() {
		return edition.get(LocaleContextHolder.getLocale());
	}
	
	/**
	 * Returns the localized dimension in the current request locale
	 * @return
	 */
	public String getLocalDimension() {
		return dimension.get(LocaleContextHolder.getLocale());
	}
	//////////////////////////////////////////////////////////////////////////////////
	
	public Map<Locale, String> getEdition() {
		return edition;
	}
	
	public void setEdition(Map<Locale, String> edition) {
		this.edition = edition;
	}
	
	public Map<Locale, String> getColor() {
		return color;
	}
	
	public void setColor(Map<Locale, String> color) {
		this.color = color;
	}
	
	public Map<Locale, String> getFinish() {
		return finish;
	}
	public void setFinish(Map<Locale, String> finish) {
		this.finish = finish;
	}
	
	public Map<Locale, String> getTechnicalData() {
		return technicalData;
	}
	
	public void setTechnicalData(Map<Locale, String> technicalData) {
		this.technicalData = technicalData;
	}
	
	public String getSource() {
		return source;
	}
	
	public void setSource(String source) {
		this.source = source;
	}
	public List<Designer> getDesigners() {
		return designers;
	}
	
	public void setDesigners(List<Designer> designers) {
		this.designers = designers;
	}
	
	public SortedSet<Tag> getTags() {
		return tags;
	}
	public void setTags(SortedSet<Tag> tags) {
		this.tags = tags;
	}

	@Override
	public int hashCode() {
		if (id!=null) {
			return id.hashCode();
		}
		return super.hashCode();
	}
	
	@Override
	public boolean equals(Object obj) {
		if (obj == null) return false;
		if (getClass() != obj.getClass()) return false;
		final Product other = (Product) obj;
		return Objects.equals(this.id, other.id);
	}

	public List<YadaAttachedFile> getFiles2d() {
		return files2d;
	}

	public void setFiles2d(List<YadaAttachedFile> files2d) {
		this.files2d = files2d;
	}

	public List<YadaAttachedFile> getFiles3d() {
		return files3d;
	}

	public void setFiles3d(List<YadaAttachedFile> files3d) {
		this.files3d = files3d;
	}

	public List<YadaAttachedFile> getFiles() {
		return files;
	}

	public void setFiles(List<YadaAttachedFile> files) {
		this.files = files;
	}

	public YadaAttachedFile getWireframe() {
		return wireframe;
	}

	public void setWireframe(YadaAttachedFile wireframe) {
		this.wireframe = wireframe;
	}

	public YadaAttachedFile getSilhouette() {
		return silhouette;
	}

	public void setSilhouette(YadaAttachedFile silhouette) {
		this.silhouette = silhouette;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public List<YadaPersistentEnum<?>> getTipology() {
		return tipology;
	}

	public void setTipology(List<YadaPersistentEnum<?>> tipology) {
		this.tipology = tipology;
	}

	public List<YadaPersistentEnum<?>> getContract() {
		return contract;
	}

	public void setContract(List<YadaPersistentEnum<?>> contract) {
		this.contract = contract;
	}

	public Map<Locale, String> getSpecs() {
		return specs;
	}

	public void setSpecs(Map<Locale, String> specs) {
		this.specs = specs;
	}

	public Map<Locale, String> getNotes() {
		return notes;
	}

	public void setNotes(Map<Locale, String> notes) {
		this.notes = notes;
	}

	public Map<Locale, String> getDimension() {
		return dimension;
	}

	public void setDimension(Map<Locale, String> dimension) {
		this.dimension = dimension;
	}

}
