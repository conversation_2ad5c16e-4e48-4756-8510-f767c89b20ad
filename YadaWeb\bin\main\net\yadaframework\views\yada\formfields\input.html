<!DOCTYPE html>
<html xmlns:yada="http://www.yadaframework.net" xmlns:th="http://www.thymeleaf.org">
<head>
<meta charset="UTF-8">
</head>

<!--/*
Template for <yada:input> resulting HTML.
It is used internally by the yada dialect processor to render the <yada:input> tag but could be included directly in a HTML template if needed.

*/-->

<body>
	<!--/* Can use any th attributes on the tag here because the original ones have been replaced with plain HTML tags already, so no conflicts can arise */-->
	<th:block th:fragment="field" th:with="_numeric_=${yadaInput.type=='number'},
		_file_=${yadaInput.type=='file'},
		_password_=${yadaInput.type=='password'},
		_validationFailed_=${yadaInvalidFlag==true}">
	
		<!--/* When suggestions are required, we must wrap the code inside a dropdown */-->
		<th:block th:if="${yadaSuggestionEnabled==true}">
			<div class="dropdown">
				<div data-bs-toggle="dropdown" data-bs-auto-close="true" aria-expanded="false" data-bs-reference="parent"></div>
				<div th:replace=" :: .yadaInput"></div>
				<div th:replace="/yada/formfields/inputSuggestionFragment :: fragment"></div>
				<script th:inline="javascript">
					$(document).ready(function() {
						const yadaTagId = /*[[${yadaTagId}]]*/ 0;
						$("input[data-yadaTagId="+yadaTagId+"]").on("keyup", yada.suggestionList);
					});
				</script>
			</div>
		</th:block>
		
		<!--/* When suggestions are not required, the dropdown is not needed */-->
		<th:block th:unless="${yadaSuggestionEnabled==true}">
			<div th:replace=" :: .yadaInput"></div>
		</th:block>
	
		<!--/* This fragment is removed from output because included above */-->
		<th:block th:remove="all">
			<div class="input-group yadaInput" th:classappend="${yadaInput.class}">
				<a th:if="${_numeric_}" class="input-group-text yadaInputNumericDecrement" href="javascript:;" tabindex=-1>-</a>
				
				<div th:if="${yadaAddonLeft!=null}" class="input-group-text" th:utext="${yadaAddonLeft}">@</div>
				
				<input th:attr="data-yadaTagId=${yadaTagId},__${yadaTargetAttributesString}__" th:classappend="'form-control' + ${_numeric_?' yadaInputNumber':''} + ${_validationFailed_?' is-invalid':''}">
				
				<div th:if="${yadaAddonRight!=null}" class="input-group-text" th:utext="${yadaAddonRight}">@example.com</div>
				<div th:if="${_file_}" class="jsYadaFileDelete yadaHidden input-group-text"><i class="yadaIcon yadaIcon-delete"></i></div>
				
				<span th:if="${_password_}" class="input-group-btn yadaShowPasswordAddon">
					<a class="btn  btn-secondary yadaShowPassword" href="#" title="Show password"><span class="glyphicon glyphicon-eye-open"></span></a>
				</span>
				
				<a th:if="${_numeric_}" class="input-group-text yadaInputNumericIncrement" href="javascript:;" tabindex=-1>+</a>
				
				<a th:if="${yadaHelpText!=null}" class="yadaHelpButton input-group-text" data-trigger="focus" data-container="body" 
					data-toggle="popover" data-placement="auto bottom"
					data-html="true"
					th:attr="data-content=${help}">
					<i class="yadaIcon yadaIcon-help"></i>
				</a>
				
				<div th:if="${_validationFailed_}" th:text="${yadaValidationMessage}?:#{__${yadaMessageKey}__}" class="invalid-feedback"> <!-- Bootstrap 5 -->
      				Please enter a valid value
    			</div>
    			
    			<script th:if="${_file_}" th:inline="javascript">
					$(document).ready(function() {
						const yadaTagId = /*[[${yadaTagId}]]*/ 0;
						const $inputTag = $("input[data-yadaTagId="+yadaTagId+"]");
						$inputTag.on("change", function(e){
							const input = e.target;
							if (input.files.length>0) {
								const $deleteIcon = $inputTag.siblings(".jsYadaFileDelete");
								$deleteIcon.removeClass("yadaHidden");
								$deleteIcon.on("click", function(){
									$inputTag.val("");
									$deleteIcon.addClass("yadaHidden");
								});
							}
						});
					});
				</script>
				<script th:if="${yadainputcounterid!=null}" th:inline="javascript">
					$(document).ready(function() {
						const inputCounterId = /*[[${yadainputcounterid}]]*/ 0;
						const yadaTagId = /*[[${yadaTagId}]]*/ 0;
						yada.updateInputCounter($("input[data-yadaTagId="+yadaTagId+"]"), $("#"+inputCounterId));
					});
				</script>
				<script th:if="${_numeric_}" th:inline="javascript">
					$(document).ready(function() {
						// initialise numeric input fields with the min attribute
						const yadaTagId = /*[[${yadaTagId}]]*/ 0;
						$("input[data-yadaTagId="+yadaTagId+"]").each(function(){
							const min = $(this).attr("min");
							if (min!=null && $(this).val()<min) {
								$(this).val(min);
							}
						});
						// Only allow digits in numeric input fields
						$("input[data-yadaTagId="+yadaTagId+"]").yadaInputFilter(function(value) {
				    		return /^-?\d*$/.test(value);    // Allow digits only and an initial -, using a RegExp
				  		});
						
					});
				</script>
			</div>
		</th:block>
	</th:block>
</body>
</html>
		