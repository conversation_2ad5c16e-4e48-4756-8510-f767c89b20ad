package com.danesemilano.persistence.entity;

import java.util.Locale;

import org.springframework.context.MessageSource;

import net.yadaframework.core.YadaLocalEnum;
import net.yadaframework.persistence.entity.YadaPersistentEnum;

public enum EnumCategory implements YadaLocalEnum<EnumCategory> {
	// ATTENZIONE: aggiungere eventuali nuovi valori alla fine!
	TABLE_ACCESSORIES,
	DESK_ACCESSORIES,
	CALENDARS,
	ASHTRAYS,
	FRAMES,
	WASTEPAPER_BASKET,
	COAT_AND_UMBRELLA_STANDS,
	HOME_GARDEN,
	STORAGE,
	BOOKCASES,
	FURNITURE,
	LIGHT,
	TOYS, //questa categoria è stata cambiata il nome in Puzzle
	ART_EDITIONS,
	BODY_ACCESSORIES,
	WATCHES,
	NOVELTIES,
	SCULPTURES
	// Aggiungere qui nuovi valori
	;
	
	
	private static String MESSAGES_PREFIX = "enum.category.";
	
	private YadaPersistentEnum<EnumCategory> yadaPersistentEnum;
	
	public YadaPersistentEnum<EnumCategory> toYadaPersistentEnum() {
		return yadaPersistentEnum;
	}
	
	// TODO fix generics
	public void setYadaPersistentEnum(YadaPersistentEnum yadaPersistentEnum) {
		this.yadaPersistentEnum = yadaPersistentEnum;
	}
	
	/**
	 * Return the localized text for this enum
	 * @param messageSource
	 * @param locale
	 * @return
	 */
	public String toString(MessageSource messageSource, Locale locale) {
		return messageSource.getMessage(MESSAGES_PREFIX + name().toLowerCase(), null, locale);
	}

}
