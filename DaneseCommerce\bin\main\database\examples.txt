# Renaming a column:

alter table Certificate CHANGE oldname newname varchar(12) not null;

--> Constraints rename themselves automatically

ALTER TABLE table_name RENAME COLUMN oldname TO newname;

# Adding one column to a table:

ALTER TABLE Annuncio ADD COLUMN `likes` bigint after `latitudine`;

# Adding many columns:

ALTER TABLE Annuncio
ADD COLUMN prezzoVisitaWeek integer after prezzoVisitaNight,
ADD COLUMN prezzoVisitaWeekend integer after prezzoVisitaWeek,
ADD COLUMN prezzoVisitaYear integer after prezzoVisitaWeekend;

# Change the definition of a column

ALTER TABLE Campaign MODIFY annuncio_id bigint not null;

# Removing columns

ALTER TABLE `Annuncio` 
DROP COLUMN `periodoPrezzo`,
DROP COLUMN `prezzo`;

# Changing the value of a cell

UPDATE Annuncio SET videoReady=true WHERE videoReady is null;

# Delete a table

drop table Annuncio_Tag;

# Delete a constraint (use 'index')

alter table Certificate drop index UK_jwn1h8fk2i36w92br0ctbvou0;

# Delete a foreign key

alter table Certificate drop foreign key `FK_jwn1h8fk2i36w92br0ctbvou0`;

