[{"resource_type": "products", "groups": [{"description": "Add up to two unique product tabs, that are unique to a given product. Make sure to include both the title and the content for each tab.", "name": "Product tabs", "namespace": "sf_product_tabs", "sub_groups": [{"name": "Tab 1", "fields": [{"type": "text", "key": "tab_1_title", "name": "Title", "hint": "Keep the title short"}, {"type": "rte", "key": "tab_1_content", "name": "Content"}]}, {"name": "Tab 2", "fields": [{"type": "text", "key": "tab_2_title", "name": "Title", "hint": "Keep the title short"}, {"type": "rte", "key": "tab_2_content", "name": "Content"}]}]}, {"description": "Choose up to 4 hand-picked related products.", "name": "Hand-picked related products", "namespace": "sf_related_products", "fields": [{"type": "switch", "key": "enabled", "name": "Enable", "hint": "If unchecked, the theme will use the default theme behavior (picking random products)"}, {"type": "product", "key": "product_1", "name": "Product 1"}, {"type": "product", "key": "product_2", "name": "Product 2"}, {"type": "product", "key": "product_3", "name": "Product 3"}, {"type": "product", "key": "product_4", "name": "Product 4"}]}]}]