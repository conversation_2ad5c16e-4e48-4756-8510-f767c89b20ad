#!/bin/bash
# Usare il parametro "force" per bypassare il prompt
# NOTA: questo script VIENE COPIATO ogni volta dal build.xml del workspace, quindi non fare modifiche su ubuntu
# Note for yada developers: the $ in the yada template is the escaped form of the dollar sign

hostname=localhost
force=

if [ "$1" == "force" ]
then
	force=-f
fi

echo 
echo Rigenerazione del DB dncdbcol

mysqladmin $force --user=root --password=n38n drop dncdbcol

# devo usare mysql e non mysqladmin perche' il secondo non mi setta il charset

mysql --verbose -u root --password=n38n --host=$hostname <<SQLCOMMAND 
create database dncdbcol character set utf8mb4;
CREATE USER 'dncusercol'@'localhost' IDENTIFIED BY 'n38n';
GRANT ALL ON dncdbcol.* TO 'dncusercol'@'localhost';
FLUSH PRIVILEGES;
SQLCOMMAND

mysql --verbose -u root --password=n38n --host=$hostname dncdbcol < dnc.sql
mysql --verbose -u root --password=n38n --host=$hostname dncdbcol < dncextra.sql

echo Done.
