

(function( dnc ) {
	"use strict";
	
	// Namespace trick explained here: http://stackoverflow.com/a/5947280/587641
	// For a public property or function, use "dnc.xxx = ..."
	// For a private property use "var xxx = "
	// For a private function use "function xxx(..."

	////////////////////////
	// Sito
	
	dnc.loadingMore=false;
	
	dnc.autoLoadMore = function() {
  	  	var el=$(".loadMore:visible");
  	  	if (dnc.loadingMore==true || el.length==0) {
  	  		return;
  	  	}
          var rect = el[0].getBoundingClientRect(); 
          var elemTop =rect.top; 
          var elemBottom =  rect.bottom; 

          // Only completely visible elements return true:
          // var isVisible = (elemTop >= 0) && (elemBottom <= window.innerHeight);
          // Partially visible elements return true:
          var isVisible = elemTop < window.innerHeight && elemBottom >= 0;
          
          if (isVisible) {
        	  dnc.loadingMore = true;
//              $('a', el).delay(900000).trigger('click'); //not working
              setTimeout(function() {
            	  $('a', el).trigger('click');}, 777);

          }               
      }
	
    $('.language-item').on('click', function (e) {
    	// Non serve cambiare la classe perché la pagina si ricarica
        // $('.language-item').removeClass('selected-language');
        // $(this).addClass('selected-language');
    	yada.loaderOn();
    	var newLanguage = $(this).attr("data-lang"); // it, en
    	yada.changeLanguagePathVariable(newLanguage);
    });
    
    function handleSearch() {
    	var $input = $('#searchField');
    	var $button = $("#searchButton");
    	var text = $input.val();
    	if (text.length>=3) { // Servono almeno 3 caratteri
    		var url = $button.attr("href") + "/" + encodeURIComponent(text);
    		window.location.href=url;
    	}
    }
    
    $('#searchButton').click(function(e) {
    	e.preventDefault();
    	handleSearch();
    	return false;
    });
    
    $('#searchField').keypress(function(e){
		if ( event.which == 13 ) {
			e.preventDefault();
			handleSearch();
			return false;
		}
    });
    
    
    function handleSearchMob() {
    	var $input = $('#searchFieldMob');
    	var $button = $("#searchButtonMob");
    	var text = $input.val();
    	if (text.length>=3) { // Servono almeno 3 caratteri
    		var url = $button.attr("href") + "/" + encodeURIComponent(text);
    		window.location.href=url;
    	}
    }
    
    $('#searchButtonMob').click(function(e) {
    	e.preventDefault();
    	handleSearchMob();
    	return false;
    });
    
    $('#searchFieldMob').keypress(function(e){
		if ( event.which == 13 ) {
			e.preventDefault();
			handleSearchMob();
			return false;
		}
    });
    
    $(document).ready(function () {
		$('.loader').click(function() {
			yada.loaderOff();
		});
    });
    
	// Add the registered symbol to Bruno Munari
	dnc.replaceMunari = function($element) {
		$element.each(function(){
			const origText = this.innerHTML;
			if (origText!=null) {
				const replacedText = origText.replace(/Munari/g, "Munari<sup>®</sup>");
				if (!origText!==replacedText) {
					this.innerHTML = replacedText;
				}
			}
		});
	}
	
	dnc.replaceMunari($(".product-tag-block"));
    dnc.replaceMunari($("a.product-details-short-text"));
    dnc.replaceMunari($(".designer-name"));
    dnc.replaceMunari($(".designerName"));
    dnc.replaceMunari($(".main-designer-name"));
    dnc.replaceMunari($(".manifesto-description"));
	
	
	////////////////////////
	// CMS
	
	var cookieLanguageChooser = "languageChooser_";
	
	/**
	 * Metodo da chiamare quando si usa un form con input localizzati e si vuole mostrare solo quelli
	 * delle lingue selezionate coi checkbox
	 */
	dnc.initializeLocalizedForm = function() {
		$('#formLanguageChooser input').change(function(){
			var $checkbox = $(this);
			var $label = $(this).parent();
			var checked = $checkbox.prop('checked');
			var language = $label.text().trim().toLowerCase(); // it
			$('form .localized.'+language).toggle(checked);
			// Setto il cookie
			yada.setCookie(cookieLanguageChooser+language, checked, 365);
			// Se non ci sono più checkbox settati, setto l'altro
			if ($('#formLanguageChooser input:checked').length==0) {
				$('#formLanguageChooser .checkbox:not(.' + language + ') input').click();
			}
		});
		// Inizializzazione coi cookies
		$('#formLanguageChooser input').each(function() {
			var $checkbox = $(this);
			var language = $checkbox.parent().text().trim().toLowerCase(); // it
			var checked = yada.getCookie(cookieLanguageChooser+language)=="true";
			$checkbox.prop('checked', checked);
			if (checked) {
				$('form .localized.'+language).show();
			}
		});
		// Se non ci sono checkbox settati, setto en
		if ($('#formLanguageChooser input:checked').length==0) {
			$('#formLanguageChooser .en input').prop("checked", true);
			$('form .localized.en').show();
		}
	}	
	
	// Analizza le immagini dello slider e mette il width a quelle con proporzione 4/3 (1.3333) e 1/1
	// Non più usato perché non sempre funzionava. E poi è inutile.
	dnc.slideImageSizeCheck = function() {
		$(".carousel img").each(function(){
			var $image = $(this);
			var newImage = new Image();
			newImage.src = $image[0].src;
			var proportions = newImage.width / newImage.height;
			if (proportions>0.89 && proportions<1.1) {
				$image.css("width","75%"); // Squared 1/1
			} else if (proportions>1.3 && proportions<1.4) {
				$image.css("width","100%"); // Landscape 4/3
			}
		});
	}
	
	
	var cookieNameBannerNewsletter="bannerNewsletter";
	
	/**
	 * Returns true if the US-CA cookie has beenn set
	 */
	dnc.isCookieBannerNewsletterSet = function() {
		return document.cookie.indexOf(cookieNameBannerNewsletter+"=true")>=0;
	}
	
	dnc.setCookieBannerNewsletter = function() {
		var date = new Date(); 
		date.setDate(date.getDate() + 7); // 7 giorni di expiration
		document.cookie = cookieNameBannerNewsletter + "=true; expires="+date.toGMTString()+"; path=/";
	}

}( window.dnc = window.dnc || {} ));
	