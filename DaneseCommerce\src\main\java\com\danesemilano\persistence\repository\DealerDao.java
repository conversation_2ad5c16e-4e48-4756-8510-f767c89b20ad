package com.danesemilano.persistence.repository;

import java.util.List;
import java.util.Locale;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.danesemilano.persistence.entity.Dealer;

import net.yadaframework.components.YadaUtil;
import net.yadaframework.persistence.YadaSql;

/**
 * Locale-related operations on entities 
 */
@Repository
@Transactional(readOnly = true) 
public class DealerDao {
	
    @PersistenceContext
	private EntityManager em;
    

    /**
     * Trova tutti i dealer di una regione, mettendo prima gli agenti, poi ordinandoli alfabeticamente per provincia e nome
     * @param regionId
     * @return
     */
	public List<Dealer> findByRegion(Long regionId, Locale locale) {
		// select d.agente, ptext, d.name from Dealer d join d.region r left join d.province p left join p.langToText ptext 
		// where r.id=35 and (KEY(ptext) is null or KEY(ptext)='it') order by d.agente desc, ptext, d.name
		return em.createQuery("select d from Dealer d join d.region r left join d.province p left join p.langToText ptext "
	 		+ "where r.id=:regionId and (KEY(ptext) is null or KEY(ptext)=:locale) order by d.agente desc, ptext, d.name", Dealer.class)
		 	.setParameter("regionId", regionId)
		 	.setParameter("locale", locale.getLanguage())
		 	.getResultList();
	}
	
    /**
     * Trova tutti i dealer di una nazione, mettendo prima gli agenti e ordinando alfabeticamente per nome
     * @param nationId
     * @return
     */
    public List<Dealer> findByNation(Long nationId) {
    	return em.createQuery("select d from Dealer d join d.nation n where n.id=:nationId order by d.agente desc, d.name asc", Dealer.class)
    	.setParameter("nationId", nationId)
    	.getResultList();
    }
    
    /**
     * Cancella un Dealer
     * @param Dealer
     */
    @Transactional(readOnly = false) 
    public String delete(Long id, Locale locale) {
    	String name = (String) YadaSql.instance().selectFrom("select name from Dealer")
        		.where("where id=:dealerId")
    	    	.setParameter("dealerId", id)
    	    	.nativeQuery(em)
    	    	.getSingleResult();
    	
        	performQuery(em.createNativeQuery("delete from Dealer where id = :dealerId"), id);
        	return name;
    }
    
    private void performQuery(Query query, Long id) {
    	query.setParameter("dealerId", id);
    	query.executeUpdate();
    }
    
    /**
     * Duplicate a Dealer
     * @param Dealer
     * @return
     */
    @Transactional(readOnly = false) 
    public Object duplicate(Dealer Dealer) {
    	return YadaUtil.copyEntity(Dealer);
    }

}
