@font-face {
  font-family: 'danese-icons';
  src:
    url('danese-icons.ttf?3seta1') format('truetype'),
    url('danese-icons.woff?3seta1') format('woff'),
    url('danese-icons.svg?3seta1#danese-icons') format('svg');
  font-weight: normal;
  font-style: normal;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'danese-icons' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-buy:before {
  content: "\e900";
}
.icon-cart:before {
  content: "\e907";
}
.icon-contact:before {
  content: "\e908";
}
.icon-lens:before {
  content: "\e909";
}
.icon-moon:before {
  content: "\e910";
}
.icon-minus:before {
  content: "\e90c";
}
.icon-waves:before {
  content: "\e911";
}
.icon-plus:before {
  content: "\e913";
}
.icon-star:before {
  content: "\e914";
}
.icon-forward:before {
  content: "\e906";
}
.icon-caret-right:before {
  content: "\e917";
}
.icon-trifolio:before {
  content: "\e901";
}
.icon-facebook:before {
  content: "\e90a";
}
.icon-linkedin:before {
  content: "\e90b";
}
.icon-arrow-left:before {
  content: "\e902";
}
.icon-arrow-right:before {
  content: "\e903";
}
.icon-x:before {
  content: "\e904";
}
.icon-submit:before {
  content: "\e905";
}
.icon-uparrow:before {
  content: "\e90d";
}
