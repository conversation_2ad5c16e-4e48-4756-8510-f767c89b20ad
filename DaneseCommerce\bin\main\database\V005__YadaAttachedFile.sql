#Modifiche necessarie per passare dal branch yada 0.3.0 al 0.4.1

alter table YadaAttachedFile ADD COLUMN height integer not null;
alter table YadaAttachedFile ADD COLUMN width integer not null;
alter table YadaAttachedFile ADD COLUMN heightDesktop integer not null;
alter table YadaAttachedFile ADD COLUMN widthDesktop integer not null;
alter table YadaAttachedFile ADD COLUMN heightMobile integer not null;
alter table YadaAttachedFile ADD COLUMN widthMobile integer not null;
ALTER TABLE YadaAttachedFile ADD filenamePdf varchar(255) after filenameMobile;
ALTER TABLE YadaAttachedFile ADD heightPdf integer;
ALTER TABLE YadaAttachedFile ADD widthPdf integer;
alter table YadaAttachedFile DROP COLUMN attachedToId;

alter table YadaAttachedFile_description MODIFY COLUMN description VARCHAR(8192);
alter table YadaAttachedFile_title MODIFY COLUMN title varchar(1024);