package com.danesemilano.persistence.repository;

import java.util.List;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.danesemilano.persistence.entity.EnumCategory;
import com.danesemilano.persistence.entity.Product;

import net.yadaframework.persistence.YadaSql;
import net.yadaframework.persistence.entity.YadaAttachedFile;
import net.yadaframework.persistence.entity.YadaPersistentEnum;
import net.yadaframework.web.YadaPageRequest;
import net.yadaframework.web.YadaPageRows;

@Repository
@Transactional(readOnly = true) 
public class ProductRepoDao {
	
	@PersistenceContext
	EntityManager em;
	
	/**
	 * Trova tutti i prodotti pubblicati e li ordina alfabeticamente
	 * @return
	 */
	public List<Product> findAllByPublishedTrueOrderByProductNameAsc() {
		String sql = "from Product p where p.published=true order by productName asc";
		return em.createQuery(sql, Product.class)
			.getResultList();
	}
	
	/**
	 * Trova le immagini delle gallery per i prodotti che appartengono alla category indicata
	 * @return
	 */
	public List<YadaAttachedFile> findImagesByCategory(Long categoryId, YadaPageRequest pageable) {
		String sql = "SELECT g FROM YadaAttachedFile g WHERE g.id IN (" +
	             "SELECT MIN(g2.id) FROM Product p " +
	             "JOIN p.galleryImages g2 " +
	             "JOIN p.categories c " +
	             "WHERE p.published = true AND c.id = :categoryId " +
	             "GROUP BY p" +
	             ")";
		YadaSql yadaSql = YadaSql.instance().selectFrom(sql);
		boolean isPage = pageable!=null && pageable.isValid();
		if (isPage) {
			yadaSql.orderBy(pageable);
		}
		TypedQuery<YadaAttachedFile> query = yadaSql.query(em, YadaAttachedFile.class).setParameter("categoryId", categoryId);
		if (isPage) {
			query.setFirstResult(pageable.getFirstResult()).setMaxResults(pageable.getSize());
		}
		return query.getResultList();
	}
	
	/**
	 * Trova le immagini delle gallery per i prodotti che appartengono al contract indicato
	 * @param pageable 
	 * @return
	 */
	public List<YadaAttachedFile> findImagesByContract(Long contractId, YadaPageRequest pageable) {
		String sql = 
			    "SELECT g " +
			    "FROM YadaAttachedFile g " +
			    "WHERE g.id IN (" +
			        "SELECT MIN(gi.id) " +
			        "FROM Product p " +
			        "JOIN p.galleryImages gi " +
			        "JOIN p.contract c " +
			        "WHERE p.published = true AND c.id = :contractId " +
			        "GROUP BY p" +
			    ")";
		YadaSql yadaSql = YadaSql.instance().selectFrom(sql);
		boolean isPage = pageable!=null && pageable.isValid();
		if (isPage) {
			yadaSql.orderBy(pageable);
		}
		TypedQuery<YadaAttachedFile> query = yadaSql.query(em, YadaAttachedFile.class).setParameter("contractId", contractId);
		if (isPage) {
			query.setFirstResult(pageable.getFirstResult()).setMaxResults(pageable.getSize());
		}
		return query.getResultList();
	}
	
	/**
	 * Trova tutti i prodotti di un certo contract, che siano pubblicati.
	 * @param contractId
	 * @param productPage 
	 * @return
	 */
	public YadaPageRows<Product> findByContract(Long contractId, YadaPageRequest pageable) {
		String sql = "select p from Product p join p.contract c join fetch p.subcategories where p.published=true and c.id = :contractId";
		YadaSql yadaSql = YadaSql.instance().selectFrom(sql);
		boolean isPage = pageable!=null && pageable.isValid();
		if (isPage) {
			yadaSql.orderBy(pageable);
		}
		TypedQuery<Product> query = yadaSql.query(em, Product.class).setParameter("contractId", contractId);
		if (isPage) {
			query.setFirstResult(pageable.getFirstResult()).setMaxResults(pageable.getSize());
		}
		return new YadaPageRows<Product>(query.getResultList(), pageable);
	}
	
	/**
	 * Trova tutti i prodotti pubblicati che hanno il tag indicato
	 * @param tagId
	 * @return
	 */
	public List<Product> findByTagPublished(Long tagId) {
		String sql = "select p from Product p join p.tags t join fetch p.subcategories where p.published=true and t.id = :tagId";
		return em.createQuery(sql, Product.class)
			.setParameter("tagId", tagId)
			.getResultList();
	}

	/**
	 * Per ogni categoria di prodotto prende la prima immagine
	 * @return
	 */
	public List<YadaAttachedFile> findFirstImageEachCategory() {
		String sql = 
			    "SELECT g " +
			    "FROM YadaAttachedFile g " +
			    "WHERE g.id IN (" +
			        "SELECT MIN(gi.id) " +
			        "FROM Product p " +
			        "JOIN p.galleryImages gi " +
			        "JOIN p.categories c " +
			        "JOIN c.langToText lt " +
			        "WHERE p.published = true " +
			        "GROUP BY lt" +
			    ")";
		return em.createQuery(sql, YadaAttachedFile.class)
			.getResultList();
	}
	
	/**
	 * Trova tutti i prodotti appartenenti a una certa categoria, che siano published. Fa un fetch delle subcategories
	 * @param yadaPersistentEnum
	 * @return
	 */
	public List<Product> findAllForCategory(YadaPersistentEnum<EnumCategory> category) {
		String sql = "select p from Product p join p.categories c join fetch p.subcategories where p.published = true and c = :category";
		return em.createQuery(sql, Product.class)
			.setParameter("category", category)
			.getResultList();
	}

	/**
	 * Search di un prodotto
	 * @param searchString
	 * @param pageable
	 * @param locale
	 * @return
	 */
	public YadaPageRows<Product> search(String searchString, YadaPageRequest pageable, String locale) {
		String sql = "select distinct p from Product p join p.designers d join p.categories cat join cat.langToText catlt join fetch p.subcategories sc join sc.langToText sclt where p.published=true and " 
			+ "( lower(p.productName)  like :s or lower(d.name) like :s or lower(d.surname) like :s or (KEY(catlt)=:locale and lower(catlt) like :s) or (KEY(sclt)=:locale and lower(sclt) like :s))";
		YadaSql yadaSql = YadaSql.instance().selectFrom(sql);
		boolean isPage = pageable!=null && pageable.isValid();
		if (isPage) {
			yadaSql.orderBy(pageable);
		}
		TypedQuery<Product> query = yadaSql.query(em, Product.class)
			.setParameter("s", searchString)
			.setParameter("locale", locale);
		if (isPage) {
			query.setFirstResult(pageable.getFirstResult()).setMaxResults(pageable.getSize());
		}
		return new YadaPageRows<Product>(query.getResultList(), pageable);
	}
	
	/**
	 * Remove the association of a file 2D from a product
	 * @param productId
	 * @param yadaAttachedFileId
	 */
	@Transactional(readOnly = false) 
	public void removeFile2d(Long productId, Long yadaAttachedFileId) {
		String sql = "delete from Product_files2d where Product_id = :productId and files2d_id = :yadaAttachedFileId";
		em.createNativeQuery(sql)		
			.setParameter("productId", productId)
			.setParameter("yadaAttachedFileId", yadaAttachedFileId)
			.executeUpdate();
	}
	
	/**
	 * Remove the association of a file 3D from a product
	 * @param productId
	 * @param yadaAttachedFileId
	 */
	@Transactional(readOnly = false) 
	public void removeFile3d(Long productId, Long yadaAttachedFileId) {
		String sql = "delete from Product_files3d where Product_id = :productId and files3d_id = :yadaAttachedFileId";
		em.createNativeQuery(sql)		
			.setParameter("productId", productId)
			.setParameter("yadaAttachedFileId", yadaAttachedFileId)
			.executeUpdate();
	}
	
	/**
	 * Remove the association of a any file from a product
	 * @param productId
	 * @param yadaAttachedFileId
	 */
	@Transactional(readOnly = false) 
	public void removeFile(Long productId, Long yadaAttachedFileId) {
		String sql = "delete from Product_files where Product_id = :productId and files_id = :yadaAttachedFileId";
		em.createNativeQuery(sql)		
			.setParameter("productId", productId)
			.setParameter("yadaAttachedFileId", yadaAttachedFileId)
			.executeUpdate();
	}
	
	/**
	 * Update a new wireframe image attachment to a product
	 * @param productId
	 * @param yadaAttachedFileId
	 */
	@Transactional(readOnly = false) 
	public void setWireframeImage(Long productId, Long yadaAttachedFileId) {
		String sql = "UPDATE YadaProduct SET wireframe_id=:yadaAttachedFileId where id=:yadaProductId";
		em.createNativeQuery(sql)		
			.setParameter("yadaProductId", productId)
			.setParameter("yadaAttachedFileId", yadaAttachedFileId)
			.executeUpdate();
	}
	
	/**
	 * Update a new silhouette image attachment to a product
	 * @param productId
	 * @param yadaAttachedFileId
	 */
	@Transactional(readOnly = false) 
	public void setSilhouetteImage(Long productId, Long yadaAttachedFileId) {
		String sql = "UPDATE YadaProduct SET silhouette_id=:yadaAttachedFileId where id=:yadaProductId";
		em.createNativeQuery(sql)		
			.setParameter("yadaProductId", productId)
			.setParameter("yadaAttachedFileId", yadaAttachedFileId)
			.executeUpdate();
	}
	
	/**
	 * Adds a new file 2D attachment to a product
	 * @param productId
	 * @param yadaAttachedFileId
	 */
	@Transactional(readOnly = false) 
	public void addFile2d(Long productId, Long yadaAttachedFileId) {
		String sql = "insert into Product_files2d (Product_id, files2d_id) values (:productId, :yadaAttachedFileId)";
		em.createNativeQuery(sql)		
			.setParameter("productId", productId)
			.setParameter("yadaAttachedFileId", yadaAttachedFileId)
			.executeUpdate();
	}
	
	/**
	 * Adds a new file 3D attachment to a product
	 * @param productId
	 * @param yadaAttachedFileId
	 */
	@Transactional(readOnly = false) 
	public void addFile3d(Long productId, Long yadaAttachedFileId) {
		String sql = "insert into Product_files3d (Product_id, files3d_id) values (:productId, :yadaAttachedFileId)";
		em.createNativeQuery(sql)		
			.setParameter("productId", productId)
			.setParameter("yadaAttachedFileId", yadaAttachedFileId)
			.executeUpdate();
	}
	
	/**
	 * Adds a new any file attachment to a product
	 * @param productId
	 * @param yadaAttachedFileId
	 */
	@Transactional(readOnly = false) 
	public void addFile(Long productId, Long yadaAttachedFileId) {
		String sql = "insert into Product_files (Product_id, files_id) values (:productId, :yadaAttachedFileId)";
		em.createNativeQuery(sql)		
			.setParameter("productId", productId)
			.setParameter("yadaAttachedFileId", yadaAttachedFileId)
			.executeUpdate();
	}
	
//	/**
//	 * Find a list product by category
//	 * @param category
//	 * @return
//	 */
//	@Query(value="Select * From YadaProduct p join YadaProduct_categories c where p.id = c.YadaProduct_id AND c.categories_id = :category and p.published = true", nativeQuery=true)
//	List<Product> findByCategory(@Param("category") Long category);
	
	/**
	 * Trova tutti i prodotti di una certa category, che siano pubblicati.
	 * @param categoryId
	 * @return
	 */
	public List<Product> findByCategory(Long categoryId) {
		String sql = "select p from Product p join p.categories c join fetch p.subcategories where p.published=true and c.id = :categoryId";
		return em.createQuery(sql, Product.class)
			.setParameter("categoryId", categoryId)
			.getResultList();
	}

	// Kept for compatibility with Spring Data Repository
	public Product findOne(Long productId) {
		return em.find(Product.class, productId);
	}	
	
	@Transactional(readOnly = false)
	public Product save(Product entity) {
		if (entity==null) {
			return null;
		}
		if (entity.getId()==null) {
			em.persist(entity);
			return entity;
		}
		return em.merge(entity);
	}

}
