package com.danesemilano.persistence.repository;

import java.util.List;
import java.util.Locale;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.danesemilano.persistence.entity.Tag;

@Repository
@Transactional(readOnly = true) 
public class TagRepoDao {
	
	@PersistenceContext
	EntityManager em;
	
	/**
	 * Cerca il tag che ha il valore specificato nella lingua indicata
	 * @param name
	 * @return
	 */
	public Tag findByName(String name, Locale locale) {
		String sql = "select t from Tag t join t.name n where KEY(n)=:locale and lower(n)=lower(:name)";
		List<Tag> resultList = em.createQuery(sql, Tag.class)		
			.setMaxResults(1)
			.setParameter("name", name)
			.setParameter("locale", locale)
			.getResultList();
		return normaliseSingleResult(resultList);
	}
	
	/**
	 * Cerca il tag che ha il valore specificato in una qualunque lingua
	 * @param name
	 * @return
	 */
	public Tag findByName(String name) {
		String sql = "select t from Tag t join t.name n where lower(n)=lower(:name)";
		List<Tag> resultList = em.createQuery(sql, Tag.class)		
			.setMaxResults(1)
			.setParameter("name", name)
			.getResultList();
		return normaliseSingleResult(resultList);
	}
	
	/**
	 * Crea la relazione tra tag e prodotto se non esiste già
	 * @param tagId
	 * @param productId
	 */
	@Transactional(readOnly = false) 
	public void ensureTagOnProduct(Long tagId,Long productId) {
		String sql = "insert ignore into Tag_YadaProduct (tags_id, products_id) VALUES (:tagId, :productId)";
		em.createNativeQuery(sql)		
			.setParameter("tagId", tagId)
			.setParameter("productId", productId)
			.executeUpdate();
	}
	
	/**
	 * Crea la relazione tra tag e eventNews se non esiste già
	 * @param tagId
	 * @param eventNewsId
	 */
	@Transactional(readOnly = false) 
	public void ensureTagOnEventNews(Long tagId, Long eventNewsId) {
		String sql = "insert ignore into Tag_EventNews (tags_id, eventNews_id) VALUES (:tagId, :eventNewsId)";
		em.createNativeQuery(sql)		
			.setParameter("tagId", tagId)
			.setParameter("eventNewsId", eventNewsId)
			.executeUpdate();
	}

	public List<Tag> findAllSorted(Locale locale) {
		String sql = "from Tag e join e.name name where KEY(name)=:locale order by name";
		return em.createQuery(sql, Tag.class)
			.setParameter("locale", locale)
			.getResultList();
	}
	
    /**
     * For backwards compatibility, returns null when no result is found
     * @param resultList
     * @return
     */
    private Tag normaliseSingleResult(List<Tag> resultList) {
		// Need to keep the contract of the Spring Data Repository, so we return null when no value found.
		if (resultList.isEmpty()) {
			return null;
		} else {
			return resultList.get(0);
		}
    }

	@Transactional(readOnly = false)
	public Tag save(Tag entity) {
		if (entity==null) {
			return null;
		}
		if (entity.getId()==null) {
			em.persist(entity);
			return entity;
		}
		return em.merge(entity);
	}

	// Kept for compatibility with Spring Data Repository
	public Tag findOne(Long tagId) {
		return em.find(Tag.class, tagId);
	}
	
}
