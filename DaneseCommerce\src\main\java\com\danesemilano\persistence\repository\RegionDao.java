package com.danesemilano.persistence.repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.danesemilano.persistence.entity.EnumRegion;

import net.yadaframework.persistence.YadaSql;
import net.yadaframework.persistence.entity.YadaPersistentEnum;

@Repository
@Transactional(readOnly = true) 
public class RegionDao {
	
    @PersistenceContext
	private EntityManager em;
	
	/**
	 * Ritorna tutte le Region che siano state usate almeno in un Dealer, ordinate alfabeticamente
	 * @return
	 */
	public List<YadaPersistentEnum<EnumRegion>> findAvailableRegions(Locale locale) {
		@SuppressWarnings("unchecked")
		List<Object[]> selected = YadaSql.instance().selectFrom("select distinct r, lt from Dealer d") // lt bisogna metterlo per evitare un errore "ORDER BY clause is not in SELECT list"
			.join("join d.region r")
			.join("join r.langToText lt") // Serve per ordinare
			.where("where KEY(lt)=:locale")
			.orderBy("order by lt")
			.setParameter("locale", locale.getLanguage())
			.query(em).getResultList();
		// selected contiene sia Region che il nome usato per sortare. Ritorno solo Region
		List<YadaPersistentEnum<EnumRegion>> result = new ArrayList<>();
		for (Object[] row : selected) {
			result.add((YadaPersistentEnum<EnumRegion>)row[0]);
		}
		return result;
	}

}
