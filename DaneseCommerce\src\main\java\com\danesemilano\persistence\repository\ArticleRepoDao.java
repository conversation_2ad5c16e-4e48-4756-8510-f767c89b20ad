package com.danesemilano.persistence.repository;

import java.util.List;
import java.util.Locale;

import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.NonUniqueResultException;
import jakarta.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.danesemilano.persistence.entity.Article;

@Repository
@Transactional(readOnly = true) 
public class ArticleRepoDao {
	
	@PersistenceContext
	EntityManager em;
	
	public List<Article> findAllByProduct_id(Long id) {
		String sql = "select a from Article a join a.product p where p.id = :id";
		return em.createQuery(sql, Article.class)
			.setParameter("id", id)
			.getResultList();
	}
	
	public Article findOneByProduct_id(Long id) {
		String sql = "select a from Article a join a.product p where p.id = :id";
		try {
			return em.createQuery(sql, Article.class)
				.setMaxResults(1)
				.setParameter("id", id)
				.getSingleResult();
		} catch (NonUniqueResultException | NoResultException e) {
			return null; // Nothing found
		}
	}
	
	/**
	 * Update a new silhouette image attachment to a product
	 * @param productId
	 * @param yadaAttachedFileId
	 */
	@Transactional(readOnly = false) 
	public void setSilhouetteImage(Long productId, Long yadaAttachedFileId) {
		String sql = "UPDATE YadaArticle SET silhouette_id=:yadaAttachedFileId where id=:yadaProductId";
		em.createNativeQuery(sql)
			.setParameter("yadaProductId", productId)
			.setParameter("yadaAttachedFileId", yadaAttachedFileId)
			.executeUpdate();
	}
	
	/**
	 * Return a shopUrl by productId and locale
	 */
	public String shopUrlByProductIdAndLocale(Long productId, Locale locale) {
		String sql = "select shop.shopUrl from Article_shopUrl shop join YadaArticle a on a.id=shop.Article_id  where a.product_id=:productId and shop.locale=:locale";
		try {
			return (String) em.createNativeQuery(sql)
				.setMaxResults(1)
				.setParameter("productId", productId)
				.setParameter("locale", locale)
				.getSingleResult();
		} catch (NonUniqueResultException | NoResultException e) {
			return null; // Nothing found
		}
	}
}
