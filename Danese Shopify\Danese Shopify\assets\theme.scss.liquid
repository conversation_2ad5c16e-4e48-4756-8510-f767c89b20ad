/**
 * ----------------------------------------------------------------------------------------------
 * This is a variation of Normalize.css (http://necolas.github.io/normalize.css/)
 * ----------------------------------------------------------------------------------------------
 */

/**
 * Base
 */

*,
*:before,
*:after {
  box-sizing: border-box !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html {
  font-family: sans-serif;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-size-adjust: 100%;
  -ms-overflow-style: -ms-autohiding-scrollbar;
}

body {
  margin: 0;
}

/**
 * HTML5 display definitions
 */

article,
aside,
details,
figcaption,
figure,
footer,
header,
main,
nav,
section,
summary {
  display: block;
}

audio,
canvas,
progress,
video {
  display: inline-block;
  vertical-align: baseline;
}

audio:not([controls]) {
  display: none;
  height: 0;
}

/**
 * Text-level semantic
 */

:active {
  outline: none;
}

a {
  color: inherit;
  background-color: transparent;
  text-decoration: none;

  &:active,
  &:hover {
    outline: 0;
  }
}

b,
strong {
  font-weight: bold;
}

small {
  font-size: 80%;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  font-size: inherit;
}

p:last-child,
h1:last-child,
h2:last-child,
h3:last-child,
h4:last-child,
h5:last-child,
h6:last-child {
  margin-bottom: 0;
}

/**
 * Embedded content
 */

img {
  max-width: 100%;
  height: auto;
  border: 0;
}

svg:not(:root) {
  overflow: hidden;
}

/**
 * Grouping content
 */

ul,
ol {
  margin: 0;
  padding: 0;
  list-style-position: inside;
}

pre {
  overflow: auto;
}

code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 16px;
}

/**
 * Forms
 */

button,
input,
optgroup,
select,
textarea {
  color: inherit;
  font: inherit;
  margin: 0;
}

button {
  overflow: visible;
}

button,
select {
  text-transform: none;
}

button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button;
  cursor: pointer;
}

button[disabled],
html input[disabled] {
  cursor: default;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}

input {
  line-height: normal;
}

input[type="checkbox"],
input[type="radio"] {
  box-sizing: border-box;
  padding: 0;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  height: auto;
}

input[type="search"] {
  -webkit-appearance: none;
  box-sizing: content-box;
}

input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 6px 10px 12px;
}

legend {
  border: 0;
  padding: 0;
}

textarea {
  overflow: auto;
}

optgroup {
  font-weight: bold;
}

/**
 * Tables
 */

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td,
th {
  padding: 0;
}
.slick-slider {
  display: block;
  position: relative;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.slick-list {
  position: relative;
  display: block;
  overflow: hidden;
  margin: 0;
  padding: 0;

  &:focus {
    outline: none;
  }

  &.dragging {
    cursor: pointer;
  }
}

.slick-track,
.slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.slick-track {
  display: block;
  position: relative;
  top: 0;
  left: 0;

  &:before,
  &:after {
    display: table;
    content: '';
  }

  &:after {
    clear: both;
  }
}

.slick-loading .slick-track {
  visibility: hidden;
}

.slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
  overflow: hidden;
}

[dir='rtl'] .slick-slide {
  float: right;
}

.slick-slide img {
  display: block;
}

.slick-slide.slick-loading img {
  display: none;
}

.slick-slide.dragging img {
  pointer-events: none;
}

.slick-initialized .slick-slide {
  display: block !important;
}

.slick-loading .slick-slide {
  visibility: hidden;
}

.slick-vertical .slick-slide {
  display: block;
  height: auto;
  border: 1px solid transparent;
}

.slick-arrow.slick-hidden {
  display: none;
}
/*! Avalanche | MIT License | @colourgarden */

/*------------------------------------
 *   GRID LAYOUT
 *------------------------------------*/

.grid {
  display: block;
  list-style: none;
  padding: 0;
  margin: 0 0 0 -25px;
  font-size: 0;
}

.grid__cell {
  box-sizing: border-box;
  display: inline-block;
  width: 100%;
  padding: 0 0 0 25px;
  margin: 0;
  vertical-align: top;
  font-size: 1rem;
}

.grid--gallery {
  margin-bottom: -25px;
}

.grid--gallery > .grid__cell {
  padding-bottom: 25px;
}

.grid--center {
  text-align: center;
}

.grid--center > .grid__cell {
  text-align: left;
}

.grid__cell--center {
  display: block;
  margin: 0 auto;
}

.grid--right {
  text-align: right;
}

.grid--right > .grid__cell {
  text-align: left;
}

.grid--middle > .grid__cell {
  vertical-align: middle;
}

.grid--bottom > .grid__cell {
  vertical-align: bottom;
}

.grid--flush {
  margin-left: 0;
}

.grid--flush > .grid__cell {
  padding-left: 0;
}

.grid--tiny {
  margin-left: -6.25px;
}

.grid--tiny > .grid__cell {
  padding-left: 6.25px;
}

.grid--gallery-tiny {
  margin-bottom: -6.25px;
}

.grid--gallery.grid--tiny > .grid__cell,
.grid--gallery-tiny > .grid__cell {
  padding-bottom: 6.25px;
}

.grid--small {
  margin-left: -12.5px;
}

.grid--small > .grid__cell {
  padding-left: 12.5px;
}

.grid--gallery-small {
  margin-bottom: -12.5px;
}

.grid--gallery.grid--small > .grid__cell,
.grid--gallery-small > .grid__cell {
  padding-bottom: 12.5px;
}

.grid--large {
  margin-left: -50px;
}

.grid--large > .grid__cell {
  padding-left: 50px;
}

.grid--gallery-large {
  margin-bottom: -50px;
}

.grid--gallery.grid--large > .grid__cell,
.grid--gallery.grid--gallery-large > .grid__cell {
  padding-bottom: 50px;
}

.grid--huge {
  margin-left: -65px;
}

.grid--huge > .grid__cell {
  padding-left: 65px;
}

.grid--gallery-huge {
  margin-bottom: -65px;
}

.grid--gallery.grid--huge > .grid__cell,
.grid--gallery.grid--gallery-huge > .grid__cell {
  padding-bottom: 65px;
}

.grid--auto > .grid__cell {
  width: auto;
}

/*------------------------------------*\
    GRID WIDTHS
\*------------------------------------*/

.\31\/2, .\32\/4, .\33\/6 {
  width: 50%;
}

.\31\/3, .\32\/6 {
  width: 33.33333%;
}

.\32\/3, .\34\/6 {
  width: 66.66667%;
}

.\31\/4 {
  width: 25%;
}

.\33\/4 {
  width: 75%;
}

.\31\/5 {
  width: 20%;
}

.\32\/5 {
  width: 40%;
}

.\33\/5 {
  width: 60%;
}

.\34\/5 {
  width: 80%;
}

.\31\/6 {
  width: 16.66667%;
}

.\35\/6 {
  width: 83.33333%;
}

/* If responsive flag enabled, loop breakpoint widths */
/* Create each media query */
@media screen and (max-width: 499px) {
  .\31\/2--thumb, .\32\/4--thumb, .\33\/6--thumb {
    width: 50%;
  }

  .\31\/3--thumb, .\32\/6--thumb {
    width: 33.33333%;
  }

  .\32\/3--thumb, .\34\/6--thumb {
    width: 66.66667%;
  }

  .\31\/4--thumb {
    width: 25%;
  }

  .\33\/4--thumb {
    width: 75%;
  }

  .\31\/5--thumb {
    width: 20%;
  }

  .\32\/5--thumb {
    width: 40%;
  }

  .\33\/5--thumb {
    width: 60%;
  }

  .\34\/5--thumb {
    width: 80%;
  }

  .\31\/6--thumb {
    width: 16.66667%;
  }

  .\35\/6--thumb {
    width: 83.33333%;
  }
}
/* Create each media query */
@media screen and (min-width: 500px) and (max-width: 800px) {
  .\31\/2--handheld, .\32\/4--handheld, .\33\/6--handheld {
    width: 50%;
  }

  .\31\/3--handheld, .\32\/6--handheld {
    width: 33.33333%;
  }

  .\32\/3--handheld, .\34\/6--handheld {
    width: 66.66667%;
  }

  .\31\/4--handheld {
    width: 25%;
  }

  .\33\/4--handheld {
    width: 75%;
  }

  .\31\/5--handheld {
    width: 20%;
  }

  .\32\/5--handheld {
    width: 40%;
  }

  .\33\/5--handheld {
    width: 60%;
  }

  .\34\/5--handheld {
    width: 80%;
  }

  .\31\/6--handheld {
    width: 16.66667%;
  }

  .\35\/6--handheld {
    width: 83.33333%;
  }
}
/* Create each media query */
@media screen and (min-width: 500px) {
  .\31\/2--handheld-and-up, .\32\/4--handheld-and-up, .\33\/6--handheld-and-up {
    width: 50%;
  }

  .\31\/3--handheld-and-up, .\32\/6--handheld-and-up {
    width: 33.33333%;
  }

  .\32\/3--handheld-and-up, .\34\/6--handheld-and-up {
    width: 66.66667%;
  }

  .\31\/4--handheld-and-up {
    width: 25%;
  }

  .\33\/4--handheld-and-up {
    width: 75%;
  }

  .\31\/5--handheld-and-up {
    width: 20%;
  }

  .\32\/5--handheld-and-up {
    width: 40%;
  }

  .\33\/5--handheld-and-up {
    width: 60%;
  }

  .\34\/5--handheld-and-up {
    width: 80%;
  }

  .\31\/6--handheld-and-up {
    width: 16.66667%;
  }

  .\35\/6--handheld-and-up {
    width: 83.33333%;
  }
}
/* Create each media query */
@media screen and (max-width: 800px) {
  .\31\/2--pocket, .\32\/4--pocket, .\33\/6--pocket {
    width: 50%;
  }

  .\31\/3--pocket, .\32\/6--pocket {
    width: 33.33333%;
  }

  .\32\/3--pocket, .\34\/6--pocket {
    width: 66.66667%;
  }

  .\31\/4--pocket {
    width: 25%;
  }

  .\33\/4--pocket {
    width: 75%;
  }

  .\31\/5--pocket {
    width: 20%;
  }

  .\32\/5--pocket {
    width: 40%;
  }

  .\33\/5--pocket {
    width: 60%;
  }

  .\34\/5--pocket {
    width: 80%;
  }

  .\31\/6--pocket {
    width: 16.66667%;
  }

  .\35\/6--pocket {
    width: 83.33333%;
  }
}
/* Create each media query */
@media screen and (min-width: 801px) and (max-width: 1024px) {
  .\31\/2--lap, .\32\/4--lap, .\33\/6--lap {
    width: 50%;
  }

  .\31\/3--lap, .\32\/6--lap {
    width: 33.33333%;
  }

  .\32\/3--lap, .\34\/6--lap {
    width: 66.66667%;
  }

  .\31\/4--lap {
    width: 25%;
  }

  .\33\/4--lap {
    width: 75%;
  }

  .\31\/5--lap {
    width: 20%;
  }

  .\32\/5--lap {
    width: 40%;
  }

  .\33\/5--lap {
    width: 60%;
  }

  .\34\/5--lap {
    width: 80%;
  }

  .\31\/6--lap {
    width: 16.66667%;
  }

  .\35\/6--lap {
    width: 83.33333%;
  }
}
/* Create each media query */
@media screen and (min-width: 801px) {
  .\31\/2--lap-and-up, .\32\/4--lap-and-up, .\33\/6--lap-and-up {
    width: 50%;
  }

  .\31\/3--lap-and-up, .\32\/6--lap-and-up {
    width: 33.33333%;
  }

  .\32\/3--lap-and-up, .\34\/6--lap-and-up {
    width: 66.66667%;
  }

  .\31\/4--lap-and-up {
    width: 25%;
  }

  .\33\/4--lap-and-up {
    width: 75%;
  }

  .\31\/5--lap-and-up {
    width: 20%;
  }

  .\32\/5--lap-and-up {
    width: 40%;
  }

  .\33\/5--lap-and-up {
    width: 60%;
  }

  .\34\/5--lap-and-up {
    width: 80%;
  }

  .\31\/6--lap-and-up {
    width: 16.66667%;
  }

  .\35\/6--lap-and-up {
    width: 83.33333%;
  }
}
/* Create each media query */
@media screen and (max-width: 1024px) {
  .\31\/2--portable, .\32\/4--portable, .\33\/6--portable {
    width: 50%;
  }

  .\31\/3--portable, .\32\/6--portable {
    width: 33.33333%;
  }

  .\32\/3--portable, .\34\/6--portable {
    width: 66.66667%;
  }

  .\31\/4--portable {
    width: 25%;
  }

  .\33\/4--portable {
    width: 75%;
  }

  .\31\/5--portable {
    width: 20%;
  }

  .\32\/5--portable {
    width: 40%;
  }

  .\33\/5--portable {
    width: 60%;
  }

  .\34\/5--portable {
    width: 80%;
  }

  .\31\/6--portable {
    width: 16.66667%;
  }

  .\35\/6--portable {
    width: 83.33333%;
  }
}
/* Create each media query */
@media screen and (min-width: 1025px) {
  .\31\/2--desk, .\32\/4--desk, .\33\/6--desk {
    width: 50%;
  }

  .\31\/3--desk, .\32\/6--desk {
    width: 33.33333%;
  }

  .\32\/3--desk, .\34\/6--desk {
    width: 66.66667%;
  }

  .\31\/4--desk {
    width: 25%;
  }

  .\33\/4--desk {
    width: 75%;
  }

  .\31\/5--desk {
    width: 20%;
  }

  .\32\/5--desk {
    width: 40%;
  }

  .\33\/5--desk {
    width: 60%;
  }

  .\34\/5--desk {
    width: 80%;
  }

  .\31\/6--desk {
    width: 16.66667%;
  }

  .\35\/6--desk {
    width: 83.33333%;
  }
}
/* Create each media query */
@media screen and (min-width: 1160px) {
  .\31\/2--widescreen, .\32\/4--widescreen, .\33\/6--widescreen {
    width: 50%;
  }

  .\31\/3--widescreen, .\32\/6--widescreen {
    width: 33.33333%;
  }

  .\32\/3--widescreen, .\34\/6--widescreen {
    width: 66.66667%;
  }

  .\31\/4--widescreen {
    width: 25%;
  }

  .\33\/4--widescreen {
    width: 75%;
  }

  .\31\/5--widescreen {
    width: 20%;
  }

  .\32\/5--widescreen {
    width: 40%;
  }

  .\33\/5--widescreen {
    width: 60%;
  }

  .\34\/5--widescreen {
    width: 80%;
  }

  .\31\/6--widescreen {
    width: 16.66667%;
  }

  .\35\/6--widescreen {
    width: 83.33333%;
  }
}
/* Create each media query */
@media screen and (-webkit-min-device-pixel-ratio: 2), screen and (min-resolution: 192dpi), screen and (min-resolution: 2dppx) {
  .\31\/2--retina, .\32\/4--retina, .\33\/6--retina {
    width: 50%;
  }

  .\31\/3--retina, .\32\/6--retina {
    width: 33.33333%;
  }

  .\32\/3--retina, .\34\/6--retina {
    width: 66.66667%;
  }

  .\31\/4--retina {
    width: 25%;
  }

  .\33\/4--retina {
    width: 75%;
  }

  .\31\/5--retina {
    width: 20%;
  }

  .\32\/5--retina {
    width: 40%;
  }

  .\33\/5--retina {
    width: 60%;
  }

  .\34\/5--retina {
    width: 80%;
  }

  .\31\/6--retina {
    width: 16.66667%;
  }

  .\35\/6--retina {
    width: 83.33333%;
  }
}

@mixin av-mq($alias) {
  @if $alias == "thumb" {
    @media screen and (max-width: 499px) {
      @content;
    }
  } @else if $alias == "handheld" {
    @media screen and (min-width: 500px) and (max-width: 800px) {
      @content;
    }
  } @else if $alias == "handheld-and-up" {
    @media screen and (min-width: 500px) {
      @content;
    }
  } @else if $alias == "pocket" {
    @media screen and (max-width: 800px) {
      @content;
    }
  } @else if $alias == "lap" {
    @media screen and (min-width: 801px) and (max-width: 1024px) {
      @content;
    }
  } @else if $alias == "lap-and-up" {
    @media screen and (min-width: 801px) {
      @content;
    }
  } @else if $alias == "portable" {
    @media screen and (max-width: 1024px) {
      @content;
    }
  } @else if $alias == "desk" {
    @media screen and (min-width: 1025px) {
      @content;
    }
  } @else if $alias == "widescreen" {
    @media screen and (min-width: 1160px) {
      @content;
    }
  } @else if $alias == "large-widescreen" {
    @media screen and (min-width: 1440px) {
      @content;
    }
  } @else if $alias == "retina" {
    @media screen and (-webkit-min-device-pixel-ratio: 2), screen and (min-resolution: 192dpi), screen and (min-resolution: 2dppx) {
      @content;
    }
  }
}

/**
 * Small adjustments based on size for the gallery grid
 */

@include av-mq('thumb') {
  .grid--gallery {
    margin-bottom: -10px;
    margin-left: -10px;
  }

  .grid--gallery > .grid__cell {
    padding-bottom: 10px;
    padding-left: 10px;
  }

  .grid--gallery.grid--gallery-large > .grid__cell {
    padding-bottom: 35px;
  }
}
/*
 *  Remodal - v1.1.0
 *  Responsive, lightweight, fast, synchronized with CSS animations, fully customizable modal window plugin with declarative configuration and hash tracking.
 *  http://vodkabears.github.io/remodal/
 *
 *  Made by Ilya Makarov
 *  Under MIT License
 */

/* Hide scroll bar */

html.remodal-is-locked {
  overflow: hidden;
  -ms-touch-action: none;
  touch-action: none;
}

.remodal,
[data-remodal-id] {
  display: none;
}

/* Necessary styles of the overlay */

.remodal-overlay {
  position: fixed;
  z-index: 9999;
  top: -5000px;
  right: -5000px;
  bottom: -5000px;
  left: -5000px;
  display: none;
}

/* Necessary styles of the wrapper */

.remodal-wrapper {
  position: fixed;
  z-index: 10000;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: none;
  overflow: auto;
  text-align: center;
  -webkit-overflow-scrolling: touch;
}

.remodal-wrapper:after {
  display: inline-block;
  height: 100%;
  margin-left: -0.05em;
  content: "";
}

/* Fix iPad, iPhone glitches */

.remodal-overlay,
.remodal-wrapper {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

/* Necessary styles of the modal dialog */

.remodal {
  position: relative;
  outline: none;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

.remodal-is-initialized {
  display: inline-block;
}

/* Default theme styles of the overlay */

.remodal-overlay {
  background: rgba(#000000, 0.4);
}

.remodal-overlay.remodal-is-opening,
.remodal-overlay.remodal-is-closing {
  -webkit-animation-duration: 0.3s;
  animation-duration: 0.3s;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
}

.remodal-overlay.remodal-is-opening {
  -webkit-animation-name: remodal-overlay-opening-keyframes;
  animation-name: remodal-overlay-opening-keyframes;
}

.remodal-overlay.remodal-is-closing {
  -webkit-animation-name: remodal-overlay-closing-keyframes;
  animation-name: remodal-overlay-closing-keyframes;
}

/* Default theme styles of the modal dialog */

.remodal {
  box-sizing: border-box;
  width: 100%;
  margin-bottom: 10px;
  padding: 35px;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.remodal.remodal-is-opening,
.remodal.remodal-is-closing {
  -webkit-animation-duration: 0.3s;
  animation-duration: 0.3s;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
}

.remodal.remodal-is-opening {
  -webkit-animation-name: remodal-opening-keyframes;
  animation-name: remodal-opening-keyframes;
}

.remodal.remodal-is-closing {
  -webkit-animation-name: remodal-closing-keyframes;
  animation-name: remodal-closing-keyframes;
}

/* Vertical align of the modal dialog */

.remodal,
.remodal-wrapper:after {
  vertical-align: middle;
}

@-webkit-keyframes remodal-opening-keyframes {
  from {
    -webkit-transform: scale(1.05);
    transform: scale(1.05);
    opacity: 0;
  }
  to {
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}

@keyframes remodal-opening-keyframes {
  from {
    -webkit-transform: scale(1.05);
    transform: scale(1.05);
    opacity: 0;
  }
  to {
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}

@-webkit-keyframes remodal-closing-keyframes {
  from {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
  }
  to {
    -webkit-transform: scale(0.95);
    transform: scale(0.95);
    opacity: 0;
  }
}

@keyframes remodal-closing-keyframes {
  from {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
  }
  to {
    -webkit-transform: scale(0.95);
    transform: scale(0.95);
    opacity: 0;
  }
}

@-webkit-keyframes remodal-overlay-opening-keyframes {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes remodal-overlay-opening-keyframes {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@-webkit-keyframes remodal-overlay-closing-keyframes {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes remodal-overlay-closing-keyframes {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.ftg {
  position: relative;

  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

.ftg .tile {
  float: left;
  overflow: hidden;
  margin: 0;
  opacity: 0;
}

.ftg .tile img {
  transition: -webkit-transform .2s linear;
  transition: transform .2s linear;
  display: block;
  position: relative;
  width: 100%;
}

.ftg .tile iframe {
  width: 100%;
}

.ftg .tile.ftg-enlarged img {
  display: block;
  margin: auto;
}

.ftg .tile.ftg-loaded {
  opacity: 1;
}

.ftg .tile.ftg-hidden {
  -webkit-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  opacity: 0;
}

@function dark-or-light-color($color) {
  @if (lightness($color) > 70) {
    @return #000000; /* Lighter background, return dark color */
  } @else {
    @return #ffffff; /* Darker background, return light color */
  }
}

/**
 * BASE COLORS
 */

$background          : {{settings.background}};
$secondary-background: {{settings.secondary_background}};
$heading-color       : {{settings.heading_color}};
$text-color          : {{settings.text_color}};
$accent-color        : {{settings.accent_color}};
$border-color        : {{settings.border_color}};

$primary-button-color       : {{settings.primary_button_color}};
$primary-button-background  : {{settings.primary_button_background}};
$secondary-button-color     : {{settings.secondary_button_color}};
$secondary-button-background: {{settings.secondary_button_background}};

$header-color       : {{settings.header_color}};
$header-background  : {{settings.header_background}};
$header-accent-color: {{settings.header_accent_color}};
$header-border-color: {{settings.header_border_color}};

$navigation-sidebar-color     : {{settings.navigation_sidebar_color}};
$navigation-sidebar-background: {{settings.navigation_sidebar_background}};
$navigation-sidebar-border    : {{settings.navigation_sidebar_border}};

$newsletter-color     : {{settings.newsletter_color}};
$newsletter-background: {{settings.newsletter_background}};

$slideshow-heading-color      : {{settings.slideshow_heading_color}};
$slideshow-subheading-color   : {{settings.slideshow_subheading_color}};

$product-label-on-sale-background  : {{settings.product_label_on_sale_background}};
$product-label-sold-out-background : {{settings.product_label_sold_out_background}};

/**
 * TYPOGRAPHY
 */

{% if settings.heading_font contains 'Google' %}
  {% assign font_parts = settings.heading_font | split: '_' %}
  $heading-font: {{font_parts[1] | replace: '+', ' ' }};
{% else %}
  $heading-font: {{settings.heading_font}};
{% endif %}

{% if settings.text_font contains 'Google' %}
  {% assign font_parts = settings.text_font | split: '_' %}
  $text-font: {{font_parts[1] | replace: '+', ' ' }};
{% else %}
  $text-font: {{settings.text_font}};
{% endif %}

{% if settings.slideshow_heading_font contains 'Google' %}
  {% assign font_parts = settings.slideshow_heading_font | split: '_' %}
  $slideshow-heading-font: {{font_parts[1] | replace: '+', ' ' }};
{% else %}
  $slideshow-heading-font: {{settings.slideshow_heading_font}};
{% endif %}

{% if settings.slideshow_subheading_font contains 'Google' %}
  {% assign font_parts = settings.slideshow_subheading_font | split: '_' %}
  $slideshow-subheading-font: {{font_parts[1] | replace: '+', ' ' }};
{% else %}
  $slideshow-subheading-font: {{settings.slideshow_subheading_font}};
{% endif %}

$page-title-font-size: {{settings.page_title_font_size}};

/**
 * SLIDESHOW
 */

$slideshow-heading-font-size   : {{settings.slideshow_heading_font_size}};
$slideshow-subheading-font-size: {{settings.slideshow_subheading_font_size}};

/**
 * COLLECTION
 */

$collection-mobile-grid-items-per-row: {{settings.mobile_grid_items_per_row}};
$collection-tablet-grid-items-per-row: {{settings.tablet_grid_items_per_row}};
/**
 * Sticky footer, normalized to work on all browsers (http://philipwalton.com/articles/normalizing-cross-browser-flexbox-bugs/)
 */

.page__container {
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  min-height: 100vh;
  height: 100%;
  background: $background;
}

.page__container--secondary {
  background: $secondary-background;
}

.shopify-section__announcement-bar,
.shopify-section__header,
.shopify-section__footer {
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

.main {
  -webkit-flex: 1 0 auto;
  -ms-flex: 1 0 auto;
  flex: 1 0 auto;
}

.js .no-js {
  display: none !important;
}

.no-scroll {
  overflow: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.container--shrink {
  max-width: 700px;
}

.inner {
  margin: 0 auto;
  padding: 0 20px;
}

.page__overlay {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: #000000;
  opacity: 0;
  z-index: 5;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0s 0.3s;
}

.page__overlay--open {
  opacity: 0.6;
  visibility: visible;
  transition: opacity 0.3s;
}

.accessibility-skip {
  position: absolute;
  list-style: none;
  top: -40px;
}

.accessibility-skip__item > a {
  position: absolute;
  left: -9999em;
  transition: none !important;

  &:active,
  &:focus {
    display: block;
    position: static;
    left: 0;
  }
}

.visually-hidden {
  position: absolute;
  overflow: hidden;
  clip: rect(0 0 0 0);
  height: 1px; width: 1px;
  margin: -1px; padding: 0; border: 0;
}

.anchor {
  display: block;
  position: relative;
  top: -75px;
  visibility: hidden;
}

.icon-cross-container {
  position: relative;
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 100%;
  background: $secondary-button-background;
  color: $secondary-button-color;
  transition: all 0.2s ease-in-out;

  &:hover {
    background: $accent-color;
    color: dark-or-light-color($accent-color);
  }

  svg {
    position: absolute;
    width: 8px;
    height: 8px;
    left: calc(50% - 4px);
    top: calc(50% - 4px);
  }
}

.placeholder-svg {
  display: block;
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  fill: $text-color;
}

@include av-mq("lap-and-up") {
  .inner,
  .container {
    padding: 0 25px;
  }
}
body {
  font-size: 100%;
  font-weight: 400;
  font-family: $text-font;
  color: $text-color;
  line-height: 1.6;
  scroll-behavior: smooth;
}

a {
  transition: all 0.25s ease-in-out;
}

.link--primary {
  color: $accent-color;

  &:hover {
    color: darken($accent-color, 10%);
  }
}

.icon {
  display: inline-block;
  width: 1em;
  height: 1em;
  fill: currentColor;
  vertical-align: middle;
}

/**
 * Those next two rules allow to remove margin-top, this is easier to maintain to only act on the margin-bottom
 */

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  font-weight: inherit;
}

p:last-child,
h1:last-child,
h2:last-child,
h3:last-child,
h4:last-child,
h5:last-child,
h6:last-child {
  margin-bottom: 0;
}

p {
  line-height: 2;
}

.visually-hidden {
  position: absolute;
  overflow: hidden;
  clip: rect(0 0 0 0);
  height: 1px;
  width: 1px;
  margin: -1px;
  padding: 0;
  border: 0;
}

.list--unstyled {
  list-style: none;
  padding: 0;
  margin: 0;
}

.rte {
  line-height: 2;

  img,
  iframe {
    max-width: 100%;
  }

  a:not(.button) {
    color: $accent-color;

    &:hover {
      color: darken($accent-color, 15%);
    }
  }

  p:last-child,
  h1:last-child,
  h2:last-child,
  h3:last-child,
  h4:last-child,
  h5:last-child,
  h6:last-child {
    margin-bottom: 0;
  }

  p,
  ul,
  ol,
  .button-group {
    margin-bottom: 22px;
  }

  img {
    margin-bottom: 30px;
  }

  ul, ol {
    list-style-position: inside;
  }

  ul {
    padding-left: 0;
    margin-left: 26px;
  }

  ol {
    padding-left: 0;
    margin-left: 30px;
  }

  blockquote {
    margin: 24px 0;
    padding: 22px 28px;
    background: $secondary-background;
    border-left: 3px solid darken($text-color, 20%);
  }

  h1, h2, h3, h4, h5, h6 {
    margin: 14px 0;
    color: $heading-color;
    font-family: $heading-font;
    line-height: 1.6;
    font-weight: 700;
  }

  h1 {
    margin-bottom: 20px;
    font-size: 26px;
  }

  h2 {
    margin-bottom: 18px;
    font-size: 24px;
  }

  h3 {
    margin-bottom: 16px;
    font-size: 22px;
  }

  h4 {
    margin-bottom: 14px;
    font-size: 20px;
  }

  h5 {
    margin-bottom: 12px;
    font-size: 18px;
  }

  h6 {
    margin-bottom: 12px;
    font-size: 16px;
  }

  table {
    tr {
      border: 1px solid $border-color;
    }

    th {
      padding: 14px 34px;
      background: $border-color;
      color: $background;
      border-right: 1px solid $background;

      &:last-child {
        border-right: none;
      }
    }

    td {
      padding: 24px 34px;
      border-right: 1px solid $border-color;
    }
  }
}

.video-wrapper,
.video-container {
  height: 0;
  padding-bottom: 56.25%;
  position: relative;

  iframe,
  embed,
  object {
    position: absolute;
    top: 0;
    left: 0;
    width: 100% !important;
    height: 100% !important;
  }
}

@include av-mq('handheld-and-up') {
  .rte {
    p,
    ul,
    ol,
    .button-group {
      margin-bottom: 26px;
    }

    h1 {
      font-size: 34px;
    }

    h2 {
      font-size: 30px;
    }

    h3 {
      font-size: 26px;
    }

    h4 {
      font-size: 24px;
    }

    h5 {
      font-size: 20px;
    }

    h6 {
      font-size: 18px;
    }
  }
}

/**
 * Those two breakpoints allow to reduce the font-size of all texts as well as the margin and
 * padding, thanks to the magic of REM
 */

html {
  font-size: 14px;
}

@include av-mq("handheld-and-up") {
  html {
    font-size: 15px;
  }
}

@include av-mq("desk") {
  html {
    font-size: 16px;
  }
}

.announcement-bar {
  display: block;
  padding: 5px 10px;
  font-size: 12px;
}

.announcement-bar__content {
  margin: 0;
}

@include av-mq('handheld-and-up') {
  .announcement-bar {
    padding: 8px 34px;
    font-size: 14px;
  }
}
.alert {
  border-radius: 2px;
  padding: 15px;
  font-size: 90%;

  & + .form__control {
    margin-top: 25px;
  }
}

.alert__title {
  margin: 0;
  font-weight: 700;
}

.alert--error {
  color: #ffffff;
  background: #da3a3a;
}

.alert--success {
  color: #ffffff;
  background: #35a523;
}

.alert__error-list {
  padding-left: 26px;
  margin-bottom: 0;
}

.alert__error-item {
  margin-bottom: 5px;

  &:last-child {
    margin-bottom: 0;
  }
}

@include av-mq('handheld-and-up') {
  .alert {
    padding: 15px 20px;

    & + .form__control {
      margin-top: 48px;
    }
  }
}
/**
 * ARTICLE
 */

.article__image-link {
  display: block;
  font-size: 0;
}

.article__image {
  width: 100%;
}

.article__meta {
  margin: 12px 0;
  font-size: 12px;
  text-transform: uppercase;
}

.article__separator {
  padding: 0 5px;
  font-size: 16px;
  vertical-align: middle;
}

.article__comments {
  float: right;
  margin-top: 1px;

  &:hover {
    color: $accent-color;
  }

  svg {
    margin-left: 8px;
    width: 16px;
    height: 16px;
  }
}

.article__title {
  margin: 0 0 20px 0;
  font-size: 18px;
  color: $heading-color;
  font-weight: 700;
}

.article__excerpt {
  margin-top: 14px;
  margin-bottom: 8px;
}

.article__read-more {
  display: inline-block;
  margin-top: 8px;
  text-transform: uppercase;
  font-size: 12px;
  color: $accent-color;

  &:hover {
    color: darken($accent-color, 10%);

    svg {
      -webkit-transform: translateX(3px);
      -ms-transform: translateX(3px);
      transform: translateX(3px);
    }
  }

  svg {
    display: inline-block;
    width: 10px;
    height: 10px;
    margin-left: 8px;
    vertical-align: baseline;
    transition: -webkit-transform 0.25s ease-in-out;
    transition: transform 0.25s ease-in-out;
  }
}

.article--full {
  margin-top: 25px;
  text-align: center;

  .article__image {
    padding: 0 20px;
  }

  .article__header {
    margin-bottom: 30px;
  }

  .article__body {
    clear: both;
    text-align: left;
    margin: 0 auto;
    overflow: hidden;
  }
}

.article__misc {
  margin-top: 40px;
  padding: 25px 0;
  text-align: center;
  border-top: 1px solid $border-color;
}

.article__tags {
  margin-top: 22px;
  font-size: 14px;
}

.article__tags-list {
  display: inline-block;
  list-style: none;
  padding: 0;
  margin: 0;
}

.article__tags-label {
  display: inline-block;
  margin-right: 25px;
  font-size: 14px;
  text-transform: uppercase;
}

.article__tags--mini {
  margin-top: 16px;
  font-size: 15px;
  font-weight: 400;
}

.article .share-buttons__label,
.article__tags-label {
  color: $heading-color;
}

.article__tags-item {
  display: inline-block;

  &:last-child {
    margin-right: 0;
  }

  a:hover {
    color: $accent-color;
  }
}

@include av-mq('handheld-and-up') {
  .article__meta {
    font-size: 14px;
  }

  .article__comments {
    margin-top: 0;
  }

  .article__title {
    margin-bottom: 35px;
    font-size: 20px;
  }

  .article__excerpt {
    margin-top: 30px;
    margin-bottom: 14px;
  }

  .article__read-more {
    font-size: 14px;
  }

  .article--full {
    .article__header {
      margin-bottom: 45px;
    }

    .article__image {
      max-width: 780px;
    }

    .article__title {
      font-size: 36px;
    }

    .article__image + .article__body {
      margin-top: 26px;
    }
  }

  .article__misc {
    margin-top: 68px;
    padding: 34px 0 38px 0;
  }

  .article__tags {
    margin-top: 40px;
    font-size: 16px;
  }

  .article__tags-label {
    margin-right: 40px;
    font-size: 14px;
  }
}

/**
 * COMMENTS
 */

.comments {
  background: $secondary-background;
  border-top: 1px solid $border-color;
  border-bottom: 1px solid $border-color;
}

.comments__header {
  padding: 28px 0;
}

.comments__count {
  display: inline-block;
  margin: 0;
  color: $heading-color;
  font-size: 16px;
  font-weight: 700;
}

.comments__write {
  float: right;
  color: $accent-color;
  font-size: 14px;

  &:hover {
    color: darken($accent-color, 10%);
  }
}

/**
 * COMMENT DETAIL
 */

.comment {
  margin-bottom: 20px;
  padding: 20px 25px;
  background: $background;
  border: 1px solid $border-color;

  &:first-child {
    margin-top: 40px;
  }

  &:last-child {
    margin-bottom: 32px;
  }
}

.comment__gravatar {
  max-width: 46px;
  float: left;
  margin: 6px 25px 0 0;
  border-radius: 100%;
}

.comment__author {
  margin: 0 0 6px 0;
  color: $heading-color;
  font-size: 14px;
  font-weight: 700;
}

.comment__date {
  text-transform: uppercase;
  font-size: 12px;
}

.comment__content {
  margin-top: 26px;
  clear: both;
}

.comment__form {
  margin-bottom: 50px;
}

/**
 * FORM
 */

.comment-form__header {
  margin-bottom: 30px;
  padding: 15px 0;
  border-bottom: 1px solid $border-color;
}

.comment-form__heading {
  color: $heading-color;
  font-size: 16px;
  font-weight: 700;
}

.comment-form__approval {
  display: block;
  margin: -15px 0 15px 0;
  text-align: center;
}

.comment-form__submit {
  margin-top: 30px;
}

@include av-mq('handheld-and-up') {
  .comments__count,
  .comment-form__heading {
    font-size: 20px;
  }

  .comments__header {
    padding: 55px 0;
  }

  .comments__write {
    font-size: 16px;
  }

  .comment {
    padding: 28px 30px 20px 30px;

    &:last-child {
      margin-bottom: 60px;
    }
  }

  .comment__gravatar {
    margin-top: 10px;
  }

  .comment__author {
    font-size: 16px;
  }

  .comment__date {
    font-size: 14px;
  }

  .comment-form__header {
    margin-bottom: 46px;
    padding: 32px 0 26px 0;
  }
}
.blog__rss {
  display: none;
  float: right;
  margin: 15px 0 0 0;
  font-size: 12px;
  text-transform: uppercase;

  &:hover {
    color: $accent-color;
  }

  svg {
    margin-left: 10px;
    vertical-align: -1px;
  }
}

.blog__tags {
  display: none;
}

@include av-mq('handheld-and-up') {
  .blog__rss {
    display: inline-block;
    float: right;
    margin: 15px 0 0 0;
    font-size: 12px;
    text-transform: uppercase;

    &:hover {
      color: $accent-color;
    }

    svg {
      margin-left: 10px;
      vertical-align: -1px;
    }
  }

  .blog__tags {
    display: block;
    margin: 20px 0 6px 0;
    text-align: center;
  }

  .blog__tag {
    display: inline-block;
    margin: 0 60px 28px 0;

    &:last-child {
      margin-right: 0;
    }
  }

  .blog__tag:hover,
  .blog__tag--active {
    color: $accent-color;
  }

  .blog__tag .icon-cross-container {
    margin-left: 8px;
    vertical-align: middle;
  }

  .blog__tag--active .icon-cross-container {
    background: $accent-color;
    color: dark-or-light-color($accent-color);
  }

  .page__header .blog__rss {
    position: absolute;
    top: 0;
    right: 0;
  }
}
.breadcrumb {
  display: none;
}

@include av-mq('handheld-and-up') {
  .breadcrumb {
    display: inline-block;
    margin: 15px 0 0 0;
    font-size: 12px;
    text-transform: uppercase;
    color: $accent-color;
  }

  .breadcrumb__list {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  .breadcrumb__item {
    float: left; // This is used to fight spaces

    & + &:before {
      content: "\00A0" "\00BB";
      color: $text-color;
    }

    & + &[data-breadcrumb-separator]:before {
      content: "\00A0" attr(data-breadcrumb-separator);
      color: $text-color;
    }
  }

  .breadcrumb__item--active {
    color: $text-color;
  }

  /**
   * Breadcrumb within the page header is positioned absolutely to not take any space and let the title positioned in the middle
   */
  .page__header .breadcrumb {
    position: absolute;
    top: 0;
    left: 0;
  }
}
.button {
  position: relative;
  display: inline-block;
  padding: 12px 30px 13px 30px;
  line-height: normal;
  border-radius: 2px;
  border: none;
  text-transform: uppercase;
  font-size: 14px;
  transition: color 0.25s ease-in-out, background 0.25s ease-in-out;
  text-align: center;

  &:focus, &:active {
    outline: none;
  }

  &[disabled] {
    cursor: not-allowed;
  }

  &:not(.button--no-disabled-opacity)[disabled] {
    opacity: 0.4;
  }
}

.button--primary {
  color: $primary-button-color;
  background: $primary-button-background;

  &:hover {
    background: darken($primary-button-background, 10%);
  }
}

.button--secondary {
  color: $secondary-button-color;
  background: $secondary-button-background;

  &:hover {
    background: darken($secondary-button-background, 10%);
  }
}

.button--success,
.button--success:hover {
  background: #35a523;
  color: #ffffff;
}

.button--error,
.button--error:hover {
  background: #da3a3a;
  color: #ffffff;
}

.button--full {
  width: 100%;
}

.button + .button {
  margin-left: 15px;
}

.button-wrapper {
  text-align: center;
}

.button-group__item + .button-group__item {
  margin-top: 15px;
}

@include av-mq('handheld-and-up') {
  .button {
    padding: 15px 40px 16px 40px;
  }

  .button-group__item {
    display: inline-block;
    margin-top: 0;
  }

  .button-group__item + .button-group__item {
    margin-left: 15px;
  }
}
/**
 * GENERAL
 */

.icon-cart-full {
  color: $accent-color;
}

.cart-icon-wrapper {
  position: relative;
  text-align: center;

  svg {
    width: 25px;
    height: 25px;
  }
}

.cart-icon-wrapper__count {
  position: absolute;
  font-size: 12px;
  font-style: normal;
  color: $header-background;
  top: 7px;
  left: 0;
  line-height: normal;
  width: 100%;
}

/**
 * MAIN CART
 */

.cart--desktop,
.cart__meta--desktop {
  display: none;
}

.cart--desktop {
  display: none;
}

.cart--mobile {
  .cart__items {
    padding: 0;
    list-style: none;
  }

  .cart-item {
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    margin: 15px 0;
    padding: 0 20px 15px 20px;
    border-bottom: 1px solid $border-color;
  }

  .cart-item__left {
    margin-right: 20px;
    text-align: center;
  }

  .cart-item__right {
    -webkit-flex: 1 1 auto;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
  }

  .cart-item__link {
    display: inline-block;
    font-size: 0;
  }

  .cart-item__image {
    max-width: 100px;
    max-height: 100px;
  }

  .cart-item__title {
    color: $heading-color;
    font-weight: 700;
  }

  .cart-item__properties {
    list-style: none;
    padding: 0;
    margin: 10px 0 0 0;
    font-size: 13px;
    text-transform: uppercase;
  }

  .cart-item__remove {
    color: $accent-color;
  }

  .cart-item__remove {
    vertical-align: text-bottom;
  }

  .cart-item__quantity-input {
    display: inline-block;
    max-width: 60px;
    margin-right: 15px;
    padding-top: 10px;
    padding-bottom: 11px;
    text-align: center;
  }

  .cart-item__subtotal {
    display: block;
    margin: 12px 0 18px 0;
    color: $heading-color;
  }
}

.cart__meta {
  margin-bottom: 30px;
}

.cart__total {
  display: block;
  font-size: 18px;
  color: $heading-color;
  font-weight: 700;
}

.cart__currency-notice {
  position: relative;
  padding-top: 10px;
  margin-top: 10px;

  &:before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    height: 1px;
    width: 50px;
    background: $border-color;
  }
}

.cart__meta--mobile {
  margin-top: 25px;
  text-align: right;

  .cart__taxes {
    display: block;
    margin-top: 15px;
  }
}

.cart__note {
  margin-top: 25px;
  text-align: left;
}

.cart__checkout {
  margin-top: 30px;

  .button-wrapper {
    margin-top: 35px;
  }
}

@include av-mq('handheld-and-up') {
  .cart--mobile,
  .cart__meta--mobile {
    display: none;
  }

  .cart--desktop,
  .cart__meta--desktop {
    display: block;
  }

  .cart--desktop {
    table {
      width: 100%;
    }

    th {
      padding: 20px 15px;
      text-transform: uppercase;
      font-size: 14px;
      font-weight: 400;
      border-bottom: 1px solid $border-color;
      text-align: center;

      &:first-child {
        padding-left: 25px;
        text-align: left;
      }
    }

    td {
      text-align: center;
      padding: 20px 15px;
      border-bottom: 1px solid $border-color;

      &:first-child {
        padding-left: 25px;
        text-align: left;
      }
    }

    .cart-item__link {
      display: block;
      font-size: 0;
    }

    .cart-item__product {
      width: 100px;
    }

    .cart-item__image {
      float: left;
      max-width: 100px;
      max-height: 200px;
    }

    .cart-item__info {
      margin-top: 14px;
    }

    .cart-item__brand {
      margin-bottom: 12px;
    }

    .cart-item__brand,
    .cart-item__variant,
    .cart-item__properties {
      display: block;
      font-size: 14px;
      text-transform: uppercase;
    }

    .cart-item__properties {
      list-style: none;
      padding: 0;
      margin: 10px 0 0 0;
    }

    .cart-item__title {
      display: block;
      font-size: 18px;
      font-weight: 700;
      color: $heading-color;
    }

    .cart-item__price,
    .cart-item__price-header {
      display: none;
    }

    .cart-item__quantity {
      min-width: 170px;
    }

    .cart-item__variant {
      margin-top: 30px;
    }

    .cart-item__price,
    .cart-item__subtotal,
    .cart-item__quantity {
      font-size: 16px;
      color: $heading-color;
    }

    .cart-item__remove {
      padding-right: 25px;

      a {
        float: right;
      }
    }

    .quantity__actions {
      display: inline-block;
      margin-left: 20px;
      color: $text-color;
      font-size: 14px;
      vertical-align: -13px;
      border: 1px solid $border-color;
      border-radius: 20px;

      a {
        display: inline-block;
        height: 38px;
        line-height: 38px;
        padding: 0 18px 0 15px;
        float: left;

        &:hover {
          color: $accent-color;
        }

        &:first-child {
          padding: 0 15px 0 18px;
          border-right: 1px solid $border-color;
        }
      }
    }
  }

  .cart__meta {
    margin-top: 30px;
    overflow: hidden;
  }

  .cart__currency-notice {
    margin-top: 38px;
    padding-top: 32px;
  }

  .cart__note {
    max-width: 600px;
  }

  .cart__taxes {
    display: block;
    margin-top: 22px;
  }

  .cart__checkout {
    margin: 22px 0 48px 0;
    text-align: right;

    .button-wrapper {
      text-align: right;
    }
  }

  .additional-checkout-buttons {
    text-align: right;
  }
}

@include av-mq('desk') {
  .cart--desktop {
    td:first-child,
    th:first-child {
      padding-left: 50px;
    }

    .cart-item__remove {
      padding-right: 50px;
    }

    .cart-item__price-header,
    .cart-item__price {
      display: table-cell;
    }

    .cart-item__product {
      width: 160px;
    }

    .cart-item__image {
      max-width: 160px;
    }
  }
}

/**
 * ADDITIONAL CHECKOUT BUTTONS
 */

.additional-checkout-buttons {
  margin-top: 20px;

  & > *:not(script) {
    padding: 15px 0 0 15px;
    vertical-align: top;
    line-height: 1;

    &:first-child,
    &:empty {
      padding-left: 0;
    }
  }
}

@include av-mq('thumb') {
  .additional-checkout-buttons > *:not(script) {
    padding: 15px 0 0 5px;
  }
}

/**
 * SHIPPING ESTIMATOR
 */

.shipping-estimator {
  display: block;
  padding: 25px 0 30px 0;
  background: $secondary-background;
  border-top: 1px solid $border-color;

  .form-control {
    display: block;
  }
}

.shipping-estimator__results {
  margin-top: 30px;
  padding: 30px 0 0 0;
  border-top: 1px solid $border-color;
}

.shipping-estimator__title,
.shipping-estimator__results-title {
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 700;
  color: $heading-color;
}

.shipping-estimator__submit {
  width: auto;
  margin-top: 0;
}

@include av-mq('handheld-and-up') {
  .shipping-estimator {
    .form__control {
      display: inline-block;
      width: 20%;
      margin: 0 15px 0 0;
    }

    .form__control--zip {
      width: 10%;
    }
  }

  .flexbox {
    .shipping-estimator__form {
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
    }

    .shipping-estimator {
      .form__control {
        max-width: 400px;
        -webkit-flex: 2 1 auto;
        -ms-flex: 2 1 auto;
        flex: 2 1 auto;
      }

      .form__control--zip {
        max-width: 280px;
        -webkit-flex: 1 1 auto;
        -ms-flex: 1 1 auto;
        flex: 1 1 auto;
      }
    }

    .shipping-estimator__submit {
      margin-top: 34px;
    }
  }
}

@include av-mq('desk') {
  .shipping-estimator {
    padding: 45px 0 50px 0;

    .form__control {
      margin-right: 32px;
    }
  }

  .shipping-estimator__title {
    margin-bottom: 40px;
  }

  .shipping-estimator__results {
    padding-top: 45px;
  }
}

/**
 * MINI-CART
 */

.mini-cart {
  position: fixed;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  max-width: 390px;
  height: 100%;
  right: 0;
  z-index: 15;
  overflow: hidden;
  background: $secondary-background;
  -webkit-text-size-adjust: none;
  -webkit-transform: translateX(100%);
  -ms-transform: translateX(100%);
  transform: translateX(100%);
  transition: -webkit-transform 0.15s ease-in-out;
  transition: transform 0.15s ease-in-out;
}

.mini-cart--open {
  -webkit-transform: translateX(0%);
  -ms-transform: translateX(0%);
  transform: translateX(0%);
}

.mini-cart--reloading .mini-cart__content {
  .mini-cart__full,
  .mini-cart__empty {
    opacity: 0.2;
  }

  &::after {
    position: absolute;
    content: '';
    width: 50px;
    height: 50px;
    top: calc(50% - 25px);
    left: calc(50% - 25px);
    -webkit-animation: circle infinite .75s linear;
    animation: circle infinite .75s linear;
    border: 2px solid $secondary-button-background;
    border-left-color: transparent;
    border-radius: 100%;
  }
}

.mini-cart__header {
  display: table;
  width: 100%;
  background: $header-background;
  border-bottom: 1px solid $header-border-color;
}

.mini-cart__header-item {
  display: table-cell;
  padding: 0 25px;
  vertical-align: middle;
  line-height: 0;

  .cart-icon-wrapper {
    display: inline-block;
  }
}

.mini-cart__header-title {
  display: inline-block;
  margin: 0 0 0 22px;
  font-size: 20px;
  font-weight: 400;
  color: $header-color;
  vertical-align: -3px;
  line-height: normal;
}

.mini-cart__close {
  width: 70px;
  height: 100%;
  border-left: 1px solid $header-border-color;
  font-size: 22px;
  text-align: center;
  color: $header-color;

  &:hover {
    color: $header-accent-color;
  }
}

.mini-cart__content {
  position: relative;
  -webkit-flex: 1 0 auto;
  -ms-flex: 1 0 auto;
  flex: 1 0 auto;
}

.mini-cart__empty {
  position: absolute;
  text-align: center;
  top: 50%;
  left: 50%;
  width: 100%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.mini-cart__empty-title {
  margin: 0 0 20px 0;
  color: $heading-color;
  font-weight: 700;
  font-size: 16px;
}

.mini-cart__empty-subtitle {
  margin-bottom: 28px;
}

.mini-cart__notification {
  margin: 0;
  padding: 10px 20px;
  background: #30a711;
  font-size: 15px;
  color: #ffffff;
}

.mini-cart__items {
  list-style: none;
  margin: 0;
  position: absolute;
  top: 0;
  left: 0;
  padding: 0 0 92px 0;
  height: 100%;
  width: 100%;
  overflow-y: auto;
  -ms-overflow-style: none;
  -webkit-overflow-scrolling: touch;
}

.mini-cart__remove {
  position: absolute;
  top: -10px;
  left: -10px;
  text-align: center;
}

.mini-cart__item {
  overflow: hidden;
  padding: 18px 20px;
  border-bottom: 1px solid $border-color;

  &:last-child {
    border-bottom: 0;
  }

  .product-meta {
    display: inline-block;
  }

  .product-meta__title {
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

@media (max-width: 330px) {
  .mini-cart__item .product-meta__title {
    max-width: 160px;
  }
}

.mini-cart__image-container {
  display: inline-block;
  position: relative;
  float: left;
  margin-right: 20px;
}

.mini-cart__image {
  max-width: 85px;
  vertical-align: middle;
}

.mini-cart__footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  background: $secondary-background;
  -webkit-transform: translateZ(0);
  transform: translateZ(0); /* Fix a bug on Safari iOS */

  .button {
    border-radius: 0;
  }
}

.mini-cart__total {
  padding: 12px 20px;
  background: $background;
  border-top: 1px solid $border-color;
}

.mini-cart__total-label {
  text-transform: uppercase;
  font-size: 14px;
}

.mini-cart__total-price {
  float: right;
  font-size: 15px;
  font-weight: 700;
  color: $heading-color;
}

@include av-mq('handheld-and-up') {
  .mini-cart {
    width: 470px;
    max-width: none;
    height: auto;
  }

  .mini-cart__content {
    height: auto;
  }

  .mini-cart__empty {
    position: relative;
    margin: 30px 0 35px 0;
    left: 0;
    top: 0;
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
  }

  .mini-cart__empty-title {
    margin-bottom: 30px;
    font-size: 20px;
  }

  .mini-cart__empty-subtitle {
    margin-bottom: 36px;
  }

  .mini-cart__notification {
    padding: 22px 25px;
    font-size: 16px;
  }

  .mini-cart__total {
    padding: 16px 26px;
  }

  .mini-cart__items,
  .mini-cart__footer {
    position: relative;
    padding: 0;
  }

  .mini-cart__image-container {
    margin-right: 30px;
  }

  .mini-cart__image {
    max-width: 62px;
  }

  .mini-cart__items {
    max-height: 350px;
  }

  .mini-cart__item {
    padding: 24px 25px;

    .product-meta {
      position: relative;
      float: right;
      width: 77%;
    }

    .product-meta__price,
    .product-meta__title {
      font-size: 16px;
      margin-bottom: 0;
    }

    .product-meta__price {
      position: absolute;
      bottom: 3px;
      right: 0;
    }

    .product-meta__title {
      max-width: 200px;
    }
  }
}

@include av-mq('lap-and-up') {
  .mini-cart__footer .button {
    padding-top: 20px;
    padding-bottom: 20px;
  }
}
/**
 * FILTERS
 */

.collection__filters {
  padding: 20px;
  background: $secondary-button-background;
  overflow: hidden;
}

.collection-filter {
  border-radius: 2px;
  color: $secondary-button-background;
}

.collection-filter > select {
  color: $secondary-button-background;
  background: $secondary-button-color;
  border: none;
}

.collection-filter + .collection-filter {
  margin-top: 10px;
}

@include av-mq('desk') {
  .collection__filters {
    text-align: left;
  }

  .collection-filter {
    display: inline-block;
    margin-top: 0 !important;
    margin-right: 16px;
    max-width: 190px;

    &:last-child {
      margin-right: 0;
    }
  }

  .collection-filter--sorter {
    float: right;
  }
}

@include av-mq('widescreen') {
  .collection-filter {
    max-width: 250px;
  }
}

@include av-mq('large-widescreen') {
  .collection-filter {
    max-width: 300px;
  }
}

/**
 * GRID COLLECTION ITEMS
 */

.collection--grid.grid--flush {
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  border-top: 1px solid $border-color;

  .product-item {
    border-bottom: 1px solid $border-color;
    background: $background;
  }

  .product-item__inner {
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
  }

  .product-item__image {
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex: 1 0 auto;
    -ms-flex: 1 0 auto;
    flex: 1 0 auto;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
  }

  .product-item__image-wrapper {
    -webkit-flex-shrink: 1;
    -ms-flex-negative: 1;
    flex-shrink: 1;
    min-width: 1px;
  }

  .product-item__image-wrapper img {
    max-width: 100%;
    padding-top: 0;
  }
}

.collection--grid.collection--grid-no-border {
  border-top: none;
}

.product-item__inner {
  position: relative;
  background: $background;
  height: 100%;
}

.product-item__labels {
  position: absolute;
  z-index: 1;
  top: 5px;
  left: 0;
}

.product-item__label {
  margin-bottom: 4px;
}

.product-item__label .label {
  border-radius: 0 2px 2px 0;
}

.section--secondary .product-item__label .label--quick-shop,
.template-collection .product-item__label .label--quick-shop {
  border-left: 0;
}

.product-item__label--hidden {
  display: none;
}

.product-item__image {
  display: block;
  font-size: 0;
  transition: opacity 0.2s ease-in-out;
  text-align: center;

  img {
    padding: 5px 5px 10px 5px;
  }
}

.no-touchevents .product-item__image:hover,
.product-item--sold-out .product-item__image {
  opacity: 0.7;
}

.product-item__info {
  padding: 0 10px 10px 10px;
  font-size: 13px;
  text-align: center;
}

.product-item__vendor,
.product-item__price {
  font-weight: 700;
  color: $heading-color;
}

.product-item__vendor {
  margin-bottom: 6px;
  text-transform: uppercase;
}

.product-item__title {
  margin-bottom: 8px;
}

.product-item__price--new {
  margin-right: 10px;
  color: $accent-color;
}

.product-item__price--old {
  text-decoration: line-through;
}

@include av-mq('thumb') {
  .collection--grid.grid--flush .product-item {
    border-right: 1px solid $border-color;

    &:nth-child(#{$collection-mobile-grid-items-per-row}n) {
      border-right: none;
    }

    &:nth-child(#{$collection-mobile-grid-items-per-row}n+1):nth-last-child(-n+#{$collection-mobile-grid-items-per-row}),
    &:nth-child(#{$collection-mobile-grid-items-per-row}n+1):nth-last-child(-n+#{$collection-mobile-grid-items-per-row}) ~ .product-item {
      border-bottom: none;
    }
  }
}

@include av-mq('handheld') {
  .collection--grid.grid--flush .product-item {
    border-right: 1px solid $border-color;

    &:nth-child(#{$collection-tablet-grid-items-per-row}n) {
      border-right: none;
    }

    &:nth-child(#{$collection-tablet-grid-items-per-row}n+1):nth-last-child(-n+#{$collection-tablet-grid-items-per-row}),
    &:nth-child(#{$collection-tablet-grid-items-per-row}n+1):nth-last-child(-n+#{$collection-tablet-grid-items-per-row}) ~ .product-item {
      border-bottom: none;
    }
  }
}

@include av-mq('handheld-and-up') {
  .product-item__labels {
    top: 20px;
  }

  .product-item__image img {
    padding: 20px;
  }

  .product-item__info {
    padding: 0 10px 30px 10px;
    font-size: 14px;
  }
}

@include av-mq('lap-and-up') {
  .collection--grid.grid--flush .product-item {
    border-right: 1px solid $border-color;
  }

  .collection--grid.grid--flush .product-item.\31\/2--lap-and-up {
    &:nth-child(2n) {
      border-right: none;
    }

    &:nth-child(2n+1):nth-last-child(-n+2),
    &:nth-child(2n+1):nth-last-child(-n+2) ~ .product-item {
      border-bottom: none;
    }
  }

  .collection--grid.grid--flush .product-item.\31\/3--lap-and-up {
    &:nth-child(3n) {
      border-right: none;
    }

    &:nth-child(3n+1):nth-last-child(-n+3),
    &:nth-child(3n+1):nth-last-child(-n+3) ~ .product-item {
      border-bottom: none;
    }
  }

  .collection--grid.grid--flush .product-item.\31\/4--lap-and-up {
    &:nth-child(4n) {
      border-right: none;
    }

    &:nth-child(4n+1):nth-last-child(-n+4),
    &:nth-child(4n+1):nth-last-child(-n+4) ~ .product-item {
      border-bottom: none;
    }
  }

  .collection--grid.grid--flush .product-item.\31\/5--lap-and-up {
    &:nth-child(5n) {
      border-right: none;
    }

    &:nth-child(5n+1):nth-last-child(-n+5),
    &:nth-child(5n+1):nth-last-child(-n+5) ~ .product-item {
      border-bottom: none;
    }
  }

  .collection--grid.grid--flush .product-item.\31\/6--lap-and-up {
    &:nth-child(6n) {
      border-right: none;
    }

    &:nth-child(6n+1):nth-last-child(-n+6),
    &:nth-child(6n+1):nth-last-child(-n+6) ~ .product-item {
      border-bottom: none;
    }
  }
}


/**
 * COLLAGE COLLECTION ITEMS
 */

.product-tile:not(.ftg-loaded) {
  pointer-events: none;
}

.product-tile__link {
  display: block;
}

.product-tile__quick-shop {
  display: none;
}

@include av-mq('handheld-and-up') {
  .product-tile__overlay {
    position: absolute;
    display: table;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    opacity: 0;
    transition: opacity 0.25s ease-in-out;
    text-align: center;
    will-change: opacity;
  }

  .product-tile__overlay .product-meta {
    display: table-cell;
    vertical-align: middle;
    width: 100%;
    height: 100%;
  }

  .product-tile:hover .product-tile__overlay,
  .product-tile:hover .product-tile__quick-shop {
    opacity: 1;
  }
}

@include av-mq('desk') {
  .product-tile__quick-shop {
    display: block;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    border-radius: 0;
    opacity: 0; /* This allow to solve an issue on IE9-10 where opacity is not properly inherited */
    transition: opacity 0.25s ease-in-out;

    svg {
      width: 22px;
      height: 22px;
      vertical-align: bottom;
      margin-right: 15px;
    }
  }
}
.faq__item {
  padding: 30px 15px 32px 15px;
  border-bottom: 1px solid $border-color;

  &:last-child {
    border-bottom: none;
  }
}

.faq__question {
  margin-bottom: 0;
  font-weight: 700;
  font-family: $heading-font;
  color: $heading-color;
  text-align: center;
  cursor: pointer;
}

.faq__answer {
  display: none;
  max-width: 600px;
  margin: 22px auto 0 auto;
}

@include av-mq('handheld-and-up') {
  .faq__question {
    font-size: 24px;
  }

  .faq__answer {
    margin-top: 32px;
  }
}
/**
 * ----------------------------------------------------------------------------------------------------
 * FOOTER
 * ----------------------------------------------------------------------------------------------------
 */

.footer {
  font-size: 12px;
  color: $header-color;
  background: $header-background;
  border-top: 1px solid $header-border-color;

  ul {
    list-style: none;
    padding: 0;
  }

  li:not(:last-child) {
    margin-bottom: 8px;
  }

  a:hover {
    color: $header-accent-color;
  }
}

.footer__module {
  margin: 32px 0 34px 0;

  &:last-child {
    margin-bottom: 0;
  }
}

.footer__title {
  margin: 0 0 18px 0;
  text-transform: uppercase;
  font-weight: 700;
  color: darken($header-color, 15%);
}

.footer__phone,
.footer__email {
  margin: 8px 0;
}

.footer__social svg {
  margin-right: 14px;
  width: 14px;
  height: 14px;
  vertical-align: sub;
}

.footer__newsletter-form {
  position: relative;
  margin-top: 22px;

  .footer__newsletter-input {
    padding: 10px 10px;
    height: 38px;
    font-size: 13px;
    border: 1px solid $header-border-color;
    background: $header-background;
    transition: border 0.25s ease-in-out;

    &:focus {
      color: $header-color;
    }

    &::-webkit-input-placeholder {
      color: rgba($header-color, 0.6);
    }

    &::-moz-placeholder {
      color: rgba($header-color, 0.6);
    }

    &:-ms-input-placeholder {
      color: rgba($header-color, 0.6);
    }

    &::placeholder {
      color: rgba($header-color, 0.6);
    }
  }

  .footer__newsletter-submit {
    position: absolute;
    padding: 9px 20px 9px 20px;
    width: auto;
    height: 32px;
    right: 3px;
    top: 3px;
    color: $header-background;
    background: $header-color;
    font-size: 11px;

    &:hover {
      background: $header-accent-color;
    }
  }
}

.footer__misc {
  margin-top: 38px;
  padding: 20px 0;
  border-top: 1px solid $header-border-color;
  text-align: center;
}

.footer__copyright {
  display: inline-block;
  margin: 0;
}

.footer__payment-methods {
  display: block;
  list-style: none;
  margin: 15px 0 0 0;
  padding: 0 0 10px 0;
  font-size: 26px;
  color: lighten($header-color, 10%);
}

.footer__payment-method {
  display: inline-block;
  margin: 0 15px 0 0 !important;

  &:last-child {
    margin-right: 0 !important;
  }
}

@include av-mq('handheld') {
  .footer__wrapper {
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
  }

  .footer__module {
    width: 33.33333%;
  }
}

@include av-mq('handheld-and-up') {
  .footer {
    font-size: 13px;
  }

  .footer__title {
    font-size: 14px;
  }

  .footer__social svg {
    margin-right: 14px;
    width: 16px;
    height: 16px;
    vertical-align: sub;
  }

  .footer__misc {
    text-align: left;
  }

  .footer__payment-methods {
    float: right;
    margin-top: -6px;
    padding-bottom: 16px;

    svg {
      width: 32px;
      height: 32px;
    }
  }
}

@include av-mq('lap-and-up') {
  .footer__wrapper {
    display: table;
    width: 100%;
    margin: 42px 0;
  }

  .footer__module {
    display: table-cell;
    width: auto;
    margin-top: 0;
    padding-right: 25px;
    vertical-align: top;

    &:last-child {
      padding-right: 0;
    }
  }
}

/**
 * BACK TO TOP BUTTON
 */

.touchevents .back-to-top {
  display: none;
}

.back-to-top {
  position: fixed;
  display: block;
  height: 50px;
  width: 50px;
  bottom: 30px;
  right: 25px;
  line-height: 46px;
  color: $text-color;
  background: $background;
  border-radius: 100%;
  z-index: 5;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease-in-out;
  text-align: center;
  box-shadow: 0 1px 1px rgba($text-color, 0.6);

  &:hover {
    color: $background;
    background: $accent-color;
  }
}

.back-to-top--active {
  opacity: 1;
  visibility: visible;
}

// When we have the no-scroll class set on the body this means we have the overlay active, and we want to remove it
.no-scroll .back-to-top {
  opacity: 0;
}
.form--shrinked {
  max-width: 400px;
  margin: 0 auto;
}

input,
textarea {
  width: 100%;
  padding: 14px 15px 15px 15px;
  color: inherit;
  border: 1px solid $border-color;
  border-radius: 2px;
  font-size: 14px;
  background: $background;
  -webkit-appearance: none; /* Remove the inner box-shadow in iOS devices */

  &::-webkit-input-placeholder {
    color: lighten($text-color, 10%);
  }

  &::-moz-placeholder {
    color: lighten($text-color, 10%);
  }

  &:-ms-input-placeholder {
    color: lighten($text-color, 10%);
  }

  &::placeholder {
    color: lighten($text-color, 10%);
  }
}

input[type="checkbox"] {
  width: 1em;
  height: 1em;
  margin-right: 15px;
  vertical-align: -2px;
  -webkit-appearance: checkbox;
}

textarea {
  display: block; /* Fix in inconsistency with Chrome/Safari */
  resize: vertical;
}

input:focus,
textarea:focus {
  outline: none;
  border-color: $accent-color;
}

.form__control {
  position: relative;
  margin-bottom: 30px;

  &:last-child {
    margin-bottom: 0;
  }
}

.form__label {
  display: block;
  margin-bottom: 12px;
  font-size: 14px;
  text-transform: uppercase;
}

/**
 * Style selects (this only works in IE10+, FF and Webkit browsers)
 */

.styled-select {
  position: relative;
}

.styled-select svg {
  position: absolute;
  top: calc(50% - 5px);
  right: 24px;
  width: 12px;
  height: 12px;
  line-height: normal;
  pointer-events: none;
  fill: currentColor;
}

select {
  /* Disable built-in styles */
  -webkit-appearance: none;
  -moz-appearance: none;

  display: inline-block;
  width: 100%;
  height: 48px;
  padding-left: 24px;
  padding-right: 65px;
  border: 1px solid $border-color;
  border-radius: 2px;
  background: $background;
  color: inherit;
  font-size: 14px;
  cursor: pointer;
  text-transform: uppercase;
  line-height: normal;

  &:active, &:focus {
    border-color: $accent-color;
    outline: none;
  }

  /* Remove the ugly blue background on IE when a value is selected */
  &:focus::-ms-value {
    background: $background;
    color: $text-color;
  }
}

/* Allow to remove the ugly dashes when selecting an option in Firefox */
select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 $text-color;
}

/* Disable default styles for IE10+ */
select::-ms-expand {
  display: none;
}

@include av-mq('handheld-and-up') {
  .form__control {
    margin-bottom: 40px;
  }
}
.gift-card {
  text-align: center;
}

.gift-card__illustration {
  margin-bottom: 20px;

  img {
    width: 320px;
    max-width: 100%;
  }
}

.gift-card__issued-text {
  font-size: 16px;
  color: $heading-color;
  font-weight: 700;
}

.gift-card__amount {
  display: block;
  margin: 22px 0 25px 0;
  font-size: 20px;
  font-weight: 700;
  color: $primary-button-background;
}

.gift-card__instructions {
  font-size: 14px;
}

.gift-card__code {
  display: inline-block;
  margin: 13px 0 32px 0;
  padding: 15px 65px 15px 65px;
  border: 1px solid $border-color;
  border-radius: 2px;
  font-size: 14px;
}

.gift-card__qr-code {
  margin-bottom: 32px;

  img {
    margin: 0 auto;
  }
}

.gift-card__apple-wallet {
  display: block;
  margin-bottom: 25px;
}

@include av-mq('handheld-and-up') {
  .gift-card__illustration {
    margin-bottom: 35px;
  }

  .gift-card__issued-text {
    font-size: 20px;
  }

  .gift-card__amount {
    margin-bottom: 50px;
    font-size: 30px;
  }

  .gift-card__instructions {
    font-size: 16px;
  }
}
.contact__form {
  margin-top: 25px;
}

#contact-us-map {
  height: 320px;
  border-top: 1px solid $border-color;
}

.gmap-infobubble {
  overflow: hidden !important;
  height: auto !important;
  width: auto !important;
  white-space: nowrap;
  font-size: 14px;
  box-shadow: 0 0 2px 1px rgba(0, 0, 0, 0.2), 0 3px $primary-button-background inset;
}

.gmap-company-name {
  display: block;
  padding-bottom: 20px;
  font-weight: 700;
  font-size: 16px;
  color: $heading-color;
  font-family: $heading-font;
}

.gmap-company-street,
.gmap-company-city,
.gmap-company-country {
  display: block;
  padding-bottom: 6px;
  font-family: $text-font;
}

.gmap-infobubble {
  span:last-child {
    padding-bottom: 0;
  }
}

.gmap-arrow {
  & > div {
    -webkit-filter: drop-shadow(-1px 1px 1px rgba(0, 0, 0, 0.2));
    filter: drop-shadow(-1px 1px 1px rgba(0, 0, 0, 0.2));
  }
}

@include av-mq('handheld-and-up') {
  .contact__form {
    margin-top: 0;
  }

  #contact-us-map {
    width: 100%;
    height: 550px;
    clear: both;
  }

  .gmap-company-name {
    font-size: 20px;
  }

  .gmap-infobubble {
    padding: 30px 30px 26px 30px !important;
    font-size: 16px;
  }
}
/**
 * HEADER
 */

#shopify-section-header {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 2;
  border-top: none;
}

.header {
  width: 100%;
  background: $header-background;
  color: $header-color;
  padding: 0;
  border-bottom: 1px solid $header-border-color;
  box-shadow: 0 1px rgba($header-border-color, 0.3);

  .header__link:hover,
  .header__item-social a:hover {
    color: $header-accent-color;
  }
}

.header__wrapper {
  position: relative;
  display: table;
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  height: 70px;
  overflow: hidden;
  z-index: 1;
}

.header__item {
  position: relative;
  display: table-cell;
  height: 100%;
  vertical-align: middle;
  background-clip: padding-box !important;
  border-right: 1px solid $header-border-color;
  border-left: 1px solid $header-border-color;
  text-align: center;
  font-size: 25px;
  line-height: 0;
  background: $header-background;
  -webkit-transform-style: preserve-3d;

  &:first-child {
    border-left: 0;
  }

  &:last-child {
    border-right: 0;
  }
}

.header__link {
  position: relative;
  display: block;
  cursor: pointer;
  width: 100%;
  height: 100%;
  transition: all 0.25s ease-in-out;

  & > svg {
    display: block;
    margin: 0 auto;
  }

  & > svg,
  & > .cart-icon-wrapper {
    position: relative;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
  }
}

.header__logo--centered {
  text-align: center;
}

.header__item-logo {
  padding: 0 15px;
  border-right: none;
  vertical-align: middle;
}

.header__logo-image {
  vertical-align: middle;
}

.header__logo-text {
  font-size: 15px;
  font-weight: 700;
  line-height: 1;
}

.header__item-toggle,
.header__item-account,
.header__item-search,
.header__item-cart {
  width: 75px;
}

.header__item-social,
.header__item-currency,
.header__item-account,
.header__item-search {
  display: none;
}

@include av-mq('handheld-and-up') {
  .header__item-logo {
    padding-left: 25px;
    text-align: left;
  }

  .header__logo-text {
    font-size: 20px;
  }

  .header__item-search {
    transition: all 0.25s ease-in-out;
  }

  .header__item-search,
  .header__item-account,
  .header__item-cart {
    display: table-cell;
    vertical-align: middle;
  }

  .header__wrapper--search {
    overflow: visible;
  }

  .header__search-wrapper {
    position: absolute;
    padding: 0 25px;
    top: 0;
    left: 0;
    width: 525px;
    height: 100%;
    background: $header-background;
    opacity: 0;
    visibility: hidden;
    border-left: 1px solid $header-border-color;
    border-right: 1px solid $header-border-color;
    transition: left 0.25s ease-in-out, opacity 0.25s ease-in-out 0.06s, visibility 0.25s ease-in-out;
    text-align: left;

    .header__link {
      display: inline-block;
      width: auto;
    }
  }

  .header__search-form {
    display: inline-block;
    height: 100%;
    vertical-align: bottom;

    .search__input {
      height: 100%;
      width: 395px;
      background: transparent;
      border: none;
      font-size: 18px;
      color: $header-color;

      &:focus {
        color: $header-accent-color;
      }

      &::-webkit-input-placeholder {
        color: $header-color;
      }

      &::-moz-placeholder {
        color: $header-color;
      }

      &:-ms-input-placeholder {
        color: $header-color;
      }

      &::placeholder {
        color: $header-color;
      }
    }
  }

  .header__search-close {
    float: right;
  }

  .header__item-search--expanded {
    .header__search-wrapper {
      opacity: 1;
    }

    .header__link:first-child {
      color: $header-accent-color;
    }

    .header__search-wrapper {
      /* 450px is the size of the search bar (525px) minus the size of the element in the nav (75px). Note that we
         cannot use transform (and hence better performance) because of IE that leaves empty space and screw up everything */
      left: -450px;
      visibility: visible;
    }
  }
}

@include av-mq('lap-and-up') {
  .header__item-currency {
    display: table-cell;
    vertical-align: middle;
  }

  .header__item-currency {
    width: 105px;

    &:hover .currency-selector,
    &:hover .currency-selector__select {
      color: $header-accent-color;
    }
  }

  .currency-selector,
  .currency-selector__select {
    transition: color 0.25s ease-in-out;
  }

  .currency-selector__select {
    padding-right: 40px;
    border: none;
    background: transparent;
    color: $header-color;
  }

  .header--with-horizontal-nav {
    border-bottom-color: $border-color;
    box-shadow: none;

    .header__item-toggle {
      display: none;
    }

    .header__item-logo {
      border-left: none;
    }
  }
}

@include av-mq('desk') {
  .header__item-social {
    display: table-cell;
    padding-right: 30px;
    border-left: none;
    text-align: right;
    font-size: 20px;
  }
}
.label {
  display: inline-block;
  padding: 2px 12px;
  font-size: 11px;
  text-transform: uppercase;
  border-radius: 2px;
}

.label + .label {
  margin-left: 5px;
}

.label--sold-out,
.label--unavailable {
  color: dark-or-light-color($product-label-sold-out-background);
  background: $product-label-sold-out-background;
}

.label--on-sale {
  color: dark-or-light-color($product-label-on-sale-background);
  background: $product-label-on-sale-background;
  display: none;
}

.label--custom {
  color: dark-or-light-color($accent-color);
  background: $accent-color;
}

.label--quick-shop {
  border: 1px solid $border-color;
  transition: all 0.2s ease-in-out;
  background: $background;

  svg {
    margin-right: 8px;
    width: 20px;
    height: 20px;
    vertical-align: text-bottom;
  }

  &:hover {
    color: $accent-color;
    border-color: $accent-color;
  }
}

@include av-mq('handheld-and-up') {
  .label {
    padding: 4px 15px;
    font-size: 13px;
  }
}
.list-collections--padded {
  padding: 25px 0;
}

.collection__link {
  position: relative;
  display: block;
  overflow: hidden;
  height: 100%;
  text-align: center;
  font-size: 0;
}

.collection__image {
  max-width: 100%;
  width: 100%;
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  transition: -webkit-transform 0.4s ease-in-out;
  transition: transform 0.4s ease-in-out;
  will-change: transform;
}

.collection__overlay {
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  opacity: 0;
  background: rgba(#000000, 0);
  transition: background 0.25s ease-in-out, opacity 0.25s ease-in-out;
  z-index: 1;
}

.collection__title {
  text-align: center;
  color: #ffffff;
  font-size: 16px;
  font-weight: 700;
}

.no-touchevents .collection__link:hover .collection__image {
  -webkit-transform: scale(1.2);
  -ms-transform: scale(1.2);
  transform: scale(1.2);
}

.collection__link:hover .collection__overlay,
.touchevents .collection__overlay {
  background: rgba(#000000, 0.3);
  opacity: 1;
}

@include av-mq('handheld-and-up') {
  .collection__title {
    font-size: 20px;
  }
}
.modal {
  position: relative;
  margin: 20px 0;
  width: 90%;
  max-width: 550px;
  padding: 25px;
  background: $background;
  text-align: left;
}

.modal--large {
  max-width: 960px;
}

.modal--centered {
  text-align: center;
}

.modal__close {
  position: absolute;
  top: 0;
  right: 0;
  padding: 13px;
  line-height: 0;
  background: $secondary-background;

  &:hover {
    color: $accent-color;
  }
}

.modal__title {
  margin-top: 10px;
  font-family: $heading-font;
  color: $heading-color;
  font-size: 17px;
  font-weight: 700;
  text-align: center;
}

.modal__newsletter {
  margin-top: 40px;

  input[type="email"] {
    text-transform: uppercase;
    margin-bottom: 20px;
  }
}

@include av-mq('handheld-and-up') {
  .modal {
    padding: 42px 50px 50px 50px;
  }

  .modal__title {
    margin: 0 0 35px 0;
    font-size: 26px;
  }

  .modal__newsletter {
    input {
      display: inline-block;
      width: auto;
      margin: 0;
    }

    input[type="email"] {
      margin: 0 20px 0 0;
    }
  }
}
/* If enabled, the horizontal navigation only display on larger screen */
.navigation-horizontal {
  display: none;
}

@include av-mq('lap-and-up') {
  .navigation-horizontal {
    display: block;
    background: $secondary-background;
    color: $text-color;
    border-top: 1px solid $border-color;
    text-align: center;
  }

  .navigation-horizontal__list,
  .navigation-horizontal__dropdown,
  .dropdown-column__list {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  .navigation-horizontal__list-item {
    position: relative;
    display: inline-block;

    .icon-dropdown-arrow {
      margin-left: 8px;
      vertical-align: middle;
      width: 12px;
      height: 12px;
    }
  }

  .navigation-horizontal__dropdown-item {
    .icon-dropdown-arrow-right {
      position: absolute;
      margin-top: 5px;
      right: 28px;
      width: 12px;
      height: 12px;
    }
  }

  .navigation-horizontal__list-link,
  .navigation-horizontal__dropdown-link {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  .navigation-horizontal__list-link {
    display: inline-block;
    padding: 20px 25px;
    transition: all 0.15s ease-in-out;
  }

  .navigation-horizontal__dropdown-item {
    position: relative;
  }

  .navigation-horizontal__dropdown-link {
    display: block;
    width: 100%;
    padding: 6px 30px;
    white-space: nowrap;
  }

  .navigation-horizontal__list-link:hover,
  .navigation-horizontal__dropdown-link:hover,
  .navigation-horizontal__dropdown-item--active > .navigation-horizontal__dropdown-link {
    color: $accent-color;
  }

  .navigation-horizontal__list-item--active > .navigation-horizontal__list-link {
    background: $background;
    color: $accent-color;
  }

  .navigation-horizontal__dropdown {
    visibility: hidden;
    position: absolute;
    top: 100%;
    left: -1px;
    padding: 30px 0 20px 0;
    background: $background;
    border: 1px solid $border-color;
    border-top: none;
    font-size: 14px;
    text-align: left;
    opacity: 0;
    transition: opacity 0.15s ease-in-out, visibility ease-in-out 0.15s;
    z-index: 1000;
  }

  .navigation-horizontal__dropdown .navigation-horizontal__dropdown {
    padding-top: 20px;
    left: 100%;
    top: -21px;
    border-top: 1px solid $border-color;
    background: $secondary-background;
  }

  .navigation-horizontal__dropdown-item--expandable > .navigation-horizontal__dropdown-link {
    margin-right: 26px;
  }

  .navigation-horizontal__list-item--active > .navigation-horizontal__list-link + .navigation-horizontal__dropdown,
  .navigation-horizontal__dropdown-item--active > .navigation-horizontal__dropdown-link + .navigation-horizontal__dropdown {
    visibility: visible;
    opacity: 1;
  }

  .navigation-horizontal__dropdown--mega {
    left: -200px;
    padding: 35px 30px;
    white-space: nowrap;
    border-bottom: 5px solid $accent-color;
  }

  .navigation-horizontal__dropdown--mega-with-image {
    padding: 40px 30px 35px 50px;
  }

  .navigation-horizontal__image-container {
    display: inline-block;
    margin-right: 15px;
    width: 150px;
    font-size: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  .navigation-horizontal__image {
    max-width: 150px;
    max-height: 150px;
    font-size: 0;
  }

  .dropdown-column {
    display: inline-block;
    padding: 0 20px;
    vertical-align: top;
  }

  .navigation-horizontal__dropdown--mega-with-image .dropdown-column {
    margin-top: 10px;
  }

  .dropdown-column__title {
    display: block;
    margin: 0 0 8px 0;
    color: $heading-color;
    font-family: $heading-font;
    font-weight: 700;
    font-size: 14px;
    line-height: 14px;
    text-transform: uppercase;
  }

  .dropdown-column__list-link {
    display: block;
    padding: 6px 0;

    &:hover {
      color: $accent-color;
    }
  }
}

@include av-mq('desk') {
  .navigation-horizontal__list-link {
    display: inline-block;
    padding: 20px 34px;
  }

  .navigation-horizontal__dropdown--mega {
    padding: 45px 40px;
  }


  .navigation-horizontal__dropdown--mega-with-image {
    padding: 50px 40px 45px 60px;
  }

  .navigation-horizontal__image-container {
    width: 175px;
  }

  .navigation-horizontal__image {
    max-width: 175px;
    max-height: 175px;
  }

  .dropdown-column {
    padding: 0 30px;
  }
}
/**
 * SIDEBAR
 */

.navigation-sidebar {
  position: fixed;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  max-width: 390px;
  height: 100%;
  z-index: 15;
  overflow: hidden;
  background: $navigation-sidebar-background;
  color: $navigation-sidebar-color;
  font-size: 15px;
  -webkit-text-size-adjust: none;
  text-transform: uppercase;
  -webkit-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
  transform: translateX(-100%);
  transition: -webkit-transform 0.15s ease-in-out;
  transition: transform 0.15s ease-in-out;
}

.navigation-sidebar--open {
  -webkit-transform: translateX(0%);
  -ms-transform: translateX(0%);
  transform: translateX(0%);
}

.navigation-sidebar__header {
  display: table;
  position: relative;
  width: 100%;
  background: $navigation-sidebar-background;
  top: 0;
  z-index: 1;
  font-size: 22px;
  border-bottom: 1px solid $navigation-sidebar-border;
}

.navigation-sidebar__search--mobile {
  padding: 3px 25px;
  background: $navigation-sidebar-border;
  color: $navigation-sidebar-color;
  border-top: 1px solid darken($navigation-sidebar-border, 8%);
  box-shadow: 0 1px lighten($navigation-sidebar-border, 3%), 0 1px darken($navigation-sidebar-border, 3%) inset;

  .icon-search {
    opacity: 0.6;
  }
}

.navigation-sidebar__search-form {
  display: inline-block;

  .search__input {
    background: none;
    border: none;
    font-size: 15px;
    color: rgba($navigation-sidebar-color, 0.6);

    &::-webkit-input-placeholder {
      color: rgba($navigation-sidebar-color, 0.6);
    }

    &::-moz-placeholder {
      color: rgba($navigation-sidebar-color, 0.6);
    }

    &:-ms-input-placeholder {
      color: rgba($navigation-sidebar-color, 0.6);
    }

    &::placeholder {
      color: rgba($navigation-sidebar-color, 0.6);
    }
  }
}

.navigation-sidebar__top {
  display: table;

  a {
    display: table-cell;
    width: 75px;
    height: 100%;
    vertical-align: middle;
    text-align: center;
    line-height: 0;
  }
}

.navigation-sidebar__current {
  display: table-cell;
  padding-left: 28px;
  color: $navigation-sidebar-color;
  vertical-align: middle;
  font-size: 16px;
}

.navigation-sidebar__image {
  display: none;
}

.navigation-sidebar__lists {
  position: relative;
  -webkit-flex: 1 0 auto;
  -ms-flex: 1 0 auto;
  flex: 1 0 auto;
  width: 100%;

  .no-flexbox & {
    height: 100%;
  }

  .flexboxtweener & {
    height: auto;
  }
}

.navigation-sidebar__list {
  position: absolute;
  top: 0;
  padding: 0;
  margin: 0;
  width: 100%;
  height: 100%;
  list-style: none;
  background: $navigation-sidebar-background;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: -ms-autohiding-scrollbar;
  -webkit-transform: translateX(100%);
  -ms-transform: translateX(100%);
  transform: translateX(100%);
  transition: -webkit-transform 0.25s ease-in-out;
  transition: transform 0.25s ease-in-out;

  .icon-arrow-right {
    position: absolute;
    height: 18px;
    width: 18px;
    top: calc(50% - 9px);
    right: 25px;
    transition: -webkit-transform 0.25s ease-in-out;
    transition: transform 0.25s ease-in-out;
  }
}

.navigation-sidebar__list:not(.navigation-sidebar__list--active) {
  /* On recent versions of Chrome iOS, there is a strange bug that would prevent
     scroll. By hidding the list by default, we avoid that */
  display: none;

  .navigation-sidebar--open & {
    display: block
  }
}

.navigation-sidebar__list--active {
  -webkit-transform: translateX(0);
  -ms-transform: translateX(0);
  transform: translateX(0);
}

.navigation-sidebar__list--hidden {
  -webkit-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
  transform: translateX(-100%);
}

.navigation-sidebar__header + .navigation-sidebar__list {
  border-top: 1px solid $navigation-sidebar-border;
}

.navigation-sidebar__item--expandable > .navigation-sidebar__list {
  position: absolute;
  top: 0;
  left: 0;
}

.navigation-sidebar__item {
  border-bottom: 1px solid $navigation-sidebar-border;
  transition: background 0.25s ease-in-out;

  &:last-child {
    margin-bottom: 70px; /* This extra margin on last element allows to add extra scrollable area */
  }
}

.navigation-sidebar__link {
  position: relative;
  display: block;
  padding: 18px 25px;

  &:hover {
    .icon-arrow-right {
      -webkit-transform: translateX(5px);
      -ms-transform: translateX(5px);
      transform: translateX(5px);
    }
  }
}

.navigation-sidebar__current-title,
.navigation-sidebar__link-title {
  display: block;
  max-width: 250px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.navigation-sidebar__current-title {
  max-width: 200px;
}

@include av-mq('handheld-and-up') {
  .navigation-sidebar {
    max-width: 325px;
    font-size: 17px;
  }

  .navigation-sidebar__current {
    font-size: 20px;
  }

  .navigation-sidebar__search--mobile {
    display: none;
  }

  .navigation-sidebar__item:hover {
    background: $navigation-sidebar-border;
    color: $navigation-sidebar-color;
  }

  .navigation-sidebar__link {
    padding: 20px 25px;
  }
}

@media (screen and min-height: 800px) {
  .navigation-sidebar__image {
    display: table-row;
    width: 100%;
    text-align: center;
    font-size: 0;

    img {
      width: 100%;
      max-width: 100%;
    }
  }

  .no-flexbox .navigation-sidebar__image {
    display: none;
  }
}
.shopify-section__newsletter,
.shopify-section__newsletter + .shopify-section {
  border-top: none !important;
}

.newsletter {
  position: relative;
  padding: 35px 0 40px 0;
  background: $newsletter-background;
  text-align: center;
  overflow: hidden;
  clear: both;
  box-shadow: 0 1px $newsletter-background, 0 -1px $newsletter-background;
}

.newsletter__title {
  margin: 0 0 10px 0;
  color: $newsletter-color;
  font-size: 17px;
  font-weight: 700;
}

.newsletter__subtitle {
  margin-bottom: 30px;
  font-size: 14px;
  line-height: 2;

  @if (lightness($newsletter-background) < 30%) {
    color: lighten($newsletter-background, 40%);
  } @else {
    color: darken($newsletter-background, 20%);
  }
}

.newsletter__error {
  color: $newsletter-color;
}

.newsletter__form {
  position: relative;
  max-width: 460px;
  margin: 0 auto;
}

.newsletter__email {
  padding-right: 125px;
  border: none;
  text-transform: uppercase;

  @if (lightness($newsletter-background) < 30%) {
    background: lighten($newsletter-background, 40%);
  } @else {
    background: darken($newsletter-background, 12%);
  }

  &,
  &:focus {
    color: $newsletter-color;
  }

  &::-webkit-input-placeholder {
    color: lighten($newsletter-background, 10%);
  }

  &::-moz-placeholder {
    color: lighten($newsletter-background, 10%);
  }

  &:-ms-input-placeholder {
    color: lighten($newsletter-background, 10%);
  }

  &::placeholder {
    color: lighten($newsletter-background, 10%);
  }
}

.newsletter__submit {
  position: absolute;
  padding: 11px 14px 12px 14px;
  width: auto;
  top: 3px;
  right: 3px;
  background: $newsletter-background;
  color: $newsletter-color;

  &:hover {
    background: $newsletter-color;
    color: $newsletter-background;
  }
}

@include av-mq('handheld-and-up') {
  .newsletter {
    padding: 45px 0 48px 0;
  }

  .newsletter__title {
    margin-bottom: 32px;
    font-size: 20px;
  }

  .newsletter__subtitle {
    margin-bottom: 46px;
    font-size: 16px;
  }

  .newsletter__submit {
    padding-left: 30px;
    padding-right: 30px;
  }
}
.page__header {
  position: relative;
  background: $secondary-background;
  border-bottom: 1px solid $border-color;
}

.page__header--no-border {
  border-bottom: none;
}

.page__header-wrapper {
  padding: 30px 0;
  text-align: center;
}

.page__action {
  margin-top: 20px;
}

.page__header-actions {
  margin: 20px 0;
}

.page__title {
  margin: 0;
  font-size: 30px;
  color: $heading-color;
  font-family: $heading-font;
  font-weight: 700;
  line-height: 1.4;
}

.page__description {
  margin: 25px auto 0 auto;
  max-width: 600px;
  text-align: left;
}

.page__description--centered {
  text-align: center;
}

.page__content {
  padding: 50px 0;
}

.page__footer {
  padding: 20px 0;
  background: $secondary-background;
  border-top: 1px solid $border-color;
  text-align: center;
}

.page__footer-text {
  display: block;
  margin-top: 0;
  font-weight: 400;
}

.page__footer-title {
  margin: 0 0 20px 0;
  color: $heading-color;
}

@include av-mq('handheld-and-up') {
  .page__header-wrapper {
    padding: 60px 0;
  }

  .page__header-actions {
    margin: 40px 0 50px 0;
  }

  .page__title {
    font-size: 40px;
  }

  .page__description {
    margin-top: 40px;
  }

  .page__footer {
    padding: 34px 0;
    clear: both;
  }

  .page__footer-title {
    margin-bottom: 40px;
  }

  .page__footer-text {
    display: inline-block;
    margin: 0 35px 0 0;
    vertical-align: middle;
  }
}

@include av-mq('lap-and-up') {
  .page__header-actions {
    margin: 50px 0 120px 0;
  }

  .page__title {
    font-size: $page-title-font-size;
  }

  .page__description {
    margin-top: 50px;
  }
}
/**
 * ----------------------------------------------------------------------------------------------------
 * PAGINATION
 * ----------------------------------------------------------------------------------------------------
 */

.pagination {
  clear: both;
  width: 100%;
  background: $secondary-background;
  border-top: 1px solid $border-color;
}

.pagination__wrapper {
  position: relative;
  padding: 30px 0;
  text-align: center;
  overflow: hidden;
}

.pagination__current {
  margin: 25px 0;
  color: $secondary-button-background;
  font-size: 14px;
  text-transform: uppercase;
}

.pagination__label {
  display: none;
}

.pagination__nav--prev {
  float: left;
}

.pagination__nav--next {
  float: right;
}

.pagination__current {
  position: absolute;
  margin: 0;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

@include av-mq('thumb') {
  .pagination__nav {
    padding-left: 20px;
    padding-right: 20px;
  }
}

@include av-mq('handheld-and-up') {
  .pagination__label {
    display: inline-block;
  }

  .pagination__nav {
    padding-left: 30px;
    padding-right: 30px;

    svg {
      width: 15px;
      height: 15px;
      vertical-align: -2px;
      -webkit-transform: translateX(0);
      -ms-transform: translateX(0);
      transform: translateX(0);
      transition: -webkit-transform 0.1s ease-in-out;
      transition: transform 0.1s ease-in-out;
    }
  }

  .pagination__nav--prev {
    svg {
      margin-right: 15px;
    }

    &:hover svg {
      -webkit-transform: translateX(-5px);
      -ms-transform: translateX(-5px);
      transform: translateX(-5px);
    }
  }

  .pagination__nav--next {
    svg {
      margin-left: 15px;
    }

    &:hover svg {
      -webkit-transform: translateX(5px);
      -ms-transform: translateX(5px);
      transform: translateX(5px);
    }
  }
}
.password__header {
  padding: 35px 0 50px 0;
  text-align: center;
}

.password__title {
  color: $heading-color;
  font-family: $heading-font;
  font-size: 24px;
}

.password__admin {
  margin-top: 50px;
  padding: 25px;
  background: $secondary-background;
}

.password__admin-link {
  margin: 0;
  text-align: center;
  font-weight: 700;
}

.password__storefront-form {
  display: none;
  margin-top: 25px;
}

.password__storefront-form--opened {
  display: block;
}

.password__powered-by {
  margin: 25px 0;
  text-align: center;
  font-size: 12px;

  svg {
    margin-left: 15px;
    width: 90px;
    vertical-align: middle;
  }
}

@include av-mq('handheld-and-up') {
  .password__admin {
    margin-top: 100px;
  }

  .password__title {
    font-size: 42px;
  }
}
/**
 * GENERAL
 */

.product__details {
  margin-top: 30px;
}

/**
 * META
 */

.product-meta--desktop {
  display: none;
}

.product-meta--mobile {
  text-align: center;
}

.product-meta__vendor {
  text-transform: uppercase;
  font-size: 13px;
  font-weight: 400;
}

.product-meta__title {
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 700;
  line-height: 1.6;
  color: $heading-color;
}

.product-meta__price {
  display: inline-block;
  font-size: 16px;
  font-weight: 700;
  color: $accent-color;
}

.product-meta__price--new {
  padding-right: 15px;
  color: $accent-color;
}

.product-meta__price--old {
  text-decoration: line-through;
  font-weight: 400;
  color: $text-color;
}

.product-meta__labels:not(:empty) {
  margin-top: 24px;
}

@include av-mq('handheld-and-up') {
  .product-meta__vendor {
    font-size: 14px;
  }

  .product-meta__title,
  .product-meta__price {
    font-size: 20px;
  }

  .product-meta__price--old {
    font-size: 16px;
  }
}

@include av-mq('lap-and-up') {
  .product-meta--mobile {
    display: none;
  }

  .product-meta--desktop {
    display: block;
  }
}

/**
 * PRODUCT SLIDESHOW
 */

.product__slideshow {
  margin: 30px -20px 20px -20px; /* This allow to extend outside the parent container */
  padding: 0;
  list-style: none;

  .slick-prev,
  .slick-next {
    display: none !important;
  }

  .slick-dots {
    margin-top: 10px;
    padding: 0;
    text-align: center;

    li {
      display: inline-block;
      cursor: pointer;
    }

    .slick-active button {
      background: $primary-button-background;
    }

    button {
      display: inline-block;
      margin: 10px 8px;
      padding: 0;
      height: 8px;
      width: 8px;
      background: $text-color;
      border-radius: 100%;
      cursor: pointer;
      border: none;
      outline: none;
      font-size: 0;
    }
  }
}

.product__slideshow-image {
  max-width: 100%;
  margin: 0 auto;
}

.product__slideshow-slide {
  &.zooming .product__slideshow-image {
    visibility: hidden;
  }
}

.product_slideshow-placeholder-container {
  position: relative;
}

.product__slideshow-placeholder {
  width: 100%;
  cursor: pointer;
}

.product__video-wrapper {
  text-align: center;
  height: 0;
  padding-bottom: 56.25%;
  position: relative;

  iframe,
  embed,
  object {
    position: absolute;
    top: 0;
    left: 0;
    width: 100% !important;
    height: 100% !important;
  }
}

.product__thumbnails {
  display: none;
}

@include av-mq('lap-and-up') {
  .product__showcase {
    padding-right: 40px;
  }

  .product__slideshow {
    margin: 0;

    .slick-dots {
      display: none !important;
    }

    .slick-prev,
    .slick-next {
      position: absolute;
      display: block !important;
      top: calc(50% - 15px);
      cursor: pointer;
      font-size: 38px;
      transition: color 0.25s ease-in-out;

      &:hover {
        color: $accent-color;
      }
    }

    .slick-prev {
      left: 0;
    }

    .slick-next {
      right: 0;
    }

    .slick-list {
      margin: 0 60px;
    }
  }

  .product__thumbnails {
    display: block;
    margin: 25px 0;
    padding: 0;
    list-style: none;
    text-align: center;
  }

  .product__thumbnail {
    display: inline-block;
    margin: 5px;
    cursor: pointer;
    font-size: 0;
    vertical-align: middle;

    img {
      max-width: 85px;
    }
  }
}

/**
 * PRODUCT INFO
 */

.product__info {
  .product-meta,
  .product__description,
  .product__share,
  .product__form {
    margin-bottom: 45px;
  }
}

/**
 * PRODUCT FORM
 */

.product__variants {
  margin-bottom: 25px;
}

.product__variants .form__control {
  margin-bottom: 25px;
}

.option-selector__label {
  position: absolute;
  left: 24px;
  top: 0;
  font-size: 14px;
  line-height: 48px;
  text-transform: uppercase;
  color: $text-color;
  pointer-events: none;
}

.product__quantity {
  display: table;
  margin: 0 25px 26px 0;
  border: 1px solid $border-color;
  border-radius: 2px;

  label, input {
    display: table-cell;
  }

  input, input:active, input:focus {
    border: none;
  }

  label {
    padding: 0 25px;
    border-right: 1px solid $border-color;
    vertical-align: middle;
  }

  input {
    width: 56px;
    background: $secondary-background;
    text-align: center;
    -moz-appearance: textfield;
  }

  input[type="number"]::-webkit-outer-spin-button,
  input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
}

.flexbox {
  .product__buy {
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
  }

  .product__quantity {
    margin-bottom: 0;
  }

  .product__add-to-cart {
    -webkit-flex: 1 1 auto;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
  }
}


/**
 * GALLERY
 */

.product-collage-gallery {
  padding-top: 25px;
  padding-bottom: 25px;
}

/**
 * PRODUCT TABS
 */

.shopify-section__product-tabs {
  border-top: none !important;
}

/**
 * RELATED PRODUCTS
 */

.shopify-section__related-products {
  border-top: none !important;
}

.related-products__item {
  opacity: 1;
  transition: opacity 0.25s ease-in-out;

  &:hover {
    opacity: 0.7;
  }
}
.quick-shop {
  padding: 0;
}

.quick-shop__spinner {
  margin: 0 !important;
}

.quick-shop__slideshow {
  list-style: none;

  .slick-list {
    margin: 0;
  }

  .slick-dots {
    display: block !important;
    position: absolute;
    top: 0;
    left: 0;
    width: 50px;
    margin-top: 0;
    padding: 16px 0;
    background: $primary-button-background;
    text-align: center;

    li {
      display: block;
      margin: 0 8px;
      height: auto;
      width: auto;
      background: transparent;
    }

    button {
      display: block;
      margin: 0 auto;
      padding: 6px 0;
      width: auto;
      height: auto;
      background: transparent;
      color: transparent;
      border: none;
      font-size: 0;

      &:focus {
        outline: none;
      }

      &:before {
        display: block;
        content: '';
        width: 16px;
        height: 16px;
        border-radius: 100%;
        border: 3px solid $primary-button-background;
        background: $primary-button-color;
        transition: all 0.2s ease-in-out;
      }
    }

    .slick-active button:before {
      border: 2px solid $primary-button-color;
      background: $primary-button-background;
    }
  }
}

.quick-shop__info {
  padding: 25px 40px;
}

.quick-shop__short-description {
  margin-bottom: 20px;
}

.quick-shop__info .product-meta,
.quick-shop__view-details {
  display: block;
  margin-bottom: 40px;
}

.quick-shop__view-details svg {
  height: 12px;
  width: 12px;
  margin-left: 10px;
  vertical-align: -1px;
}

/**
 * Let's style the small stars first...
 */

@font-face {
  font-family: "icomoon";
  src: url(#{'{{ 'icomoon.eot' | asset_url }}'});
  src: url(#{'{{ 'icomoon.eot?#iefix' | asset_url }}'}) format('embedded-opentype'),
    url(#{'{{ 'icomoon.woff' | asset_url }}'}),
    url(#{'{{ 'icomoon.ttf' | asset_url }}'});
  font-weight: normal;
  font-style: normal;
}

.spr-icon-star,
.spr-icon-star-half-alt,
.spr-icon-star-empty,
.spr-icon-star-empty.spr-icon-star-hover,
.spr-icon-star.spr-icon-star-hover {
  margin-right: 5px !important;
  font-size: 16px !important;
  color: #f4d820 !important;
  opacity: 1 !important;

  &:last-child {
    margin-right: 0 !important;
  }
}

.spr-icon-star-empty {
  color: $text-color !important;
}

.spr-icon:before,
.spr-icon-star:before,
.spr-icon-star-empty.spr-icon-star-hover:before {
  font-family: "icomoon" !important;
  content: '\e613' !important;
}

/**
 * Little badge
 */

.product__reviews-badge {
  margin-top: 22px;
}

.spr-badge-caption {
  margin-left: 14px;
}

#shopify-product-reviews {
  margin: 0;

  .spr-container {
    padding: 6px 0 4px 0;
    border: none;
  }

  .spr-header-title {
    display: none;
  }

  .spr-summary-caption {
    float: left;
    font-size: 16px;
    color: $heading-color;
    font-weight: 700;
    margin-right: 18px;
  }

  .spr-summary-starrating {
    margin-top: -2px;
    float: right;
  }

  .spr-summary-actions {
    display: block;
    padding-top: 18px;
    clear: both;
    text-align: left;
    font-size: 14px;

    &:focus {
      outline: none;
    }
  }

  .spr-summary-actions-newreview {
    color: $accent-color;
    float: none;

    &:hover {
      color: darken($accent-color, 10%);
    }
  }

  .spr-form {
    border-top: 1px solid $border-color;
  }

  .spr-form-message-error {
    @extend .alert, .alert--error;
    margin-bottom: 30px;
  }

  .spr-form-title {
    display: none;
  }

  .spr-form-label {
    display: block;
    margin-bottom: 12px;
    font-size: 14px;
    text-transform: uppercase;
  }

  .spr-form-contact-name,
  .spr-form-contact-email,
  .spr-form-review-rating,
  .spr-form-review-title,
  .spr-form-review-body {
    margin-bottom: 30px;
  }

  .spr-form-actions {
    text-align: center;
  }

  .spr-form-message-success {
    font-size: 14px;
  }

  .spr-reviews {
    margin: 0;
    border-radius: 2px;
  }

  .spr-review {
    margin-bottom: 20px;
    padding: 10px 15px;
    border: 1px solid $border-color;
    background: $background;

    &:first-child {
      margin-top: 30px;
    }
  }

  .spr-review-header-starratings {
    float: right;
  }

  .spr-review-header-title {
    color: $heading-color;
    font-weight: 700;
    font-size: 15px;
  }

  .spr-review-header-byline,
  .spr-review-header-byline strong {
    text-transform: uppercase;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    opacity: 1;
  }

  .spr-review-content {
    margin: 15px 0;
  }

  .spr-review-content-body {
    line-height: inherit;
  }

  .spr-pagination {
    padding: 9px 0 0 0;
    border-top: none;
  }

  .spr-pagination-page {
    display: none;
  }

  .spr-pagination-prev,
  .spr-pagination-next {
    position: relative;
    color: $accent-color;
    font-size: 14px;

    &:hover {
      color: darken($accent-color, 10%);
    }
  }

  .spr-pagination-prev {
    float: left;
  }

  .spr-pagination-next {
    float: right;
  }

  .spr-review-reportreview:hover {
    color: darken($accent-color, 10%);
  }

  .button-primary {
    @extend .button--primary;
    float: none;
  }
}

@include av-mq('handheld-and-up') {
  #shopify-product-reviews {
    .spr-container {
      padding: 4px 0 0 0;
    }

    .spr-summary-caption {
      font-size: 20px;
    }

    .spr-summary-starrating {
      float: none;
      vertical-align: sub;
    }

    .spr-summary-actions {
      display: inline-block;
      float: right;
      clear: none;
      margin-top: 4px;
      padding: 0;
      font-size: 16px;
    }

    .spr-form {
      margin-top: 48px;
      padding-top: 45px;
    }

    .spr-form-contact-name,
    .spr-form-contact-email,
    .spr-form-review-rating,
    .spr-form-review-title,
    .spr-form-review-body {
      margin-bottom: 45px;
    }

    .spr-form-actions {
      margin: 4px 0;
    }

    .spr-form-message-error {
      margin-bottom: 40px;
    }

    .spr-form-message-success {
      font-size: 16px;
    }

    .spr-review {
      padding: 25px 30px;

      &:first-child {
        margin-top: 48px;
      }
    }

    .spr-review-header-title {
      margin-bottom: 12px;
      font-size: 16px;
    }

    .spr-review-header-byline,
    .spr-review-header-byline strong {
      font-size: 14px;
    }

    .spr-review-content {
      margin: 12px 0 15px 0;
    }

    .spr-review-content-body {
      font-size: 16px;
    }

    .spr-pagination {
      padding: 26px 0 0 0;
    }

    .spr-pagination-prev,
    .spr-pagination-next {
      font-size: 16px;
    }
  }
}
/**
 * SEARCH
 */

.search-results {
  padding: 25px 0;
}

.search__input {
  max-width: 400px;
  margin: 0 auto;
}

/**
 * AUTOCOMPLETE
 */

@-webkit-keyframes spinnerRotation {
  from {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes spinnerRotation {
  from {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.autocomplete {
  position: absolute;
  display: block;
  margin: 0;
  padding: 0;
  top: 100%;
  left: 0;
  width: 100% !important;
  z-index: 1;
}

.autocomplete__results,
.autocomplete__spinner {
  background: $secondary-background;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);
  border-top: 1px solid $header-border-color;
}

.autocomplete__spinner {
  display: block;
  padding: 56px 0;
  left: 0;
  top: 0;
  text-align: center;

  svg {
    -webkit-animation: spinnerRotation 1s linear infinite;
    animation: spinnerRotation 1s linear infinite;
  }
}

.autocomplete__results {
  margin: 0;
  padding: 0;
}

.autocomplete__result {
  display: block;
  padding: 25px;
  overflow: hidden;
  cursor: pointer;
  line-height: normal;

  &:last-child {
    padding: 0;
    border-top: none !important;
  }

  & + .autocomplete__result {
    border-top: 1px solid $border-color;
  }
}

.autocomplete__result:hover,
.autocomplete__result--active {
  background: $background;
}

.autocomplete__image {
  max-width: 60px;
  float: left;
}

.autocomplete__info {
  margin-left: 90px;
  margin-top: 7px;
  overflow: hidden;
}

.autocomplete__info--page {
  margin-top: 0;
}

.autocomplete__subheading {
  display: block;
  margin-top: 0;
  text-transform: uppercase;
  font-size: 14px;
  color: $text-color;
}

.autocomplete__heading {
  display: inline-block;
  margin-top: 8px;
  font-size: 16px;
  font-weight: 700;
  color: $heading-color;
  width: 250px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.autocomplete__price {
  margin-top: 8px;
  float: right;
  font-size: 16px;
  font-weight: 700;
  color: $accent-color;
}

.autocomplete__no-results {
  text-align: center;
  font-size: 20px;
  padding: 51px 0 52px 0;
  font-weight: 700;
  color: $heading-color;
}

.autocomplete__results {
  .button--primary {
    width: 100%;
    text-align: center;
    padding-top: 21px;
    padding-bottom: 21px;
    border-radius: 0;
    font-weight: 700;
  }
}
.section {
  background: $background;
}

.section + .section,
.shopify-section + .shopify-section {
  border-top: 1px solid $border-color;
}

.section--padded {
  padding: 35px 0 40px 0;
}

.section--no-bottom-padding {
  padding-bottom: 0 !important;
}

.section--secondary {
  background: $secondary-background;
}

.section__title {
  margin: 0 0 28px 0;
  text-align: center;
  font-size: 22px;
  font-weight: 700;
  font-family: $heading-font;
  color: $heading-color;
}

@include av-mq('handheld-and-up') {
  .section__title {
    margin-bottom: 30px;
    font-size: 34px;
  }
}

@include av-mq('lap-and-up') {
  .section--padded {
    padding: 46px 0 48px 0;
  }
}
.slideshow {
  position: relative;
  z-index: 0;

  .slick-dots {
    position: absolute;
    right: 0;
    left: 0;
    bottom: 22px;
    margin: 0 auto;
    padding: 0 20px;
    font-size: 0;
    text-align: right;

    li {
      display: inline-block;
      margin-left: 10px;
    }

    button {
      height: 5px;
      width: 23px;
      border-radius: 0;
      border: none;
      background: $slideshow-heading-color;
      background: rgba($slideshow-heading-color, 0.4);
      transition: background 0.25s ease-in-out;

      &:focus {
        outline: none;
      }
    }

    .slick-active button {
      background: $slideshow-heading-color;
    }
  }
}

.slideshow__slides {
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
  will-change: opacity;
}

.slideshow__slides.slick-initialized {
  opacity: 1;
}

.slideshow__slide {
  position: relative;
}

.slideshow__slide--contrasted::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(255, 255, 255, 0.1), rgba(0, 0, 0, 0.25));
}

.slideshow__image {
  width: 100%;
}

.slideshow__heading,
.slideshow__subheading {
  text-shadow: 0 1px 2px rgba(#000000, 0.4);
}

.slideshow__heading {
  margin: 0 0 15px 0;
  color: $slideshow-heading-color;
  font-size: 21px;
  font-family: $slideshow-heading-font;
  line-height: 1.2;
  font-weight: 700;
}

.slideshow__subheading {
  display: block;
  font-family: $slideshow-subheading-font;
  color: $slideshow-subheading-color;
  font-size: 16px;
  line-height: 1.4;

  .slideshow__cover--has-button & {
    display: none; /* if the slideshow has a button, we hide the sub-heading for size reason */
  }
}

.slideshow__button {
  margin-top: 0;
  padding: 11px 25px 12px 25px;
  font-size: 12px;
}

.slideshow__cover {
  position: absolute;
  display: inline-block;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
}

.slideshow__cover-wrapper {
  display: table;
  width: 100%;
  height: 100%;

  & > .container {
    display: table-cell;
    vertical-align: middle;
  }
}

@include av-mq('handheld-and-up') {
  .slideshow__cover-wrapper > .container {
    padding: 0 70px;
  }

  .slideshow {
    .slick-dots {
      bottom: 34px;
      padding: 0 70px;

      button {
        width: 48px;
      }
    }
  }

  .slideshow__heading {
    font-size: $slideshow-heading-font-size - 6px;
    max-width: 800px;
  }

  .slideshow__subheading {
    font-size: $slideshow-subheading-font-size;
    max-width: 600px;
    line-height: 1.8;
  }

  .slideshow__cover--has-button .slideshow__subheading {
    display: block;
  }

  .slideshow__button {
    margin-top: 45px;
    padding: 15px 40px 16px 40px;
    font-size: 14px;
  }
}

@include av-mq('desk') {
  .slideshow .slick-dots button {
    height: 8px;
  }

  .slideshow__heading {
    margin-bottom: 25px;
    font-size: $slideshow-heading-font-size;
  }
}
/**
 * SOCIAL MEDIA
 */

.social-media {
  list-style: none;
}

.social-media--inline .social-media__item {
  display: inline-block;

  &:not(:last-child) {
    margin-right: 15px;
  }
}

/**
 * SOCIAL FEEDS (APPEAR ON HOME PAGE)
 */

.social-feeds {
  text-align: center;
}

.social-feeds__subtitle {
  margin: 22px 0 0 0;
}

.social-feeds__instagram + .social-feeds__twitter,
.social-feeds__twitter + .social-feeds__instagram {
  margin-top: 36px;
}

.social-feeds--desktop {
  display: none;
}

.social-feeds__instagram:only-child .instagram__image-wrapper:nth-child(-n+3) {
  margin-bottom: 14px;
}

@include av-mq('lap-and-up') {
  .social-feeds--mobile {
    display: none;
  }

  .social-feeds--desktop {
    display: block;
  }

  .social-feeds__subtitle {
    margin-top: 42px;
  }

  .social-feeds__instagram,
  .social-feeds__twitter {
    display: inline-block;
    width: 50%;
  }

  .social-feeds__instagram:only-child {
    width: 100%;

    .instagram__image-wrapper {
      width: 16.6666667%;
      margin-bottom: 0 !important;
    }
  }

  .social-feeds__instagram + .social-feeds__twitter,
  .social-feeds__twitter + .social-feeds__instagram {
    float: right;
    margin-top: 0;
    padding-left: 70px;
  }

  .social-feeds__row {
    display: table;
    width: 100%;
    table-layout: fixed;
    border-spacing: 60px 0;

    .instagram__images,
    .tweet,
    .social-feeds__subtitle {
      display: table-cell;
      height: 100%;
      width: 50%;
      vertical-align: middle;
    }

    .social-feeds__subtitle {
      padding-top: 40px;
      vertical-align: top;
    }
  }
}

/**
 * INSTAGRAM
 */

.instagram__images {
  margin: 0 -7px;
  font-size: 0;
}

.instagram__image-wrapper {
  display: inline-block;
  width: 33.33333%;
  padding: 0 7px;

  a {
    position: relative;
    display: block;
  }
}

.instagram__image {
  width: 100%;
}

.instagram__overlay {
  display: none;
}

.no-touchevents {
  .instagram__image-wrapper:hover {
    .instagram__overlay {
      position: absolute;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-justify-content: space-between;
      -ms-flex-pack: justify;
      justify-content: space-between;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      padding: 18px 15px 16px 20px;
      background: rgba(0, 0, 0, 0.6);
      text-align: left;
    }

    .instagram__caption,
    .instagram__date {
      color: #ffffff;
    }

    .instagram__caption {
      display: block;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-top: 0;
      font-size: 13px;
      line-height: 1.55;
      height: 4.65em; /* Exactly three lines */
    }

    .instagram__date {
      font-size: 14px;
      font-weight: 700;
      text-transform: uppercase;
    }
  }
}

/**
 * TWITTER
 */

.tweet {
  padding: 22px 25px 16px 25px;
  background: $background;
  border: 1px solid $border-color;
  text-align: center;
}

.tweet__content {
  margin: 0 0 22px 0;
  word-wrap: break-word;

  a {
    color: $accent-color;
  }
}

.tweet__date {
  color: $heading-color;
  text-transform: uppercase;
  font-size: 12px;
}

@include av-mq('handheld-and-up') {
  .tweet__date {
    font-size: 14px;
  }
}

/**
 * SHARE BUTTONS
 */

.share-buttons__list {
  display: inline-block;
  list-style: none;
  padding: 0;
  margin: 0;
}

.share-buttons__label {
  display: inline-block;
  margin-right: 25px;
  font-size: 14px;
  text-transform: uppercase;
  vertical-align: middle;
}

.share-buttons__item {
  display: inline-block;
  margin-right: 15px;

  &:last-child {
    margin-right: 0;
  }

  &:hover {
    color: $accent-color;
  }
}

@include av-mq('handheld-and-up') {
  .share-buttons__label {
    margin-right: 40px;
  }
}
@-webkit-keyframes circle {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes circle {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.spinner-container {
  margin: 25px;
  text-align: center;
}

.spinner {
  position: relative;
  display: inline-block;
  vertical-align: middle;
}

.spinner--circle {
  width: 30px;
  height: 30px;
  -webkit-animation: circle infinite .75s linear;
  animation: circle infinite .75s linear;
  border: 2px solid $secondary-button-background;
  border-left-color: transparent;
  border-radius: 100%;
}

.spinner-container--large {
  padding: 40px 0;

  .spinner--circle {
    width: 35px;
    height: 35px;
  }
}

@include av-mq('handheld-and-up') {
  .spinner-container--large {
    padding: 80px 0;

    .spinner--circle {
      width: 50px;
      height: 50px;
    }
  }
}
.table {
  width: 100%;

  tr {
    border: 1px solid $border-color;
  }

  th {
    padding: 14px;
    background: $secondary-button-background;
    color: $secondary-button-color;
    border-right: 1px solid $secondary-button-color;

    &:last-child {
      border-right: none;
    }
  }

  td {
    padding: 12px;
    border-right: 1px solid $border-color;
  }
}

@include av-mq('thumb') {
  .table--responsive {
    thead {
      display: none;
    }

    tr {
      display: block;
      border-right: none !important;

      & + tr {
        border-top: none !important;
      }
    }

    // IE9 table layout fixes
    tr,
    td {
      float: left;
      clear: both;
      width: 100%;
    }

    th,
    td {
      display: block;
      text-align: right;
      padding: 8px 15px;
    }

    td:not([data-label]) {
      display: none;
    }

    td:before {
      content: attr(data-label);
      float: left;
      font-size: 13px;
      padding-right: 10px;
      text-transform: uppercase;
    }
  }
}

.table__right {
  text-align: right !important;
}

.table__left {
  text-align: left !important;
}

@-webkit-keyframes tabs-content-opening {
  from {
    -webkit-transform: translateY(20px);
    transform: translateY(20px);
    opacity: 0.4;
  }
  to {
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}

@keyframes tabs-content-opening {
  from {
    -webkit-transform: translateY(20px);
    transform: translateY(20px);
    opacity: 0.4;
  }
  to {
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}

.tabs {
  border-bottom: 1px solid $border-color;
}

.tabs__nav {
  margin: 40px 0 0 0;
  padding: 0;
  list-style: none;
  border-top: 1px solid $border-color;
  border-bottom: 1px solid $border-color;
  overflow: scroll;
  white-space: nowrap;
  -webkit-overflow-scrolling: touch;
}

.tabs__nav-item {
  position: relative;
  display: inline-block;
  margin: 0 15px;
  padding: 16px 0 18px 0;
  font-size: 13px;
  text-transform: uppercase;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  transition: 0.25s ease-in-out;
}

.tabs__nav-line {
  display: block;
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  width: 100%;
  z-index: 1;
  background: $accent-color;
  transition: all 0.25s ease-in-out;
}

.tabs__nav-item:hover,
.tabs__nav-item--active {
  color: $accent-color;
}

.tabs__content {
  background: $secondary-background;
}

.tabs__content-item {
  display: none;
}

.tabs__content-item--active {
  display: block;
  -webkit-animation-name: tabs-content-opening;
  animation-name: tabs-content-opening;
  -webkit-animation-duration: 0.3s;
  animation-duration: 0.3s;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
}

.tabs__content-item > .container {
  padding-top: 25px;
  padding-bottom: 25px;
}

@include av-mq('handheld-and-up') {
  .tabs__nav {
    margin-top: 60px;
    text-align: center;
  }

  .tabs__nav-item {
    font-size: 14px;
  }
}

@include av-mq('lap-and-up') {
  .tabs__nav {
    margin-top: 75px;
    overflow: visible;
  }

  .tabs__nav-line {
    bottom: -2px;
  }

  .tabs__nav-item {
    margin: 0 42px;
  }

  .tabs__content-item > .container {
    padding-top: 50px;
    padding-bottom: 50px;
  }
}

/**
 * LOGIN / REGISTER FORMS
 */

.login-form__forgot {
  float: right;
}

/**
 * ADDRESS / CUSTOMER
 */

.account__orders {
  margin-bottom: 25px;
}

.order__details {
  margin-top: 50px;
}

.order__addresses {
  margin-top: 40px;
}

.order__total {
  font-weight: 700;
  color: $heading-color;
}

.order__summary tr,
.order__summary td {
  border: none;
}

.address__actions {
  margin-top: 10px;
}

.account__address {
  padding: 15px;
  border: 1px solid $border-color;
}

.account__address p::first-line {
  margin-bottom: 16px;
  color: $heading-color;
  font-weight: 700;
}

.address__edit {
  border-top: 1px solid $border-color;
  padding-top: 25px;
}

.address__new {
  margin-top: 25px;

  form {
    text-align: left;
  }
}

.address__address1,
.address__address2,
.address__details,
.address__zip {
  margin-bottom: 0;
}

.account__address-item {
  margin-bottom: 25px;
}
/**
 * Some override specific to the collection page
 */

.template-collection {
  .collection:not(.grid--flush) {
    padding: 25px 0;
  }

  .spinner-container {
    margin-top: 0;
  }
}
.index-module__featured-page .button-group {
  margin-top: 25px;
  text-align: center;
}