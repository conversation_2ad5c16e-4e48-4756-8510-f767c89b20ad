<config>
	<info>
		<env>dev</env>
		<appName>Dane<PERSON>Commerce</appName>
		<version>0.1 alpha EXAMPLE</version>
		<build>${build}</build>
		<date>@@RELEASEDATE_PLACEHOLDER@@</date> <!-- Injected by gradle -->
	</info>
	<paths>
		<serverAddress>http://dnctest.it:8080/</serverAddress>
		<basePath>/srv/dncdev</basePath>
	</paths>
	<email>
		<enabled>false</enabled>
		<from><EMAIL></from>
		<support>
			<!-- Addresses to which a support request is sent -->
			<to><EMAIL></to>
		</support>
		<logoImage>template.email/logo50.png</logoImage>
		<smtpserver>
			<host>smtp.EXAMPLE.com</host>
			<port>587</port>
			<protocol>smtp</protocol>
			<username><EMAIL></username>
			<password>EXAMPLE</password>
			<!--  If set to true, and a message has some valid and some invalid addresses, send the message anyway, reporting the partial failure with a SendFailedException. If set to false (the default), the message is not sent to any of the recipients if there is an invalid recipient address.  -->
			<properties>mail.smtp.sendpartial=true</properties> 
			<properties>mail.smtp.auth=true</properties>
			<properties>mail.smtp.starttls.enable=true</properties>
			<properties>mail.smtp.quitwait=false</properties>
		</smtpserver>
		<!-- Remove this list to enable email to everyone -->
		<validEmail><EMAIL></validEmail>
		<validEmail><EMAIL></validEmail>
		<validEmail><EMAIL></validEmail>
		<validEmail><EMAIL></validEmail>
	</email>

	<database>
		<jndiname>java:comp/env/jdbc/dncdbdev</jndiname>
		<!--  <showSql>true</showSql> -->
		<entityPackage>com.danesemilano.persistence.entity</entityPackage>
	</database>

	<setup>
		<users>
			<user>
				<name>xtian</name>
				<email><EMAIL></email>
				<password>sfkueslifg</password>
				<language>it</language>
				<country>it</country>
				<timezone>Europe/Rome</timezone>
				<role>USER</role>
				<role>CMSUSER</role>
				<role>COMMERCE</role>
				<role>ADMIN</role>
			</user>
		</users>
	</setup>
	
	<!-- questo è per windows -->
	<shell>
		<convert>
			<executable>magick</executable>
				<arg>convert</arg>
				<arg>${FILENAMEIN}</arg>
				<arg>${FILENAMEOUT}</arg>
		</convert>
		<resize>
			<executable>magick</executable>
				<arg>convert</arg>
				<arg>${FILENAMEIN}</arg>
				<arg>-resize</arg>
				<arg>${W}x${H}</arg>
				<arg>${FILENAMEOUT}</arg>
		</resize>
	</shell>
</config>