<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" >
<head>
	<meta charset="utf-8" />
</head>
<body>
<!--/*
	Generic CRUD DataTable to be included with th:replace.
	
	Parameters:
	- tableId = id of the table
	- classes = classes to add to the <table> tag class (optional - defaults to "table-striped no-wrap")
	            You can add the 'yadaNoLoader' class to prevent the loader from showing on table load
	- columns = column name list, like ${ {'ID', 'Name','Surname'} }, without the 'Comandi' column (be careful about the spaces between { {)
	- disableAdd = true per non aggiungere il pulsante di add
	- disableEdit = true per non aggiungere il pulsante di edit
	- disableDelete = true per non aggiungere il pulsante di delete
	- commandsColumnText = (optional) text for the commands column header, otherwise #{column.commands} is used
	- addButtonText = (optional) text for the add button, otherwise #{button.add} is used
	- editButtonText = (optional) text for the edit button, otherwise #{button.edit} is used
	- deleteButtonText = (optional) text for the delete button, otherwise #{button.delete} is used
	- checkbox = (optional) false to hide the leftmost checkbox column
	
	Example:
	<div th:replace="/yada/dataTableCrud::table(tableId='dataSourceTable',columns=${ {'ID', 'Company','Location','Channel','Token'} })"></div>
	
 */-->

<div class="yadaTableBlock" th:fragment="fragment">
	<table th:id="${tableId}" cellspacing="0" width="100%" class="table" th:classappend="${classes}?:'table-striped no-wrap'">
		<thead>
			<tr>
				<th></th> 
				<th th:if="${!(false==checkbox)}" class="yadaCheckInCell all"><input type="checkbox" class="s_columnSelector" title="All/None" /></th>
				<th th:each="column : ${columns}" th:text="${column}">Name</th>
				<th class="all" th:text="${commandsColumnText}?:#{column.commands}">Commands</th>
			</tr>
		</thead>
		<tfoot>
			<tr>
				<td></td> 
				<td th:if="${!(false==checkbox)}"></td> 
				<td th:each="column : ${columns}"></td>
				<td></td>
			</tr>
		</tfoot>
	</table>
	<div th:if="${!(false==checkbox)}" class="yadaTableToolbar">
		<a th:unless="${disableAdd==true}" class="btn btn-primary s_addButton" href="#">
			<i class="yadaIcon yadaIcon-add"></i> <span th:text="${addButtonText}?:#{button.add}">Add</span>
		</a>
		<a th:unless="${disableEdit==true}" class="btn btn-primary disabled s_singlerowButton s_editButton" href="#">
			<i class="yadaIcon yadaIcon-edit"></i> <span th:text="${editButtonText}?:#{button.edit}">Edit</span>
		</a>
		<a th:unless="${disableDelete==true}" class="btn btn-danger disabled s_singlerowButton s_multirowButton s_deleteButton" href="#">
			<i class="yadaIcon yadaIcon-delete"></i> <span th:text="${deleteButtonText}?:#{button.delete}">Delete</span>
		</a>
	</div>
</div>
</body>
</html>
