package com.danesemilano.persistence.repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.danesemilano.persistence.entity.Nation;
import com.google.common.collect.Iterables;

import net.yadaframework.persistence.YadaSql;

/**
 * Locale-related operations on entities 
 */
@Repository
@Transactional(readOnly = true) 
public class NationDao {
	
    @PersistenceContext
	private EntityManager em;
    
	
	/**
	 * Ritorna tutte le Nation che siano state usate almeno in un Dealer, ordinate alfabeticamente
	 * @return
	 */
	public List<Nation> findAvailableNations(Locale locale) {
		@SuppressWarnings("unchecked")
		List<Object[]> selected = YadaSql.instance().selectFrom("select distinct nat, nm from Dealer d") // nm bisogna metterlo per evitare un errore "ORDER BY clause is not in SELECT list"
			.join("join d.nation nat")
			.join("join nat.name nm") // Serve per ordinare
			.where("where KEY(nm)='it'")
			.orderBy("order by nm")
			.setParameter("locale", locale.getLanguage())
			.query(em).getResultList();
		// selected contiene sia Nation che il nome usato per sortare. Ritorno solo Nation
		List<Nation> result = new ArrayList<>();
		for (Object[] row : selected) {
			result.add((Nation)row[0]);
		}
		return result;
	}

    
    /**
	 * Trova una Nation con lo stesso nome dato il suo locale, che non abbia l'id indicato
     * @param name nome del Nation
     * @param locale locale del nome
     * @param excludeId id da escludere, può essere null
	 * @return la Nation o null
	 */
    @SuppressWarnings("unchecked")
	public Nation findExistingNationByName(String name, Locale locale, Long excludeId) {
		return Iterables.getFirst(
			(List<Nation>)YadaSql.instance().selectFrom("select * from Nation x")
			.join("join Nation_name n ON x.id=n.Nation_id")
			.where("n.name = :name and n.locale = :locale").and()
			.where(excludeId!=null, "x.id!=:excludeId").and()
			.limit(1)
			.setParameter("name", name)
			.setParameter("locale", locale)
			.setParameter("excludeId", excludeId)
			.nativeQuery(em, Nation.class).getResultList()
			, null);
	}
	
    /**
     * Cancella una naton
     * @param id nation
     * @param locale
     * @return il nome della nation cancellata
     */
    @Transactional(readOnly = false) 
    public String delete(Long id, Locale locale) {
    	// E' molto comodo usare l'Entity Manager perché, sebbene carichi tutta la riga prima di cancellare, si occupa di eliminare tutte le relazioni senza
    	// problemi di foreign key violations.
    	Nation n = em.find(Nation.class, id);
    	String name = n.getLocalName();
    	em.remove(n);
        return name;
    }
    
}
