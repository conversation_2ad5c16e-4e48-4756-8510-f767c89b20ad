package com.danesemilano.persistence.repository;

import java.util.List;
import java.util.Locale;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.danesemilano.persistence.entity.Tag;
import com.google.common.collect.Iterables;

import net.yadaframework.persistence.YadaSql;

/**
 * Locale-related operations on entities 
 */
@Repository
@Transactional(readOnly = true) 
public class TagDao {
	
    @PersistenceContext
	private EntityManager em;
    
	@Transactional(readOnly = false)
	public void removeTagFromProduct(String tagname, Long productId) {
    	String sql = "delete typ from Tag_YadaProduct typ join Tag t on typ.tags_id = t.id join Tag_name tn on tn.Tag_id = t.id "
    		+ "where tn.name = :tagname and tn.locale='it' and typ.products_id = :productId";
    	em.createNativeQuery(sql)
		.setParameter("productId", productId)
		.setParameter("tagname", tagname)
		.executeUpdate();
    }
    
    /**
     * Cancella l'associazione ai tag che non sono presenti nel set passato
     * @param toKeep
     * @param productId
     */
	@Transactional(readOnly = false) 
	public void deleteMissingTags(Set<Tag> toKeep, Long productId) {
		String sql = "delete from Tag_YadaProduct where products_id = :productId and tags_id not in :toKeepIds";
		List<Long> toKeepIds = toKeep.stream().map(Tag::getId).collect(Collectors.toList());
		em.createNativeQuery(sql)
			.setParameter("productId", productId)
			.setParameter("toKeepIds", toKeepIds)
			.executeUpdate();
	}

	/**
	 * Trova un Tag con lo stesso nome dato il suo locale, che non abbia l'id indicato
     * @param name nome del tag
     * @param locale locale del nome
     * @param excludeId id da escludere, può essere null
	 * @return il tag o null
	 */
    @SuppressWarnings("unchecked")
	public Tag findExistingTagByName(String name, Locale locale, Long excludeId) {
		return Iterables.getFirst(
			(List<Tag>)YadaSql.instance().selectFrom("select * from Tag t")
			.join("join Tag_name n ON t.id=n.Tag_id")
			.where("n.name = :name and n.locale = :locale").and()
			.where(excludeId!=null, "t.id!=:excludeId").and()
			.limit(1)
			.setParameter("name", name)
			.setParameter("locale", locale)
			.setParameter("excludeId", excludeId)
			.nativeQuery(em, Tag.class).getResultList()
			, null);
	}

	
    /**
     * Cancella un Tag
     * @param Tag
     */
    @Transactional(readOnly = false) 
    public String delete(Long id, Locale locale) {
    	// E' molto comodo usare l'Entity Manager perché, sebbene carichi tutta la riga prima di cancellare, si occupa di eliminare tutte le relazioni senza
    	// problemi di foreign key violations.
    	Tag t = em.find(Tag.class, id);
    	String name = t.getLocalName();
    	em.remove(t);
        return name;
    }
    
}
