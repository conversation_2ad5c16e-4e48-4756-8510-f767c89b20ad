package com.danesemilano.persistence.entity;

import java.util.Locale;

import org.springframework.context.MessageSource;

import net.yadaframework.core.YadaLocalEnum;
import net.yadaframework.persistence.entity.YadaPersistentEnum;

public enum EnumRegion implements YadaLocalEnum<EnumRegion> {
	ABRUZZO, 
	AOSTA_VALLEY, 
	APULIA, 
	BASILICATA, 
	CALABRIA, 
	CAMPANIA, 
	EMILIA_ROMAGNA, 
	FRIULI_VENEZIA_GIULIA, 
	LAZIO, 
	LIGURIA, 
	LOMBARDY, 
	MARCHES, 
	MOLISE, 
	PIEDMONT, 
	SARDINIA, 
	SICILY, 
	TRENTINO_SOUTH_TYROL, 
	TUSCANY, 
	UMBRIA, 
	VENETO;
	
	private static String MESSAGES_PREFIX = "region.";
	private YadaPersistentEnum<EnumRegion> yadaPersistentEnum;
	
	public YadaPersistentEnum<EnumRegion> toYadaPersistentEnum() {
		return yadaPersistentEnum;
	}
	
	public void setYadaPersistentEnum(YadaPersistentEnum yadaPersistentEnum) {
		this.yadaPersistentEnum = yadaPersistentEnum;
	}
	
	/**
	 * Return the localized text for this enum
	 * @param messageSource
	 * @param locale
	 * @return
	 */
	public String toString(MessageSource messageSource, Locale locale) {
		return messageSource.getMessage(MESSAGES_PREFIX + name().toLowerCase(), null, locale);
	}

}
