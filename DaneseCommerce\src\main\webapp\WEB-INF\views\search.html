<!DOCTYPE html>
<html th:with="activePage=Search" 
	th:lang="${#locale.language}"
	xmlns:th="http://www.thymeleaf.org" 
	xmlns:yada="http://www.yadaframework.net">
    <head>
        <meta charset="UTF-8"> <!--/* This is needed so that Eclipse sets the correct character set */-->
		<th:block th:replace="~{/header :: head}"/>
    </head>
    <body>
     <th:block th:replace="~{/header :: body}"/>
     
        <div class="page-containar ">
            <div class="container-fluid title-box" >
                <div class="col-lg-2ths col-md-2ths col-sm-2ths col-xs-10ths hidden-xs elementHiddenSM">
                    <a class="" th:href="@{/}">
                        <img yada:src="@{/res/images/logo.png}" alt="Danese Milano" class="danese-logo"/> 
                    </a>
                </div>   
                <div class="col-lg-8ths col-md-8ths col-sm-8ths col-xs-10ths">
                    <a class="category-title bottom-aligned" th:with="parolaCercata=${searchString}, results=#{text.result}" 
                    		th:text="${results +' : '+ parolaCercata}"> search results : searchString</a>
                </div>
            </div>
            <br><br>
        </div>

        <div class="page-containar">
            <div class="container-fluid" >
                <div class="product-box">
                
                    <div th:each="product,iter : ${products}" class="col-lg-2ths col-md-2ths col-sm-2ths col-xs-5ths product-block" 
                    	th:classappend="|${iter.index<5?'top':''} ${iter.index>1 AND iter.index<5?'block-row-secound':''} ${iter.index%5==4?'last':''}|">
                        <a th:href="@{/productDetails(idProduct=${product.id})}">
                        <div class="middle">
                            <img th:src="@{${@yadaFileManager.getDesktopImageUrl(product.image)}}" class="img-responsive" th:alt="${product.productName}" />
                        </div>
                        <div class="product-info">

                            <div class="product-content">
                                <span class="product-name" th:text="${product.productName}">Object Name</span><br>
                                <span class="designer-name bottom-content">
                                    <th:block  th:each="i,iter : ${#numbers.sequence(product.getDesigners().size() - 1, 0, -1)}" 
                                    	th:with="designerName=${iter.last?product.getDesigners().get(i).getNameSurname():(product.getDesigners().get(i).getNameSurname()+', ')}" >
                                    		<th:block th:text="${designerName}">Designer Name
                                   		</th:block>
                                    	<th:block th:if="${!iter.last}"><br>
                                   		</th:block>
                                   	</th:block>
                                   </span>	
                                <th:block th:if="${!#lists.isEmpty(product.subcategories)}">
                                    <span class="product-function" th:text="${product.subcategories.get(0).getLocalText()}">function</span>
                                </th:block>
                            </div>

                        </div>
                        </a>
                    </div>
                    
                    <div  th:each="news,iterNews : ${allnews}" th:if="${productsElements>0}" class="col-lg-2ths col-md-2ths col-sm-2ths col-xs-5ths  product-block "
                    	th:classappend="|${(productsElements + iterNews.index) <5 ?'top':''}
                    	${(productsElements + iterNews.index) >1 AND (productsElements + iterNews.index) <5?'block-row-secound':''}
                    	${(productsElements + iterNews.count) %5 == 0 ?'last':''}|">
                        <div class="middle">
                            <img th:src="@{${@yadaFileManager.getDesktopImageUrl(news.thumbnail)}}"  class="img-responsive" th:alt="${news.localTitle}" />
                        </div>
                       <div class="product-info">
                            <div class="product-content">
                                <span class="product-name">[[${news.localTitle}]]</span><br>
                            </div>
                        </div>
                     </div>
                    
                    <div th:each="designer,iterDesigners : ${designers}" th:if="${productsElements>0}" class="col-lg-2ths col-md-2ths col-sm-2ths col-xs-5ths  product-block "
                    	th:classappend="|${(productsElements + #lists.size(allnews) + iterDesigners.index) <5?'top':''} 
                    	${(productsElements  + #lists.size(allnews) + iterDesigners.index) >1 AND (productsElements + #lists.size(allnews) + iterDesigners.index) <5?'block-row-secound':''}
                    	${(productsElements + #lists.size(allnews) + iterDesigners.count) %5 == 0?'last':''}|">
                        <a class="search-results" th:href="@{/designerDetails(idDesigner=${designer.id})}" th:with="designerName=${designer.nameSurname}, biography=#{text.biography}" 
                        		th:text="${designerName + ' / ' + biography}">designer.nameSurname / biography  </a>
                        <span class="view-search"><a th:href="@{/designerDetails(idDesigner=${designer.id})}"><span class="icon-caret-right" style="font-size: 23px;"></span></a></span>
                    </div>
                    
                    <th:block th:with="prodotti=${productsElements},
                    notizie=${!#lists.isEmpty(allnews)?#lists.size(allnews):0},
                    disegnatori=${!#lists.isEmpty(designers)?#lists.size(designers):0},
                    totPieni=${(prodotti + notizie + disegnatori )}">
                     <th:block th:if="${totPieni % 5 > 0}" th:each="riquadroVuoto,iterator : ${#numbers.sequence(0, 4 - totPieni % 5)}">
	                        <div class="col-lg-2ths col-md-2ths col-sm-2ths col-xs-5ths  product-block hidden-xs " th:classappend="|${iterator.last?'last':''}
	                        ${totPieni<=5?'top':''}|"></div>
	                   </th:block>
	                    <th:block th:if="${totPieni % 2 > 0}" th:each="riquadroVuoto,iterator : ${#numbers.sequence(0, 1 - totPieni % 2)}">
	                        <div class="col-lg-2ths col-md-2ths col-sm-2ths col-xs-5ths  product-block visible-xs-block " th:classappend="${iterator.last?'last':''}"></div>
	                   </th:block>
	                   </th:block>
                    <!-- <div class="col-lg-2ths col-md-2ths col-sm-2ths col-xs-5ths  product-block  last hidden-xs">

                    </div> -->
                </div>
            </div>
        </div>     
    
        
        <th:block th:replace="~{/footer :: body}"/>
        
         <script type="text/javascript">

            $(window).resize(function () {
                productBox();
            });

            productBox();
            function productBox()
            {
                var productBoxSize = $('.product-box').width();
                var viewSize = window.matchMedia("(max-width: 865px)");
                if (viewSize.matches) {
                    productBoxSize = productBoxSize / 2;
                    $('.product-block').css("height", productBoxSize);
                }
                else
                {
                    productBoxSize = productBoxSize / 5;
                    $('.product-block').css("height", productBoxSize);
                }
            }
        </script>

    </body>
</html>
