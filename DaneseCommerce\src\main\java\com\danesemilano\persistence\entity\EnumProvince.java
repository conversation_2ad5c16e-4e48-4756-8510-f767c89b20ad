package com.danesemilano.persistence.entity;

import java.util.Locale;

import org.springframework.context.MessageSource;

import net.yadaframework.core.YadaLocalEnum;
import net.yadaframework.persistence.entity.YadaPersistentEnum;

public enum EnumProvince implements YadaLocalEnum<EnumProvince> {
	AGRIGENTO,
	ALESSANDRIA,
	ANCONA,
	AOSTA,
	AQUILA,
	AREZZO,
	ASCOLI_PICENO,
	ASTI,
	AVELLINO,
	BARI,
	BARLETTA_ANDRIA_TRANI,
	BELLUNO,
	BENEVENTO,
	BERGAMO,
	BIELLA,
	BOLOGNA,
	BOLZANO,
	BRESCIA,
	BRINDISI,
	CAGLIARI,
	CALTANISSETTA,
	CAMPOBASSO,
	CARBONIA_IGLESIAS,
	CASERTA,
	CATANIA,
	CATANZARO,
	CHIETI,
	COMO,
	COSENZA,
	CREMONA,
	CROTONE,
	CUNEO,
	ENNA,
	FERMO,
	FERRARA,
	FLORENCE,
	FOGGIA,
	FORLI_CESENA,
	FROSINONE,
	GENOA,
	GORIZIA,
	GROSSETO,
	IMPERIA,
	ISERNIA,
	LA_SPEZIA,
	LATINA,
	LECCE,
	LECCO,
	LIVORNO,
	LODI,
	LUCCA,
	MACERATA,
	MANTUA,
	MASSA_CARRARA,
	MATERA,
	MEDIO_CAMPIDANO,
	MESSINA,
	MILAN,
	MODENA,
	MONZA_BRIANZA,
	NAPLES,
	NOVARA,
	NUORO,
	OGLIASTRA,
	OLBIA_TEMPIO,
	ORISTANO,
	PADUA,
	PALERMO,
	PARMA,
	PAVIA,
	PERUGIA,
	PESARO_URBINO,
	PESCARA,
	PIACENZA,
	PISA,
	PISTOIA,
	PORDENONE,
	POTENZA,
	PRATO,
	RAGUSA,
	RAVENNA,
	REGGIO_CALABRIA,
	REGGIO_EMILIA,
	RIETI,
	RIMINI,
	ROMA,
	ROVIGO,
	SALERNO,
	SASSARI,
	SAVONA,
	SIENA,
	SYRACUSE,
	SONDRIO,
	TARANTO,
	TERAMO,
	TERNI,
	TURIN,
	TRAPANI,
	TRENTO,
	TREVISO,
	TRIESTE,
	UDINE,
	VARESE,
	VENICE,
	VERBANIA,
	VERCELLI,
	VERONA,
	VIBO_VALENTIA,
	VICENZA,
	VITERBO;
	
	private static String MESSAGES_PREFIX = "province.";
	private YadaPersistentEnum<EnumProvince> yadaPersistentEnum;
	
	public YadaPersistentEnum<EnumProvince> toYadaPersistentEnum() {
		return yadaPersistentEnum;
	}
	
	public void setYadaPersistentEnum(YadaPersistentEnum yadaPersistentEnum) {
		this.yadaPersistentEnum = yadaPersistentEnum;
	}
	
	/**
	 * Return the localized text for this enum
	 * @param messageSource
	 * @param locale
	 * @return
	 */
	public String toString(MessageSource messageSource, Locale locale) {
		return messageSource.getMessage(MESSAGES_PREFIX + name().toLowerCase(), null, locale);
	}

}
