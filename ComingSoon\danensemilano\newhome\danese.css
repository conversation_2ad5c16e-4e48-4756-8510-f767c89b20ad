
html {
    box-sizing: border-box;
}
*, *:before, *:after {
    box-sizing: inherit;
}

/*
* {
	-webkit-transition: all 500ms cubic-bezier(0.250, 0.250, 0.750, 0.750); 
   -moz-transition: all 500ms cubic-bezier(0.250, 0.250, 0.750, 0.750); 
     -o-transition: all 500ms cubic-bezier(0.250, 0.250, 0.750, 0.750); 
        transition: all 500ms cubic-bezier(0.250, 0.250, 0.750, 0.750); 

	-webkit-transition-timing-function: cubic-bezier(0.250, 0.250, 0.750, 0.750); 
	   -moz-transition-timing-function: cubic-bezier(0.250, 0.250, 0.750, 0.750); 
	     -o-transition-timing-function: cubic-bezier(0.250, 0.250, 0.750, 0.750); 
	        transition-timing-function: cubic-bezier(0.250, 0.250, 0.750, 0.750); 
}
*/

body {
	font-family: 'Archivo', sans-serif;
	font-size: 12px;
}

/********/
/* Tabs */

.tabbedPageContent {
    min-height: 300px;
    border-style: solid;
    border-width: 1px;
    border-color: #dddddd;
    border-top: none;
    padding: 20px;
}

.tabbedPageContent > h1 {
	margin-top: 0;
}

/* Tabs */
/********/

