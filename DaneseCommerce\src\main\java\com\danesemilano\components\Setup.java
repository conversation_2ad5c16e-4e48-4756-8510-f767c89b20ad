package com.danesemilano.components;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.TimeZone;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import com.danesemilano.core.DncConfiguration;
import com.danesemilano.persistence.entity.EnumCategory;
import com.danesemilano.persistence.entity.EnumContract;
import com.danesemilano.persistence.entity.EnumProvince;
import com.danesemilano.persistence.entity.EnumRegion;
import com.danesemilano.persistence.entity.EnumSubcategory;
import com.danesemilano.persistence.entity.EnumTipology;

import net.yadaframework.components.YadaSetup;
import net.yadaframework.components.YadaUtil;
import net.yadaframework.core.YadaLocalEnum;
import net.yadaframework.persistence.repository.YadaPersistentEnumDao;
import net.yadaframework.security.persistence.entity.YadaUserCredentials;
import net.yadaframework.security.persistence.entity.YadaUserProfile;
import net.yadaframework.security.persistence.repository.YadaUserCredentialsDao;
import net.yadaframework.security.persistence.repository.YadaUserProfileDao;

@Component
public class Setup extends YadaSetup {
	private transient Logger log = LoggerFactory.getLogger(Setup.class);

	@Autowired YadaUserCredentialsDao userCredentialsRepository;
	@Autowired YadaUserProfileDao yadaUserProfileRepository;
	@Autowired YadaPersistentEnumDao yadaPersistentEnumDao;
	@Autowired DncConfiguration config;
	@Autowired YadaUtil yadaUtil;
	@Autowired private PasswordEncoder encoder;
	
	@Override
	protected void setupApplication() {
		//
		// Localized enums
		//
		List<Class<? extends YadaLocalEnum<?>>> enumClasses = new ArrayList<>();
		// Add all the enum you have defined
		enumClasses.add(EnumCategory.class);
		enumClasses.add(EnumSubcategory.class);
		enumClasses.add(EnumTipology.class);
		enumClasses.add(EnumContract.class);
		enumClasses.add(EnumRegion.class);
		enumClasses.add(EnumProvince.class);
		// Add all your locales
		List<Locale> locales = Arrays.asList(new Locale[]{Locale.ENGLISH, Locale.ITALIAN});
		yadaPersistentEnumDao.initDatabase(enumClasses, locales);
	}
	
	@Override
	protected void setupUsers(List<Map<String, Object>> userList) {
		for (Map<String, Object> userDefinition : userList) {
			String email = (String) userDefinition.get("email");
			String preferredLanguage = (String) userDefinition.get("language");
			String preferredCountry = (String) userDefinition.get("country");
			//String timezone = (String) userDefinition.get("timezone");
			TimeZone timezone = TimeZone.getTimeZone((String) userDefinition.get("timezone"));
			YadaUserCredentials existingUserCredentials = userCredentialsRepository.findFirstByUsername(email);
			if (existingUserCredentials==null) {
				log.info("Setup: creating user {}", email);
				YadaUserCredentials userCredentials = new YadaUserCredentials();
				YadaUserProfile userProfile = new YadaUserProfile();
				
				userCredentials.setUsername(email);
				userCredentials.changePassword((String) userDefinition.get("password"), encoder);
				userCredentials.setEnabled(true);
				for (Integer role : (Set<Integer>)userDefinition.get("roles")) {
					userCredentials.addRole(role);
				}
				userProfile.setUserCredentials(userCredentials);
				userProfile.setTimezone(timezone);
				userProfile = (YadaUserProfile) yadaUserProfileRepository.save(userProfile);
				userCredentialsRepository.save(userCredentials);
			}
		}
	}



}
