package com.danesemilano.persistence.entity;

import java.util.Locale;

import org.springframework.context.MessageSource;

import net.yadaframework.core.YadaLocalEnum;
import net.yadaframework.persistence.entity.YadaPersistentEnum;

public enum EnumContract implements YadaLocalEnum<EnumContract> {
//	Presi dal menu del sito:
	RESIDENTIAL, 
	OFFICE, 
	HOSPITALITY, 
	OUTDOOR, 
	COPORATE_GIFT;
	
	private static String MESSAGES_PREFIX = "enum.contract.";
	
	private YadaPersistentEnum<EnumContract> yadaPersistentEnum;
	
	public YadaPersistentEnum<EnumContract> toYadaPersistentEnum() {
		return yadaPersistentEnum;
	}
	
	// TODO fix generics
	public void setYadaPersistentEnum(YadaPersistentEnum yadaPersistentEnum) {
		this.yadaPersistentEnum = yadaPersistentEnum;
	}
	
	/**
	 * Return the localized text for this enum
	 * @param messageSource
	 * @param locale
	 * @return
	 */
	public String toString(MessageSource messageSource, Locale locale) {
		return messageSource.getMessage(MESSAGES_PREFIX + name().toLowerCase(), null, locale);
	}

}
