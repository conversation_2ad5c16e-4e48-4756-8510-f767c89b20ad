<persistence xmlns="http://java.sun.com/xml/ns/persistence"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xsi:schemaLocation="http://java.sun.com/xml/ns/persistence http://java.sun.com/xml/ns/persistence/persistence_2_0.xsd"
             version="2.0">
             
   <!-- This file is only needed to generate the db schema -->
   
    <persistence-unit name="yadaPersistenceUnit">
    	<!-- These are needed for Hibernate Tools 
    	<jar-file>file:../../../yadaframework/YadaWeb/bin</jar-file>
    	<jar-file>file:../../../yadaframework/YadaWebCMS/bin</jar-file>
    	<jar-file>file:../../../yadaframework/YadaWebCommerce/bin</jar-file>
    	<jar-file>file:../../../yadaframework/YadaWebSecurity/bin</jar-file>
    	-->
    	<!-- These are needed for schema generation. You can remove the ones you don't need -->
  		<class>net.yadaframework.persistence.entity.YadaPersistentEnum</class>
  		<class>net.yadaframework.persistence.entity.YadaAttachedFile</class>
  		<class>net.yadaframework.persistence.entity.YadaBrowserId</class>
  		<class>net.yadaframework.persistence.entity.YadaClause</class>
  		<class>net.yadaframework.persistence.entity.YadaJob</class>
  		<class>net.yadaframework.security.persistence.entity.YadaUserCredentials</class>
  		<class>net.yadaframework.security.persistence.entity.YadaUserProfile</class>
  		<class>net.yadaframework.security.persistence.entity.YadaRegistrationRequest</class>
  		<class>net.yadaframework.security.persistence.entity.YadaSocialCredentials</class>
  		<class>net.yadaframework.security.persistence.entity.YadaAutoLoginToken</class>
    	 
  		<!-- Had to remove this in order to use the copy from com.danesemilano.persistence.entity
  		<class>net.yadaframework.cms.persistence.entity.YadaArticle</class>
  		<class>net.yadaframework.cms.persistence.entity.YadaProduct</class>
  		<class>net.yadaframework.commerce.persistence.entity.YadaCommerceArticle</class>
  		<class>net.yadaframework.commerce.persistence.entity.YadaAddress</class>
  		<class>net.yadaframework.commerce.persistence.entity.YadaCart</class>
  		<class>net.yadaframework.commerce.persistence.entity.YadaCartItem</class>
  		 -->
  		
      <properties>
      		<!-- 
		     <property name="javax.persistence.schema-generation.database.action" value="none" />
		     <property name="javax.persistence.schema-generation.scripts.action" value="drop-and-create" />
		     <property name="javax.persistence.schema-generation.create-database-schemas" value="true" />
		     <property name="javax.persistence.schema-generation.create-source" value="metadata"/> 
             <property name="javax.persistence.schema-generation.drop-source" value="metadata"/>
		     <property name="javax.persistence.schema-generation.scripts.create-target" value="schema/dnc.sql" />
		     <property name="javax.persistence.schema-generation.scripts.drop-target" value="schema/dnc_drop.sql" />
		     <property name="javax.persistence.schema-generation.connection" value="**************************************************************************************************"/>

		    <property name="javax.persistence.jdbc.driver" value="com.mysql.cj.jdbc.Driver"/>
		    <property name="javax.persistence.jdbc.url" value="****************************************************************************************************************************************************"/>
		    <property name="javax.persistence.jdbc.user" value="dncuserdev"/>
		    <property name="javax.persistence.jdbc.password" value="sevs3"/>
            <property name="javax.persistence.schema-generation.scripts.action" value="create"/>
			<property name="javax.persistence.schema-generation.scripts.create-target" value="schema/dnc.sql"/>
      		 -->
		    <property name="jakarta.persistence.jdbc.driver" value="com.mysql.cj.jdbc.Driver"/>
		    <property name="jakarta.persistence.jdbc.url" value="****************************************************************************************************************************************************"/>
		    <property name="jakarta.persistence.jdbc.user" value="dncuserdev"/>
		    <property name="jakarta.persistence.jdbc.password" value="sevs3"/>
            <property name="jakarta.persistence.schema-generation.scripts.action" value="create"/>
			<property name="jakarta.persistence.schema-generation.scripts.create-target" value="schema/dnc.sql"/>

			<!-- 
            <property name="hibernate.connection.driver_class" value="com.mysql.jdbc.Driver"/>
            <property name="hibernate.connection.username" value="dncuserdev"/>
            <property name="hibernate.connection.password" value="sevs3"/>
            <property name="hibernate.connection.url" value="****************************************************************************************************************************************************"/>
			 -->
            
            <!-- InnoDB Dialect 
         	<property name="hibernate.dialect" value="org.hibernate.dialect.MySQL8Dialect"/>
            -->
         	<!-- MyISAM Dialect (no transactions)
         	<property name="hibernate.dialect" value="org.hibernate.dialect.MySQL5Dialect"/>
         	 -->
      </properties>
   		 
   </persistence-unit>
</persistence>