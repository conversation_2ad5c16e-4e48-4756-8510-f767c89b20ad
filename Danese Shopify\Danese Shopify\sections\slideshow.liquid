<section class="index-module slideshow"
         data-autoplay="{{ section.settings.autoplay }}"
         data-animation-type="{{ section.settings.animation_type }}"
         data-cycle-speed="{{ section.settings.cycle_speed | times: 1000 }}">
  <div class="slideshow__slides">
    {% for block in section.blocks %}
      {% if block.settings.button_text != blank %}
        {% assign has_slideshow_button = true %}
      {% else %}
        {% assign has_slideshow_button = false %}
      {% endif %}

      <div class="slideshow__slide {% if section.settings.increase_contrast %}slideshow__slide--contrasted{% endif %}" data-slide-index="{{ forloop.index0 }}" {{ block.shopify_attributes }} style="{% unless forloop.first %}display: none;{% endunless %}">
        {% if block.settings.button_text == blank and block.settings.link != blank %}
          <a href="{{ block.settings.link }}">
        {% endif %}

        {% if block.settings.image %}
          <img class="slideshow__image"
               width="{{ block.settings.image.width }}"
               height="{{ block.settings.image.height }}"
               src="{{ block.settings.image | img_url: '1024x', format: 'pjpg' }}"
               srcset="{{ block.settings.image | img_url: '600x', format: 'pjpg' }} 600w, {{ block.settings.image | img_url: '1024x', format: 'pjpg' }} 1024w, {{ block.settings.image | img_url: '1800x', format: 'pjpg' }} 1800w"
               alt="{{ block.settings.image.alt }}">
        {% else %}
          {% capture current %}{% cycle 1, 2 %}{% endcapture %}
          {{ 'lifestyle-' | append: current | placeholder_svg_tag: 'placeholder-svg' }}
        {% endif %}

        <div class="slideshow__cover {% if has_slideshow_button %}slideshow__cover--has-button{% endif %}" style="text-align: {{ block.settings.content_position }}">
          <div class="slideshow__cover-wrapper">
            <div class="container">
              {% if block.settings.heading != blank %}
                {% if block.settings.content_position == 'right' %}
                  <h1 class="slideshow__heading" style="margin-left: auto">{{ block.settings.heading }}</h1>
                  {% elsif block.settings.content_position == 'left' %}
                  <h1 class="slideshow__heading" style="margin-right: auto">{{ block.settings.heading }}</h1>
                {% else %}
                  <h1 class="slideshow__heading" style="margin-left: auto; margin-right: auto">{{ block.settings.heading }}</h1>
                {% endif %}
              {% endif %}

              {% if block.settings.subheading != blank %}
                {% if block.settings.content_position == 'right' %}
                  <h2 class="slideshow__subheading" style="margin-left: auto">{{ block.settings.subheading }}</h2>
                  {% elsif block.settings.content_position == 'left' %}
                  <h2 class="slideshow__subheading" style="margin-right: auto">{{ block.settings.subheading }}</h2>
                {% else %}
                  <h2 class="slideshow__subheading" style="margin-left: auto; margin-right: auto">{{ block.settings.subheading }}</h2>
                {% endif %}
              {% endif %}

              {% if has_slideshow_button %}
                <a href="{{ block.settings.link }}" class="slideshow__button button button--primary">{{ block.settings.button_text }}</a>
              {% endif %}
            </div>
          </div>
        </div>

        {% if block.settings.button_text == blank and block.settings.link != blank %}
          </a>
        {% endif %}
      </div>
    {% endfor %}
  </div>
</section>

{% schema %}
{
  "name": "Slideshow",
  "class": "shopify-section__slideshow",
  "max_blocks": 5,
  "settings": [
    {
      "type": "checkbox",
      "id": "increase_contrast",
      "label": "Increase contrast",
      "info": "This adds a gradient over the image to make text easier to read",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Auto rotate between slides",
      "default": true
    },
    {
      "type": "select",
      "id": "animation_type",
      "label": "Animation type",
      "options": [
        {
          "value": "fade",
          "label": "Fade"
        },
        {
          "value": "slide",
          "label": "Slide"
        }
      ],
      "default": "slide"
    },
    {
      "type": "range",
      "id": "cycle_speed",
      "min": 3,
      "max": 10,
      "step": 1,
      "unit": "sec",
      "label": "Change slides every",
      "default": 5
    }
  ],
  "blocks": [
    {
      "type": "image",
      "name": "Slide",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "1600 x 800px jpg recommended"
        },
        {
          "type": "select",
          "id": "content_position",
          "label": "Content position",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "center"
        },
        {
          "type": "text",
          "id": "heading",
          "label": "Heading",
          "default": "Your slideshow"
        },
        {
          "type": "text",
          "id": "subheading",
          "label": "Subheading",
          "default": "Tell your brand's story through beautiful images"
        },
        {
          "type": "text",
          "id": "button_text",
          "label": "Button text"
        },
        {
          "type": "url",
          "id": "link",
          "label": "Link",
          "info": "It is applied to the button only if present, otherwise the whole slide is clickable"
        }
      ]
    }
  ],
  "presets": [
    {
      "category": "Image",
      "name": "Slideshow",
      "settings": {
        "autoplay": true,
        "animation_type": "slide",
        "cycle_speed": 5
      },
      "blocks": [
        {
          "type": "image"
        },
        {
          "type": "image"
        }
      ]
    }
  ]
}
{% endschema %}