package com.danesemilano.components;

import java.io.File;
import java.io.IOException;
import java.util.Iterator;

import jakarta.persistence.NonUniqueResultException;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.EncryptedDocumentException;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.ui.Model;
import org.springframework.web.multipart.MultipartFile;

import com.danesemilano.persistence.entity.Dealer;
import com.danesemilano.persistence.entity.EnumProvince;
import com.danesemilano.persistence.entity.EnumRegion;
import com.danesemilano.persistence.entity.Nation;
import com.danesemilano.persistence.repository.DealerRepoDao;
import com.danesemilano.persistence.repository.NationRepoDao;

import net.yadaframework.components.YadaNotify;
import net.yadaframework.components.YadaWebUtil;

/**
 * Metodi per il caricamento dei file xlsx e csv
 * <AUTHOR>
 *
 */
@Component
public class DealerFileParser {
	private final Logger log = LoggerFactory.getLogger(getClass());
	
	@Autowired private DealerRepoDao dealerRepository;
	@Autowired private NationRepoDao nationRepository;
	@Autowired YadaWebUtil yadaWebUtil;
	@Autowired YadaNotify yadaNotify;
	
	public class Result {
		public int added;
		public int updated;
		public int removed;
		public int errors;
	}

	/**
	 * Carica i dealers prendendoli da un file xlsx. Controlla se esite già altrimenti ne crea uno nuovo
	 * @param attachedFile
	 * @param model
	 * @throws IOException 
	 * @throws InvalidFormatException 
	 * @throws EncryptedDocumentException 
	 */
	public Result loadFileXLS(MultipartFile  attachedFile, Model model) throws IOException, EncryptedDocumentException, InvalidFormatException {
		
		Result result = new Result();
		File tempFile = File.createTempFile("dealer", ".xls");
		yadaWebUtil.saveAttachment(attachedFile, tempFile);
		// carichi.....try (Workbook workbook 
		
	   // 1. You can obtain a sheetIterator and iterate over it
       // Iterator<Sheet> sheetIterator = workbook.sheetIterator();
		
		// Memorizzo quello nuovo facendo EmotionLift Impact (P) - EmotionLift Impact (N)
		try (Workbook workbook = WorkbookFactory.create(tempFile)) {
			
			Sheet sheet = workbook.getSheetAt(0);
			Iterator<Row> rows = sheet.rowIterator();
			Row row;
			row = rows.next(); // Salto header (prima riga)
			
			while (rows.hasNext()) {
				row = rows.next(); 
				result = aggiornaOrCreaDealer(row, result, model);
			} 
		} catch (IOException e) {
			log.error("Impossibile caricare il file XLS dei Tags: ", e);
			result.errors++;
		} finally {
			tempFile.delete();
		}
		return result;
	}
	
	private String getNextCellValue(int column, Row row) {
		Cell cell = row.getCell(column, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL);
		if (cell!=null) {
//			Cell cell = cells.next();
			if (cell.getCellType() == CellType.NUMERIC) {
				return Long.toString(Double.valueOf(cell.getNumericCellValue()).longValue());
			}
			return cell.getStringCellValue();
		}
		return "";
	}

	public Result aggiornaOrCreaDealer(Row row, Result result, Model model) {
		
		Cell cell = row.getCell(1, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL);
		
		//Controllo se c'è il nome del Dealer sulla cella corrispettiva
		if (cell==null || cell.getCellType() != CellType.STRING) {
			// Se la prima cella non è una stringa, salto tutta la riga
			return result;
		}
		//riprendo dalla cella 0
		cell = row.getCell(0, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL);
	
		String id=getNextCellValue(0, row);//cell.getStringCellValue();
		String nome=getNextCellValue(1, row);
		String nazione=getNextCellValue(2, row);
		String regione=getNextCellValue(3, row).toUpperCase().trim();
		String provincia=getNextCellValue(4, row).toUpperCase().trim();
		String indirizzo=getNextCellValue(5, row);
		String email=getNextCellValue(6, row);
		String telefono=getNextCellValue(7, row);
		String fax=getNextCellValue(8, row);
		String agente=getNextCellValue(9, row);
		
		try {
			Dealer dealer = null;
			if (id!=null && !StringUtils.isEmpty(id)) {
				dealer = dealerRepository.findOne(Long.parseLong(id));
			} 
			if (dealer == null){
				dealer = new Dealer();
				result.added++;
			} else {
				result.updated++;
			}
			dealer.setName(nome);
			
			Nation nation = nationRepository.findByNationName(nazione);
			dealer.setNation(nation);
			if (regione.length()>0) {
				// TODO non credo sia giusto aggiornare la regione solo se non è vuota, perché così è impossibile cancellarla
				dealer.setRegion(EnumRegion.valueOf(regione).toYadaPersistentEnum());
			} // TODO else dealer.setRegion(null)
			if (provincia.length()>0) {
				// TODO non credo sia giusto aggiornare la provincia solo se non è vuota, perché così è impossibile cancellarla
				dealer.setProvince(EnumProvince.valueOf(provincia).toYadaPersistentEnum());
			} // TODO else dealer.setProvince(null)
			dealer.setAddress(indirizzo);
			dealer.setEmail(email);
			dealer.setPhone(telefono);
			dealer.setFax(fax);
			dealer.setAgente("si".equals(agente.toLowerCase()) || "sì".equals(agente.toLowerCase()));
			
			//salva
			dealerRepository.save(dealer);
			
			log.debug("Dealer salvato: " + nome);
			yadaNotify.title("Dealer Updated", model).ok().message(nome +" updated").add();
		} catch (NonUniqueResultException e) {
			log.debug("Dealer - " + nome + " - error: " + e.toString());
			yadaNotify.title("Not return a unique result", model).error().message("{} not added", nome).add();
			result.errors++;
		} catch (Exception e) {
			log.debug("Dealer - " + nome + " - error: " + e.toString());
			yadaNotify.title("XLS  File Parsing", model).error().message("Dealer {} not added - {}", nome, e.toString()).add();
			result.errors++;
		}
		return result;
	}
	
}
