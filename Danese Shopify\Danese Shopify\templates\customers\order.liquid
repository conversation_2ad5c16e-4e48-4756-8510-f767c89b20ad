<header class="page__header">
  {% include 'breadcrumb' %}

  <div class="page__header-wrapper">
    <div class="container">
      <h1 class="page__title">{{ 'customer.order.title' | t: number: order.name }}</h1>

      <div class="page__action">
        <a href="/account/logout" class="button button--secondary">{{ 'customer.account.logout' | t }}</a>
      </div>
    </div>
  </div>
</header>

<div class="page__content">
  <section class="order">
    <div class="container">
      {% if order.cancelled %}
        <div class="alert alert--error">
          {% assign cancelled_at = order.cancelled_at | date: format: 'month_day_year_time' %}

          <h3 class="alert__title">{{ 'customer.order.cancelled' | t: date: cancelled_at }}</h3>
          <p class="alert__content">({{ order.cancel_reason_label }})</p>
        </div>
      {% endif %}

      <div class="alert alert--success">
        {% assign placed_at = order.created_at | date: format: 'month_day_year_time' %}

        <h3 class="alert__title">{{ 'customer.order.date' | t: date: placed_at }}</h3>
      </div>

      <div class="order__details">
        <table class="table table--responsive">
          <thead>
            <tr>
              <th>{{ 'customer.order.product' | t }}</th>
              <th>{{ 'customer.order.sku' | t }}</th>
              <th>{{ 'customer.order.price' | t }}</th>
              <th>{{ 'customer.order.quantity' | t }}</th>
              <th>{{ 'customer.order.total' | t }}</th>
            </tr>
          </thead>

          <tbody>
            {% for line_item in order.line_items %}
              <tr data-id="{{ line_item.id }}">
                <td class="order__item" data-label="{{ 'customer.order.product' | t }}">
                  <a href="{{ line_item.product.url }}" class="link">{{ line_item.title }}</a>

                  {% if line_item.fulfillment %}
                    <div class="fulfilled">
                      {% assign fulfilled_at = order.cancelled_at | date: format: 'month_day_year' %}
                      {{ 'customer.order.fulfilled_at' | t: date: fulfilled_at }}

                      {% if line_item.fulfillment.tracking_number %}
                        <a href="{{ line_item.fulfillment.tracking_url }}" class="link">{{ line_item.fulfillment.tracking_company }} #{{ line_item.fulfillment.tracking_number}}</a>
                      {% endif %}
                    </div>
                  {% endif %}
                </td>

                <td class="order__sku" data-label="{{ 'customer.order.sku' | t }}">{{ line_item.sku }}</td>
                <td class="order__price" data-label="{{ 'customer.order.price' | t }}">{{ line_item.price | money }}</td>
                <td class="order__quantity" data-label="{{ 'customer.order.quantity' | t }}">{{ line_item.quantity }}</td>
                <td class="order__subtotal" data-label="{{ 'customer.order.total' | t }}">{{ line_item.line_price | money }}</td>
              </tr>
            {% endfor %}
          </tbody>

          <tfoot class="order__summary">
            <tr>
              <td colspan="4" class="table__right">{{ 'customer.order.subtotal' | t }}:</td>
              <td data-label="{{ 'customer.order.subtotal' | t }}">{{ order.subtotal_price | money }}</td>
            </tr>

            {% for discount in order.discounts %}
              <tr>
                <td colspan="4" class="table__right">{{ discount.code }} {{ 'customer.order.discount' | t }}:</td>
                <td data-label="{{ 'customer.order.discount' | t }}">{{ discount.savings | money }}</td>
              </tr>
            {% endfor %}

            {% for shipping_method in order.shipping_methods %}
              <tr>
                <td colspan="4" class="table__right">{{ 'customer.order.shipping' | t }} ({{ shipping_method.title }}):</td>
                <td data-label="{{ 'customer.order.shipping' | t }}">{{ shipping_method.price | money }}</td>
              </tr>
            {% endfor %}

            {% for tax_line in order.tax_lines %}
              <tr>
                <td colspan="4" class="table__right">{{ 'customer.order.tax' | t }} ({{ tax_line.title }} {{ tax_line.rate | times: 100 }}%):</td>
                <td data-label="{{ 'customer.order.tax' | t }} ({{ tax_line.title }} {{ tax_line.rate | times: 100 }}%)">{{ tax_line.price | money }}</td>
              </tr>
            {% endfor %}

            <tr>
              <td colspan="4" class="table__right">{{ 'customer.order.total' | t }}:</td>
              <td class="order__total" data-label="{{ 'customer.order.total' | t }}">{{ order.total_price | money }} {{ order.currency }}</td>
            </tr>
          </tfoot>
        </table>

        <section class="order__addresses grid grid--gallery">
          <div class="grid__cell 1/2--handheld-and-up">
            <p>{{ 'customer.order.billing_address' | t }}</p>

            <section class="account__address account__address--main">
              {{ order.billing_address | format_address }}
            </section>
          </div>

          {% if order.shipping_address %}
            <div class="grid__cell 1/2--handheld-and-up">
              <p>{{ 'customer.order.shipping_address' | t }}</p>

              <section class="account__address account__address--main">
                {{ order.shipping_address | format_address }}
              </section>
            </div>
          {% endif %}
        </section>
      </div>
    </div>
  </section>
</div>