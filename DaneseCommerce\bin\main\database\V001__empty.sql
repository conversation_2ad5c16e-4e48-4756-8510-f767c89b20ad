create table Article_finish (Article_id bigint not null, finish varchar(255), locale varchar(32) not null, primary key (Article_id, locale)) engine=InnoDB;
create table Article_language (Article_id bigint not null, language varchar(255), locale varchar(32) not null, primary key (Article_id, locale)) engine=InnoDB;
create table Article_shopUrl (Article_id bigint not null, shopUrl varchar(255), locale varchar(32) not null, primary key (Article_id, locale)) engine=InnoDB;
create table Article_tipology (Article_id bigint not null, tipology varchar(255), locale varchar(32) not null, primary key (Article_id, locale)) engine=InnoDB;
create table Dealer (id bigint not null auto_increment, address varchar(8192), agente bit not null, email varchar(255), fax varchar(255), modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, name varchar(255), phone varchar(255), version bigint not null, nation_id bigint, province_id bigint, region_id bigint, primary key (id)) engine=InnoDB;
create table Designer (id bigint not null auto_increment, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, name varchar(64), published bit not null, surname varchar(64), version bigint not null, image_id bigint, primary key (id)) engine=InnoDB;
create table Designer_description (Designer_id bigint not null, description varchar(8192), locale varchar(32) not null, primary key (Designer_id, locale)) engine=InnoDB;
create table Designer_YadaProduct (designers_id bigint not null, products_id bigint not null) engine=InnoDB;
create table EventNews (id bigint not null auto_increment, evento bit not null, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, publishDate datetime, version bigint not null, video varchar(1024), image_id bigint, thumbnail_id bigint, primary key (id)) engine=InnoDB;
create table EventNews_content (EventNews_id bigint not null, content varchar(8192), locale varchar(32) not null, primary key (EventNews_id, locale)) engine=InnoDB;
create table EventNews_subtitle (EventNews_id bigint not null, subtitle varchar(255), locale varchar(32) not null, primary key (EventNews_id, locale)) engine=InnoDB;
create table EventNews_title (EventNews_id bigint not null, title varchar(255), locale varchar(32) not null, primary key (EventNews_id, locale)) engine=InnoDB;
create table Nation (id bigint not null auto_increment, code varchar(255), modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, version bigint not null, primary key (id)) engine=InnoDB;
create table Nation_name (Nation_id bigint not null, name varchar(255), locale varchar(32) not null, primary key (Nation_id, locale)) engine=InnoDB;
create table Product_color (Product_id bigint not null, color varchar(255), locale varchar(32) not null, primary key (Product_id, locale)) engine=InnoDB;
create table Product_contract (Product_id bigint not null, contract_id bigint not null) engine=InnoDB;
create table Product_edition (Product_id bigint not null, edition varchar(255), locale varchar(32) not null, primary key (Product_id, locale)) engine=InnoDB;
create table Product_files (Product_id bigint not null, files_id bigint not null) engine=InnoDB;
create table Product_files2d (Product_id bigint not null, files2d_id bigint not null) engine=InnoDB;
create table Product_files3d (Product_id bigint not null, files3d_id bigint not null) engine=InnoDB;
create table Product_finish (Product_id bigint not null, finish varchar(255), locale varchar(32) not null, primary key (Product_id, locale)) engine=InnoDB;
create table Product_technicalData (Product_id bigint not null, technicalData varchar(255), locale varchar(32) not null, primary key (Product_id, locale)) engine=InnoDB;
create table Product_tipology (Product_id bigint not null, tipology_id bigint not null) engine=InnoDB;
create table Tag (id bigint not null auto_increment, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, version bigint not null, primary key (id)) engine=InnoDB;
create table Tag_EventNews (tags_id bigint not null, eventNews_id bigint not null) engine=InnoDB;
create table Tag_name (Tag_id bigint not null, name varchar(255), locale varchar(32) not null, primary key (Tag_id, locale)) engine=InnoDB;
create table Tag_YadaProduct (tags_id bigint not null, products_id bigint not null) engine=InnoDB;
create table YadaAddress (id bigint not null auto_increment, city varchar(64), country varchar(64), modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, notes varchar(64), number varchar(8), state varchar(64), street varchar(64), version bigint not null, zipCode varchar(16), owner_id bigint, primary key (id)) engine=InnoDB;
create table YadaArticle (DTYPE varchar(31) not null, id bigint not null auto_increment, code varchar(32), depth float, diameter float, elements integer, height float, size varchar(16), weight float, width float, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, published bit not null, unitPrice bigint, version bigint not null, itemSize varchar(32), quantity integer, source varchar(64), availableQuantity integer, daysBeforeAvailable integer, image_id bigint, product_id bigint, silhouette_id bigint, primary key (id)) engine=InnoDB;
create table YadaArticle_attachments (YadaArticle_id bigint not null, attachments_id bigint not null) engine=InnoDB;
create table YadaArticle_color (YadaArticle_id bigint not null, color varchar(32), locale varchar(32) not null, primary key (YadaArticle_id, locale)) engine=InnoDB;
create table YadaArticle_galleryImages (YadaArticle_id bigint not null, galleryImages_id bigint not null) engine=InnoDB;
create table YadaArticle_name (YadaArticle_id bigint not null, name varchar(64), locale varchar(32) not null, primary key (YadaArticle_id, locale)) engine=InnoDB;
create table YadaArticle_silhouetteImages (YadaArticle_id bigint not null, silhouetteImages_id bigint not null) engine=InnoDB;
create table YadaAttachedFile (id bigint not null auto_increment, attachedToId bigint, clientFilename varchar(255), filename varchar(255), filenameDesktop varchar(255), filenameMobile varchar(255), forLocale varchar(255), modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, published bit not null, relativeFolderPath varchar(255), sortOrder bigint not null, uploadTimestamp datetime, version bigint not null, primary key (id)) engine=InnoDB;
create table YadaAttachedFile_description (YadaAttachedFile_id bigint not null, description varchar(512), locale varchar(32) not null, primary key (YadaAttachedFile_id, locale)) engine=InnoDB;
create table YadaAttachedFile_title (YadaAttachedFile_id bigint not null, title varchar(64), locale varchar(32) not null, primary key (YadaAttachedFile_id, locale)) engine=InnoDB;
create table YadaAutoLoginToken (id bigint not null auto_increment, expiration datetime, timestamp datetime, token bigint not null, version bigint not null, yadaUserCredentials_id bigint, primary key (id)) engine=InnoDB;
create table YadaBrowserId (id bigint not null auto_increment, leastSigBits bigint, mostSigBits bigint, version bigint not null, primary key (id)) engine=InnoDB;
create table YadaCart (id bigint not null auto_increment, creationTimestamp datetime, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, version bigint not null, owner_id bigint, primary key (id)) engine=InnoDB;
create table YadaCartItem (id bigint not null auto_increment, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, quantity integer not null, version bigint not null, article_id bigint, cart_id bigint, primary key (id)) engine=InnoDB;
create table YadaClause (id bigint not null auto_increment, clauseVersion integer not null, content longtext, name varchar(32) not null, version bigint not null, primary key (id)) engine=InnoDB;
create table YadaJob (id bigint not null auto_increment, errorStreakCount integer not null, jobDescription varchar(256), jobGroup varchar(128), jobGroupPaused bit not null, jobLastSuccessfulRun TIMESTAMP NULL, jobName varchar(128), jobPriority integer not null, jobRecoverable bit not null, jobScheduledTime TIMESTAMP NULL, jobStartTime datetime, jobStateObject_id bigint, primary key (id)) engine=InnoDB;
create table YadaJob_BeActive (YadaJob_id bigint not null, jobsMustBeActive_id bigint not null) engine=InnoDB;
create table YadaJob_BeCompleted (YadaJob_id bigint not null, jobsMustComplete_id bigint not null) engine=InnoDB;
create table YadaJob_BeInactive (YadaJob_id bigint not null, jobsMustBeInactive_id bigint not null) engine=InnoDB;
create table YadaOrder (id bigint not null auto_increment, creationTimestamp datetime, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, notes varchar(2048), shippingTimestamp datetime, stateChangeTimestamp datetime, totalPrice bigint, trackingData varchar(512), version bigint not null, orderStatus_id bigint, owner_id bigint, primary key (id)) engine=InnoDB;
create table YadaOrderItem (id bigint not null auto_increment, articleCode varchar(255), modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, quantity integer not null, unitPrice bigint, version bigint not null, order_id bigint not null, primary key (id)) engine=InnoDB;
create table YadaPersistentEnum (id bigint not null auto_increment, enumClassName varchar(191) not null, enumName varchar(255) not null, enumOrdinal integer not null, primary key (id)) engine=InnoDB;
create table YadaPersistentEnum_langToText (YadaPersistentEnum_id bigint not null, localText varchar(128), language varchar(32) not null, primary key (YadaPersistentEnum_id, language)) engine=InnoDB;
create table YadaProduct (DTYPE varchar(31) not null, id bigint not null auto_increment, accessoryFlag bit not null, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, published bit not null, version bigint not null, year integer not null, dimension varchar(32), productName varchar(255), source varchar(64), image_id bigint, silhouette_id bigint, wireframe_id bigint, primary key (id)) engine=InnoDB;
create table YadaProduct_accessories (accessoryOf_id bigint not null, accessories_id bigint not null) engine=InnoDB;
create table YadaProduct_attachments (YadaProduct_id bigint not null, attachments_id bigint not null) engine=InnoDB;
create table YadaProduct_categories (YadaProduct_id bigint not null, categories_id bigint not null) engine=InnoDB;
create table YadaProduct_description (YadaProduct_id bigint not null, description varchar(8192), locale varchar(32) not null, primary key (YadaProduct_id, locale)) engine=InnoDB;
create table YadaProduct_galleryImages (YadaProduct_id bigint not null, galleryImages_id bigint not null) engine=InnoDB;
create table YadaProduct_materials (YadaProduct_id bigint not null, materials varchar(128), locale varchar(32) not null, primary key (YadaProduct_id, locale)) engine=InnoDB;
create table YadaProduct_name (YadaProduct_id bigint not null, name varchar(64), locale varchar(32) not null, primary key (YadaProduct_id, locale)) engine=InnoDB;
create table YadaProduct_subcategories (YadaProduct_id bigint not null, subcategories_id bigint not null) engine=InnoDB;
create table YadaProduct_subtitle (YadaProduct_id bigint not null, subtitle varchar(128), locale varchar(32) not null, primary key (YadaProduct_id, locale)) engine=InnoDB;
create table YadaRegistrationRequest (id bigint not null auto_increment, email varchar(64) not null, password varchar(128) not null, registrationType integer, timestamp datetime, token bigint not null, version bigint not null, trattamentoDati_id bigint, yadaUserCredentials_id bigint, primary key (id)) engine=InnoDB;
create table YadaSocialCredentials (id bigint not null auto_increment, email varchar(128) not null, socialId varchar(128) not null, type integer not null, version bigint not null, yadaUserCredentials_id bigint not null, primary key (id)) engine=InnoDB;
create table YadaUserCredentials (id bigint not null auto_increment, changePassword bit not null, creationDate datetime, enabled bit not null, failedAttempts integer not null, lastFailedAttempt datetime, lastSuccessfulLogin datetime, password varchar(128) not null, passwordDate datetime, username varchar(128) not null, version bigint not null, primary key (id)) engine=InnoDB;
create table YadaUserCredentials_roles (YadaUserCredentials_id bigint not null, roles integer) engine=InnoDB;
create table YadaUserProfile (id bigint not null auto_increment, firstName varchar(32), lastName varchar(64), locale varchar(32), middleName varchar(32), timezone varchar(64), version bigint not null, userCredentials_id bigint not null, primary key (id)) engine=InnoDB;
alter table Designer_YadaProduct add constraint UKbocerkkc9yc0dl4m3cggltq4y unique (designers_id, products_id);
alter table Product_contract add constraint UKrqx2wakjbcyvs9k7b656kbtk7 unique (contract_id, Product_id);
alter table Product_files add constraint UK_cud4h711lvhwlpbf9posyf9d4 unique (files_id);
alter table Product_files2d add constraint UK_gbpe25w88hjm3qh40jkhnbbsp unique (files2d_id);
alter table Product_files3d add constraint UK_fnwnq93wym83mbf383ffxl3nw unique (files3d_id);
alter table Product_tipology add constraint UK8t9s4tvexh7xtw11mdoasop2c unique (tipology_id, Product_id);
alter table Tag_EventNews add constraint UK8vn7iw7npvwn1qpq6wevfigfh unique (tags_id, eventNews_id);
alter table Tag_name add constraint UK8a9en9qol3ngiqif6snwemlln unique (name, locale);
alter table Tag_YadaProduct add constraint UKsec1jhdelcrikh2uelamixue3 unique (tags_id, products_id);
alter table YadaArticle_attachments add constraint UK_ckp9t4579t7mov1miiu8fql4f unique (attachments_id);
alter table YadaArticle_galleryImages add constraint UK_rl1gkdyfrt43m5jw3u598olgk unique (galleryImages_id);
alter table YadaArticle_silhouetteImages add constraint UK_sq5igv7krtxe04nyyhlx4djeg unique (silhouetteImages_id);
alter table YadaBrowserId add constraint UKlvfuna79iqujxpkn0l6xvirh4 unique (mostSigBits, leastSigBits);
alter table YadaClause add constraint UKek0brxiv78vf6idvd6dv8v69d unique (name, clauseVersion);
alter table YadaPersistentEnum add constraint UKfuc71vofqasw0r57t7etipp7p unique (enumClassName, enumOrdinal);
alter table YadaProduct_attachments add constraint UK_fsu5mkfbdh7dvoq619df26n50 unique (attachments_id);
alter table YadaProduct_categories add constraint UK1cmx2j13ct60yhcy0ef59lny1 unique (YadaProduct_id, categories_id);
alter table YadaProduct_galleryImages add constraint UK_paqmx4kvpgkaxj40cxlbxnv9h unique (galleryImages_id);
alter table YadaProduct_subcategories add constraint UKamh3k42m45dww09m7a27b2qer unique (YadaProduct_id, subcategories_id);
alter table YadaSocialCredentials add constraint UK_1uppa4u7bksphbjwm4i2c8re9 unique (socialId);
alter table YadaUserCredentials add constraint UK_6gbgs7fb7g5t4wo0ys7e5q31j unique (username);
alter table YadaUserProfile add constraint UK_3bjn82k5gj41f9ocejoxx1uua unique (userCredentials_id);
alter table Article_finish add constraint FKo6fubwjyeog2emtnjtuq7btjp foreign key (Article_id) references YadaArticle (id);
alter table Article_language add constraint FKfq4gjjnbbj4jyaxakxt3qxjy0 foreign key (Article_id) references YadaArticle (id);
alter table Article_shopUrl add constraint FKpw3elbyx17a18wbffjpm6058i foreign key (Article_id) references YadaArticle (id);
alter table Article_tipology add constraint FK2b82udagdc06v548hwsmrnluw foreign key (Article_id) references YadaArticle (id);
alter table Dealer add constraint FKlw0522i674n6we0l1d0b0mx23 foreign key (nation_id) references Nation (id);
alter table Dealer add constraint FKt6b03i3xwqivg1crf5exbdddr foreign key (province_id) references YadaPersistentEnum (id);
alter table Dealer add constraint FKeayam05r95oivd0gsv2atc5f6 foreign key (region_id) references YadaPersistentEnum (id);
alter table Designer add constraint FK7x43xyhe7e7qykuxe76mbqwsq foreign key (image_id) references YadaAttachedFile (id);
alter table Designer_description add constraint FKp0rkhh2025yon97qmcab98qtc foreign key (Designer_id) references Designer (id);
alter table Designer_YadaProduct add constraint FKr4t170q75ytmq52xgeulmu5n0 foreign key (products_id) references YadaProduct (id);
alter table Designer_YadaProduct add constraint FKfspqe2iakr031nn1c9jwtwmlh foreign key (designers_id) references Designer (id);
alter table EventNews add constraint FKinrw6u4saudhng3cgu8lew96g foreign key (image_id) references YadaAttachedFile (id);
alter table EventNews add constraint FK8jl4ho8cvfgitxv5s9blqnw1g foreign key (thumbnail_id) references YadaAttachedFile (id);
alter table EventNews_content add constraint FKaks81bxr5v3xyg2u9bl5446h6 foreign key (EventNews_id) references EventNews (id);
alter table EventNews_subtitle add constraint FKgs0drsto3nbmbhdtcjw49lxad foreign key (EventNews_id) references EventNews (id);
alter table EventNews_title add constraint FKa980s3ul6ulta7qvwbqqb2g7 foreign key (EventNews_id) references EventNews (id);
alter table Nation_name add constraint FKs0kk0kyr6dio3kdsnmwq1me73 foreign key (Nation_id) references Nation (id);
alter table Product_color add constraint FK8kb4la4qyi42r0stojgt21nrd foreign key (Product_id) references YadaProduct (id);
alter table Product_contract add constraint FKkojye4iip4nnf25rg96ngi8pf foreign key (contract_id) references YadaPersistentEnum (id);
alter table Product_contract add constraint FKi8yy6if3qsqwvi5gnuswhekji foreign key (Product_id) references YadaProduct (id);
alter table Product_edition add constraint FK86t4ua3g1q5phadivfiow3rrc foreign key (Product_id) references YadaProduct (id);
alter table Product_files add constraint FKrdany1rpinj5st0tng4r9ql1w foreign key (files_id) references YadaAttachedFile (id);
alter table Product_files add constraint FKnfbxlmd7nkde1q9fbshjlxcp1 foreign key (Product_id) references YadaProduct (id);
alter table Product_files2d add constraint FK9rugve3nyt4dvu4rq2tt8kytk foreign key (files2d_id) references YadaAttachedFile (id);
alter table Product_files2d add constraint FKgsw53ifyv353lk5syrqn1ub2g foreign key (Product_id) references YadaProduct (id);
alter table Product_files3d add constraint FK6ohneiucackcyoq6bc0qlqy47 foreign key (files3d_id) references YadaAttachedFile (id);
alter table Product_files3d add constraint FK57wo8gg4ytnqhwede3o95nt8 foreign key (Product_id) references YadaProduct (id);
alter table Product_finish add constraint FK622wopgw45fovye4p4pcyi74c foreign key (Product_id) references YadaProduct (id);
alter table Product_technicalData add constraint FKn5al81tgp2unm82wu5ev1xvmy foreign key (Product_id) references YadaProduct (id);
alter table Product_tipology add constraint FK9ak6g2fe1kubphiu7nc0bpv4r foreign key (tipology_id) references YadaPersistentEnum (id);
alter table Product_tipology add constraint FKb522cbqwp0xi7bya6r42xmp07 foreign key (Product_id) references YadaProduct (id);
alter table Tag_EventNews add constraint FK3uovncidnoly7ac2a4xc9bf3i foreign key (eventNews_id) references EventNews (id);
alter table Tag_EventNews add constraint FKnmqt94tnl1jw4uou1xl7jcvyq foreign key (tags_id) references Tag (id);
alter table Tag_name add constraint FK98j8p1pa1lmkhrwyi1uf6oms9 foreign key (Tag_id) references Tag (id);
alter table Tag_YadaProduct add constraint FKsa4fp4jiklg8d1mupevgvlfr9 foreign key (products_id) references YadaProduct (id);
alter table Tag_YadaProduct add constraint FK8xaxw9kwaaniyt2c2sqyr632w foreign key (tags_id) references Tag (id);
alter table YadaAddress add constraint FK2eugd1latuu00dmevh5ae6d7l foreign key (owner_id) references YadaUserProfile (id);
alter table YadaArticle add constraint FKh7anw4lfm7mp9aad3aup32vea foreign key (image_id) references YadaAttachedFile (id);
alter table YadaArticle add constraint FK8pj59lffwoe4w2yk8xs0jirmv foreign key (product_id) references YadaProduct (id);
alter table YadaArticle add constraint FK26mg3pufoomtfhqvlg6osuc0h foreign key (silhouette_id) references YadaAttachedFile (id);
alter table YadaArticle_attachments add constraint FKo5i8wax1h5js7hlmqpl067cbd foreign key (attachments_id) references YadaAttachedFile (id);
alter table YadaArticle_attachments add constraint FKoj11oi1wdda3wac7ovyr9pf60 foreign key (YadaArticle_id) references YadaArticle (id);
alter table YadaArticle_color add constraint FKlvk7va9259rk9ksgxui806jj6 foreign key (YadaArticle_id) references YadaArticle (id);
alter table YadaArticle_galleryImages add constraint FKntidjedo6gwajgeymds0ujd7f foreign key (galleryImages_id) references YadaAttachedFile (id);
alter table YadaArticle_galleryImages add constraint FK946rcfq8u7ueugfdk4lpogb9y foreign key (YadaArticle_id) references YadaArticle (id);
alter table YadaArticle_name add constraint FKj9qpj48f710i50xcdxpddf7nj foreign key (YadaArticle_id) references YadaArticle (id);
alter table YadaArticle_silhouetteImages add constraint FKk6one6qlrw2xqp8wsj0jhail9 foreign key (silhouetteImages_id) references YadaAttachedFile (id);
alter table YadaArticle_silhouetteImages add constraint FKoeebgo3xu503nooo0piyv3nbi foreign key (YadaArticle_id) references YadaArticle (id);
alter table YadaAttachedFile_description add constraint FKj1954nnr3hu07yak1tyb4inc6 foreign key (YadaAttachedFile_id) references YadaAttachedFile (id);
alter table YadaAttachedFile_title add constraint FKqawwx1dakd1a91pxgappdycka foreign key (YadaAttachedFile_id) references YadaAttachedFile (id);
alter table YadaAutoLoginToken add constraint FKh92vo7me2k2s4v1x1jercpuo foreign key (yadaUserCredentials_id) references YadaUserCredentials (id);
alter table YadaCart add constraint FK4cloq1osjtfptmn6o5o6mxbr0 foreign key (owner_id) references YadaUserProfile (id);
alter table YadaCartItem add constraint FK3ah891yw72v4nr5skwvjndn8w foreign key (article_id) references YadaArticle (id);
alter table YadaCartItem add constraint FK632j50kx6vglg5kp3kpn155o4 foreign key (cart_id) references YadaCart (id);
alter table YadaJob add constraint FKbly4fv9jmbvwppy5b9x79yokq foreign key (jobStateObject_id) references YadaPersistentEnum (id);
alter table YadaJob_BeActive add constraint FKfcdajxue4qegy3sh412qcqd7 foreign key (jobsMustBeActive_id) references YadaJob (id);
alter table YadaJob_BeActive add constraint FKqhqlee0k5m0ir9s6kpw8m9y6d foreign key (YadaJob_id) references YadaJob (id);
alter table YadaJob_BeCompleted add constraint FK8o25xd851myc035dwd0xm7kpd foreign key (jobsMustComplete_id) references YadaJob (id);
alter table YadaJob_BeCompleted add constraint FKgcmntp7yy872ldenedb6nnyep foreign key (YadaJob_id) references YadaJob (id);
alter table YadaJob_BeInactive add constraint FK8yfnn9cj06lrptwbtnpnevp4h foreign key (jobsMustBeInactive_id) references YadaJob (id);
alter table YadaJob_BeInactive add constraint FKamylqhhgf9gjwsux3yfosvq52 foreign key (YadaJob_id) references YadaJob (id);
alter table YadaOrder add constraint FKf1vte0esagjxmtc62jj0k98ry foreign key (orderStatus_id) references YadaPersistentEnum (id);
alter table YadaOrder add constraint FK3u96txslj6i2343hhsh0rxb4m foreign key (owner_id) references YadaUserProfile (id);
alter table YadaOrderItem add constraint FKnjpsd15nt8csnpojhhd4jtpif foreign key (order_id) references YadaOrder (id);
alter table YadaPersistentEnum_langToText add constraint FKewmgshpqaehgfba9sp8pluddg foreign key (YadaPersistentEnum_id) references YadaPersistentEnum (id);
alter table YadaProduct add constraint FKen9btb905njlidk3auvewqrw0 foreign key (image_id) references YadaAttachedFile (id);
alter table YadaProduct add constraint FK9u86sjqsbchj1hulrbuma7db6 foreign key (silhouette_id) references YadaAttachedFile (id);
alter table YadaProduct add constraint FKgp0773i42mer33kwvpainsprm foreign key (wireframe_id) references YadaAttachedFile (id);
alter table YadaProduct_accessories add constraint FKc0fib48o5ohspki155jt5ijad foreign key (accessories_id) references YadaProduct (id);
alter table YadaProduct_accessories add constraint FKlrtfnagonn4o4j5y1no2qdn0p foreign key (accessoryOf_id) references YadaProduct (id);
alter table YadaProduct_attachments add constraint FK4tpdxk79xwmx79p9moiehi011 foreign key (attachments_id) references YadaAttachedFile (id);
alter table YadaProduct_attachments add constraint FKmrnigiegv3tpcjg1i0y7or2gs foreign key (YadaProduct_id) references YadaProduct (id);
alter table YadaProduct_categories add constraint FKpo9g7k6ryamt1npavvwpj1wdb foreign key (categories_id) references YadaPersistentEnum (id);
alter table YadaProduct_categories add constraint FK49lk07l13fw2ka8ag9nurfawa foreign key (YadaProduct_id) references YadaProduct (id);
alter table YadaProduct_description add constraint FK9rto4a8un9vt79me4qoq2p4sv foreign key (YadaProduct_id) references YadaProduct (id);
alter table YadaProduct_galleryImages add constraint FK8scjcwfj0kqbbosdei8hlcqon foreign key (galleryImages_id) references YadaAttachedFile (id);
alter table YadaProduct_galleryImages add constraint FKhalpejij8cy1n8w23lpac5yhu foreign key (YadaProduct_id) references YadaProduct (id);
alter table YadaProduct_materials add constraint FKet1a1eniesxql0b0p7xr1y876 foreign key (YadaProduct_id) references YadaProduct (id);
alter table YadaProduct_name add constraint FK56ctdnwejlx41fs5olg56j4hg foreign key (YadaProduct_id) references YadaProduct (id);
alter table YadaProduct_subcategories add constraint FKau9b0n8mnvm1dmobeo7omcqkl foreign key (subcategories_id) references YadaPersistentEnum (id);
alter table YadaProduct_subcategories add constraint FK1nnwcuaxq0bea0hgt5nqutn3x foreign key (YadaProduct_id) references YadaProduct (id);
alter table YadaProduct_subtitle add constraint FK2u6n4iq79g3vhl0h2v1322nns foreign key (YadaProduct_id) references YadaProduct (id);
alter table YadaRegistrationRequest add constraint FKkn2yxfy3t9fjmuannqfph49d0 foreign key (trattamentoDati_id) references YadaClause (id);
alter table YadaRegistrationRequest add constraint FKq6guqxscpqqq7pl96md1y79rn foreign key (yadaUserCredentials_id) references YadaUserCredentials (id);
alter table YadaSocialCredentials add constraint FK72s54ufexgh2xk2122ihkc82l foreign key (yadaUserCredentials_id) references YadaUserCredentials (id);
alter table YadaUserCredentials_roles add constraint FK1oj60uojdn4xql004wfe2v0hp foreign key (YadaUserCredentials_id) references YadaUserCredentials (id);
alter table YadaUserProfile add constraint FKm8x7qmacvae25wmfdhnuf4e25 foreign key (userCredentials_id) references YadaUserCredentials (id);
