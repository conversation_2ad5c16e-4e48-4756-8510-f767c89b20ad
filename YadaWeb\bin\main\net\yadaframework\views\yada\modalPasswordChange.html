<!DOCTYPE html>
<html xmlns:yada="http://yada.yodadog.net" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout">
<head>
	<meta charset="utf-8" />
</head>
<body>

<div th:replace="~{/yada/modalGenericB5::fragment(~{},~{::modalHeader},~{::modalBody},~{},~{::modalScript},~{},_)}">

	<th:block th:fragment="modalHeader">
		<div class="modal-header">
       		<h3 class="modal-title" id="ajaxModalTitle" th:text="#{yada.modal.passwordChange.title}">Cambio Password</h3>
       		<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
       	</div>
	</th:block>

	<div th:fragment="modalTitle"></div>

	<th:block th:fragment="modalBody">
		<div class="modal-body p-0">
			<div class="card-body">
				<form th:action="@{/passwordChangeAfterRequest}" th:object="${yadaFormPasswordChange}" role="form" method="post" class="yadaAjax mb-2">
					<fieldset class="has-feedback">
	   					<input th:field="*{token}" type="hidden" />
		   				<div th:include="~{/yada/form/b5/text :: body(fieldName='username',labelKey='yada.form.label.username',readonly=true)}"></div>
	   					<div th:replace="~{/yada/form/b5/text :: body(fieldName='password',labelKey='yada.form.label.password.change',type='password')}"></div>
					</fieldset>
  					<button class="btn btn-primary" type="submit" th:text="#{yada.modal.passwordChange.button}">Cambia Password</button>
  					<a th:href="@{/}" class="btn  btn-secondary" type="button" th:text="#{yada.modal.confirm.cancel}">Annulla</a>
				</form>
			</div>
   		</div>	
	</th:block>
	
	<script th:fragment="modalScript" th:inline="javascript">
	    yada.enableShowPassword();
		<!--/* In caso di successo oppure fatal error, chiude il modal e mostra quello corrispondente - REMOVED because useless
		if ([[${pwdChangeOk!=null}]]) {
			$("#ajaxModal").modal('hide');
			yada.showOkModal([[${YADA_NTITLE!=null?YADA_NTITLE.get(0):''}]], [[${YADA_NBODY!=null?YADA_NBODY.get(0):''}]], [[@{/}]]);
		}
		if ([[${fatalError!=null}]]) {
			$("#ajaxModal").modal('hide');
			yada.showErrorModal([[${YADA_NTITLE!=null?YADA_NTITLE.get(0):''}]], [[${YADA_NBODY!=null?YADA_NBODY.get(0):''}]], [[@{/passwordReset}]]);
		}
		 */-->
	</script> 
</div>
</body>
</html>
