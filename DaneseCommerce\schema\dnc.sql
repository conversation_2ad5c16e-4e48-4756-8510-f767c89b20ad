create table Article_finish (Article_id bigint not null, locale varchar(32) not null, finish varchar(255), primary key (Article_id, locale)) engine=InnoDB;
create table Article_language (Article_id bigint not null, locale varchar(32) not null, language varchar(255), primary key (Article_id, locale)) engine=InnoDB;
create table Article_shopUrl (Article_id bigint not null, locale varchar(32) not null, shopUrl varchar(255), primary key (Article_id, locale)) engine=InnoDB;
create table Article_tipology (Article_id bigint not null, tipology_id bigint not null) engine=InnoDB;
create table Dealer (agente bit not null, id bigint not null auto_increment, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, nation_id bigint, province_id bigint, region_id bigint, version bigint not null, address varchar(8192), email varchar(255), fax varchar(255), name varchar(255), phone varchar(255), primary key (id)) engine=InnoDB;
create table Designer (published bit not null, id bigint not null auto_increment, image_id bigint, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, version bigint not null, name varchar(64), surname varchar(64), primary key (id)) engine=InnoDB;
create table Designer_description (Designer_id bigint not null, locale varchar(32) not null, description varchar(8192), primary key (Designer_id, locale)) engine=InnoDB;
create table Designer_YadaProduct (designers_id bigint not null, products_id bigint not null) engine=InnoDB;
create table EventNews (evento bit not null, id bigint not null auto_increment, image_id bigint, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, publishDate datetime(6), thumbnail_id bigint, version bigint not null, video varchar(1024), primary key (id)) engine=InnoDB;
create table EventNews_content (EventNews_id bigint not null, locale varchar(32) not null, content varchar(8192), primary key (EventNews_id, locale)) engine=InnoDB;
create table EventNews_subtitle (EventNews_id bigint not null, locale varchar(32) not null, subtitle varchar(255), primary key (EventNews_id, locale)) engine=InnoDB;
create table EventNews_title (EventNews_id bigint not null, locale varchar(32) not null, title varchar(255), primary key (EventNews_id, locale)) engine=InnoDB;
create table Nation (id bigint not null auto_increment, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, version bigint not null, code varchar(255), primary key (id)) engine=InnoDB;
create table Nation_name (Nation_id bigint not null, locale varchar(32) not null, name varchar(255), primary key (Nation_id, locale)) engine=InnoDB;
create table Product_color (Product_id bigint not null, locale varchar(32) not null, color varchar(255), primary key (Product_id, locale)) engine=InnoDB;
create table Product_contract (Product_id bigint not null, contract_id bigint not null) engine=InnoDB;
create table Product_dimension (Product_id bigint not null, locale varchar(32) not null, dimension varchar(8192), primary key (Product_id, locale)) engine=InnoDB;
create table Product_edition (Product_id bigint not null, locale varchar(32) not null, edition varchar(255), primary key (Product_id, locale)) engine=InnoDB;
create table Product_files (Product_id bigint not null, files_id bigint not null) engine=InnoDB;
create table Product_files2d (Product_id bigint not null, files2d_id bigint not null) engine=InnoDB;
create table Product_files3d (Product_id bigint not null, files3d_id bigint not null) engine=InnoDB;
create table Product_finish (Product_id bigint not null, locale varchar(32) not null, finish varchar(255), primary key (Product_id, locale)) engine=InnoDB;
create table Product_notes (Product_id bigint not null, locale varchar(32) not null, notes varchar(8192), primary key (Product_id, locale)) engine=InnoDB;
create table Product_specs (Product_id bigint not null, locale varchar(32) not null, specs varchar(255), primary key (Product_id, locale)) engine=InnoDB;
create table Product_technicalData (Product_id bigint not null, locale varchar(32) not null, technicalData varchar(255), primary key (Product_id, locale)) engine=InnoDB;
create table Product_tipology (Product_id bigint not null, tipology_id bigint not null) engine=InnoDB;
create table Tag (id bigint not null auto_increment, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, version bigint not null, primary key (id)) engine=InnoDB;
create table Tag_EventNews (eventNews_id bigint not null, tags_id bigint not null) engine=InnoDB;
create table Tag_name (Tag_id bigint not null, locale varchar(32) not null, name varchar(255), primary key (Tag_id, locale)) engine=InnoDB;
create table Tag_YadaProduct (products_id bigint not null, tags_id bigint not null) engine=InnoDB;
create table YadaArticle (depth float(23), diameter float(23), elements integer, height float(23), length float(23), published bit not null, quantity integer, radius float(23), weight float(23), width float(23), id bigint not null auto_increment, image_id bigint, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, product_id bigint, silhouette_id bigint, unitPrice bigint, version bigint not null, size varchar(16), DTYPE varchar(31) not null, itemSize varchar(32), sku varchar(32), source varchar(64), internalName varchar(255), primary key (id)) engine=InnoDB;
create table YadaArticle_attachments (YadaArticle_id bigint not null, attachments_id bigint not null) engine=InnoDB;
create table YadaArticle_color (YadaArticle_id bigint not null, color varchar(32), locale varchar(32) not null, primary key (YadaArticle_id, locale)) engine=InnoDB;
create table YadaArticle_galleryImages (YadaArticle_id bigint not null, galleryImages_id bigint not null) engine=InnoDB;
create table YadaArticle_name (YadaArticle_id bigint not null, locale varchar(32) not null, name varchar(256), primary key (YadaArticle_id, locale)) engine=InnoDB;
create table YadaArticle_silhouetteImages (YadaArticle_id bigint not null, silhouetteImages_id bigint not null) engine=InnoDB;
create table YadaAttachedFile (height integer not null, heightDesktop integer, heightMobile integer, heightPdf integer, published bit not null, width integer not null, widthDesktop integer, widthMobile integer, widthPdf integer, id bigint not null auto_increment, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, sortOrder bigint not null, uploadTimestamp TIMESTAMP NULL null, version bigint not null, metadata varchar(1024), clientFilename varchar(255), filename varchar(255), filenameDesktop varchar(255), filenameMobile varchar(255), filenamePdf varchar(255), forLocale varchar(255), relativeFolderPath varchar(255), primary key (id)) engine=InnoDB;
create table YadaAttachedFile_description (YadaAttachedFile_id bigint not null, locale varchar(32) not null, description varchar(8192), primary key (YadaAttachedFile_id, locale)) engine=InnoDB;
create table YadaAttachedFile_title (YadaAttachedFile_id bigint not null, locale varchar(32) not null, title varchar(1024), primary key (YadaAttachedFile_id, locale)) engine=InnoDB;
create table YadaAutoLoginToken (expiration TIMESTAMP NULL null, id bigint not null auto_increment, timestamp TIMESTAMP NULL null, token bigint not null, version bigint not null, yadaUserCredentials_id bigint, primary key (id)) engine=InnoDB;
create table YadaBrowserId (id bigint not null auto_increment, leastSigBits bigint, mostSigBits bigint, version bigint not null, primary key (id)) engine=InnoDB;
create table YadaClause (clauseVersion integer not null, id bigint not null auto_increment, version bigint not null, name varchar(32) not null, content tinytext, primary key (id)) engine=InnoDB;
create table YadaJob (errorStreakCount integer not null, jobGroupPaused bit not null, jobPriority integer not null, jobRecoverable bit not null, id bigint not null auto_increment, jobLastSuccessfulRun TIMESTAMP NULL null, jobScheduledTime TIMESTAMP NULL null, jobStartTime TIMESTAMP NULL null, jobStateObject_id bigint, jobGroup varchar(128), jobName varchar(128), jobDescription varchar(256), primary key (id)) engine=InnoDB;
create table YadaJob_BeActive (YadaJob_id bigint not null, jobsMustBeActive_id bigint not null) engine=InnoDB;
create table YadaJob_BeCompleted (YadaJob_id bigint not null, jobsMustComplete_id bigint not null) engine=InnoDB;
create table YadaJob_BeInactive (YadaJob_id bigint not null, jobsMustBeInactive_id bigint not null) engine=InnoDB;
create table YadaPersistentEnum (enumOrdinal integer not null, id bigint not null auto_increment, enumClassName varchar(191) not null, enumName varchar(255) not null, primary key (id)) engine=InnoDB;
create table YadaPersistentEnum_langToText (YadaPersistentEnum_id bigint not null, language varchar(32) not null, localText varchar(128), primary key (YadaPersistentEnum_id, language)) engine=InnoDB;
create table YadaProduct (accessoryFlag bit not null, published bit not null, year integer not null, id bigint not null auto_increment, image_id bigint, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, silhouette_id bigint, version bigint not null, wireframe_id bigint, DTYPE varchar(31) not null, source varchar(64), productName varchar(255), primary key (id)) engine=InnoDB;
create table YadaProduct_accessories (accessories_id bigint not null, accessoryOf_id bigint not null) engine=InnoDB;
create table YadaProduct_attachments (YadaProduct_id bigint not null, attachments_id bigint not null) engine=InnoDB;
create table YadaProduct_categories (YadaProduct_id bigint not null, categories_id bigint not null) engine=InnoDB;
create table YadaProduct_description (YadaProduct_id bigint not null, locale varchar(32) not null, description varchar(8192), primary key (YadaProduct_id, locale)) engine=InnoDB;
create table YadaProduct_galleryImages (YadaProduct_id bigint not null, galleryImages_id bigint not null) engine=InnoDB;
create table YadaProduct_materials (YadaProduct_id bigint not null, locale varchar(32) not null, materials varchar(128), primary key (YadaProduct_id, locale)) engine=InnoDB;
create table YadaProduct_name (YadaProduct_id bigint not null, locale varchar(32) not null, name varchar(64), primary key (YadaProduct_id, locale)) engine=InnoDB;
create table YadaProduct_subcategories (YadaProduct_id bigint not null, subcategories_id bigint not null) engine=InnoDB;
create table YadaProduct_subtitle (YadaProduct_id bigint not null, locale varchar(32) not null, subtitle varchar(128), primary key (YadaProduct_id, locale)) engine=InnoDB;
create table YadaRegistrationRequest (registrationType tinyint check (registrationType between 0 and 3), id bigint not null auto_increment, timestamp TIMESTAMP NULL null, token bigint not null, trattamentoDati_id bigint, version bigint not null, yadaUserCredentials_id bigint, email varchar(64) not null, timezone varchar(64), password varchar(128) not null, primary key (id)) engine=InnoDB;
create table YadaSocialCredentials (type integer not null, id bigint not null auto_increment, version bigint not null, yadaUserCredentials_id bigint not null, email varchar(128) not null, socialId varchar(128) not null, primary key (id)) engine=InnoDB;
create table YadaUserCredentials (changePassword bit not null, enabled bit not null, failedAttempts integer not null, creationDate TIMESTAMP NULL null, id bigint not null auto_increment, lastFailedAttempt TIMESTAMP NULL null, lastSuccessfulLogin TIMESTAMP NULL null, passwordDate TIMESTAMP NULL null, version bigint not null, password varchar(128) not null, username varchar(128) not null, primary key (id)) engine=InnoDB;
create table YadaUserCredentials_roles (roles integer, YadaUserCredentials_id bigint not null) engine=InnoDB;
create table YadaUserProfile (timezoneSetByUser bit not null, avatar_id bigint, id bigint not null auto_increment, userCredentials_id bigint not null, version bigint not null, firstName varchar(32), locale varchar(32), middleName varchar(32), lastName varchar(64), timezone varchar(64), primary key (id)) engine=InnoDB;
alter table Article_tipology add constraint UKb03qdw5l641v0ja6mfu8ic16v unique (tipology_id, Article_id);
alter table Dealer add constraint UK_70ff6wwcy8mvnrk4jyc4w0ada unique (province_id);
alter table Dealer add constraint UK_8v61etyv6orq5py394myu3swm unique (region_id);
alter table Designer add constraint UK_dlg3ko6h7i4k7wsh4ptw7sx2y unique (image_id);
alter table Designer_YadaProduct add constraint UKbocerkkc9yc0dl4m3cggltq4y unique (designers_id, products_id);
alter table EventNews add constraint UK_nhewqhicxdykqmvyff72kw6m0 unique (image_id);
alter table EventNews add constraint UK_tcm3atckgyi38v0thpntcn0ep unique (thumbnail_id);
alter table Product_contract add constraint UKrqx2wakjbcyvs9k7b656kbtk7 unique (contract_id, Product_id);
alter table Product_files add constraint UK_cud4h711lvhwlpbf9posyf9d4 unique (files_id);
alter table Product_files2d add constraint UK_gbpe25w88hjm3qh40jkhnbbsp unique (files2d_id);
alter table Product_files3d add constraint UK_fnwnq93wym83mbf383ffxl3nw unique (files3d_id);
alter table Product_tipology add constraint UK8t9s4tvexh7xtw11mdoasop2c unique (tipology_id, Product_id);
alter table Tag_EventNews add constraint UK8vn7iw7npvwn1qpq6wevfigfh unique (tags_id, eventNews_id);
alter table Tag_name add constraint UK8a9en9qol3ngiqif6snwemlln unique (name, locale);
alter table Tag_YadaProduct add constraint UKsec1jhdelcrikh2uelamixue3 unique (tags_id, products_id);
alter table YadaArticle add constraint UK_7vechuxf3aovt7qyl8367akf6 unique (image_id);
alter table YadaArticle add constraint UK_apnx6xuksct1qvq9b4p414flg unique (silhouette_id);
alter table YadaArticle add constraint UK_nhcjr5g5me98n5drrkfg1p7rc unique (sku);
alter table YadaArticle_attachments add constraint UK_ckp9t4579t7mov1miiu8fql4f unique (attachments_id);
alter table YadaArticle_galleryImages add constraint UK_rl1gkdyfrt43m5jw3u598olgk unique (galleryImages_id);
alter table YadaArticle_silhouetteImages add constraint UK_sq5igv7krtxe04nyyhlx4djeg unique (silhouetteImages_id);
alter table YadaAutoLoginToken add constraint UK_gpwvvntka6p2tjtnut6qiokyi unique (yadaUserCredentials_id);
alter table YadaBrowserId add constraint UKlvfuna79iqujxpkn0l6xvirh4 unique (mostSigBits, leastSigBits);
alter table YadaClause add constraint UKek0brxiv78vf6idvd6dv8v69d unique (name, clauseVersion);
alter table YadaJob add constraint UK_dv98ftndf2nyk42cjoe50i36v unique (jobStateObject_id);
alter table YadaPersistentEnum add constraint UKfuc71vofqasw0r57t7etipp7p unique (enumClassName, enumOrdinal);
alter table YadaProduct add constraint UK_aaflxyksnnr4ut62k1khkw4co unique (image_id);
alter table YadaProduct add constraint UK_9l3895puoip6l23fbtc4ydeil unique (silhouette_id);
alter table YadaProduct add constraint UK_dbg31jwp3hpky08txr47oktd3 unique (wireframe_id);
alter table YadaProduct_attachments add constraint UK_fsu5mkfbdh7dvoq619df26n50 unique (attachments_id);
alter table YadaProduct_categories add constraint UK1cmx2j13ct60yhcy0ef59lny1 unique (YadaProduct_id, categories_id);
alter table YadaProduct_galleryImages add constraint UK_paqmx4kvpgkaxj40cxlbxnv9h unique (galleryImages_id);
alter table YadaProduct_subcategories add constraint UKamh3k42m45dww09m7a27b2qer unique (YadaProduct_id, subcategories_id);
alter table YadaRegistrationRequest add constraint UK_66pbcq6oohrfjqwr1o7wslucn unique (trattamentoDati_id);
alter table YadaRegistrationRequest add constraint UK_b0i98ixarlwa54gkxws9ykxae unique (yadaUserCredentials_id);
alter table YadaSocialCredentials add constraint UK_1uppa4u7bksphbjwm4i2c8re9 unique (socialId);
alter table YadaUserCredentials add constraint UK_6gbgs7fb7g5t4wo0ys7e5q31j unique (username);
alter table YadaUserProfile add constraint UK_q1b54fx8m1fu9budmpt2tm80i unique (avatar_id);
alter table YadaUserProfile add constraint UK_3bjn82k5gj41f9ocejoxx1uua unique (userCredentials_id);
alter table Article_finish add constraint FKo6fubwjyeog2emtnjtuq7btjp foreign key (Article_id) references YadaArticle (id);
alter table Article_language add constraint FKfq4gjjnbbj4jyaxakxt3qxjy0 foreign key (Article_id) references YadaArticle (id);
alter table Article_shopUrl add constraint FKpw3elbyx17a18wbffjpm6058i foreign key (Article_id) references YadaArticle (id);
alter table Article_tipology add constraint FKhpfvtm2r89ucbxlgtnms1tf5q foreign key (tipology_id) references YadaPersistentEnum (id);
alter table Article_tipology add constraint FK2b82udagdc06v548hwsmrnluw foreign key (Article_id) references YadaArticle (id);
alter table Dealer add constraint FKlw0522i674n6we0l1d0b0mx23 foreign key (nation_id) references Nation (id);
alter table Dealer add constraint FKt6b03i3xwqivg1crf5exbdddr foreign key (province_id) references YadaPersistentEnum (id);
alter table Dealer add constraint FKeayam05r95oivd0gsv2atc5f6 foreign key (region_id) references YadaPersistentEnum (id);
alter table Designer add constraint FK7x43xyhe7e7qykuxe76mbqwsq foreign key (image_id) references YadaAttachedFile (id);
alter table Designer_description add constraint FKp0rkhh2025yon97qmcab98qtc foreign key (Designer_id) references Designer (id);
alter table Designer_YadaProduct add constraint FKr4t170q75ytmq52xgeulmu5n0 foreign key (products_id) references YadaProduct (id);
alter table Designer_YadaProduct add constraint FKfspqe2iakr031nn1c9jwtwmlh foreign key (designers_id) references Designer (id);
alter table EventNews add constraint FKinrw6u4saudhng3cgu8lew96g foreign key (image_id) references YadaAttachedFile (id);
alter table EventNews add constraint FK8jl4ho8cvfgitxv5s9blqnw1g foreign key (thumbnail_id) references YadaAttachedFile (id);
alter table EventNews_content add constraint FKaks81bxr5v3xyg2u9bl5446h6 foreign key (EventNews_id) references EventNews (id);
alter table EventNews_subtitle add constraint FKgs0drsto3nbmbhdtcjw49lxad foreign key (EventNews_id) references EventNews (id);
alter table EventNews_title add constraint FKa980s3ul6ulta7qvwbqqb2g7 foreign key (EventNews_id) references EventNews (id);
alter table Nation_name add constraint FKs0kk0kyr6dio3kdsnmwq1me73 foreign key (Nation_id) references Nation (id);
alter table Product_color add constraint FK8kb4la4qyi42r0stojgt21nrd foreign key (Product_id) references YadaProduct (id);
alter table Product_contract add constraint FKkojye4iip4nnf25rg96ngi8pf foreign key (contract_id) references YadaPersistentEnum (id);
alter table Product_contract add constraint FKi8yy6if3qsqwvi5gnuswhekji foreign key (Product_id) references YadaProduct (id);
alter table Product_dimension add constraint FKiv96cwk49u3pufnch52ss9f8u foreign key (Product_id) references YadaProduct (id);
alter table Product_edition add constraint FK86t4ua3g1q5phadivfiow3rrc foreign key (Product_id) references YadaProduct (id);
alter table Product_files add constraint FKrdany1rpinj5st0tng4r9ql1w foreign key (files_id) references YadaAttachedFile (id);
alter table Product_files add constraint FKnfbxlmd7nkde1q9fbshjlxcp1 foreign key (Product_id) references YadaProduct (id);
alter table Product_files2d add constraint FK9rugve3nyt4dvu4rq2tt8kytk foreign key (files2d_id) references YadaAttachedFile (id);
alter table Product_files2d add constraint FKgsw53ifyv353lk5syrqn1ub2g foreign key (Product_id) references YadaProduct (id);
alter table Product_files3d add constraint FK6ohneiucackcyoq6bc0qlqy47 foreign key (files3d_id) references YadaAttachedFile (id);
alter table Product_files3d add constraint FK57wo8gg4ytnqhwede3o95nt8 foreign key (Product_id) references YadaProduct (id);
alter table Product_finish add constraint FK622wopgw45fovye4p4pcyi74c foreign key (Product_id) references YadaProduct (id);
alter table Product_notes add constraint FK37c2i8c4h12n33uvk99658ou6 foreign key (Product_id) references YadaProduct (id);
alter table Product_specs add constraint FKj901iuf672g6r3pfacw271vyy foreign key (Product_id) references YadaProduct (id);
alter table Product_technicalData add constraint FKn5al81tgp2unm82wu5ev1xvmy foreign key (Product_id) references YadaProduct (id);
alter table Product_tipology add constraint FK9ak6g2fe1kubphiu7nc0bpv4r foreign key (tipology_id) references YadaPersistentEnum (id);
alter table Product_tipology add constraint FKb522cbqwp0xi7bya6r42xmp07 foreign key (Product_id) references YadaProduct (id);
alter table Tag_EventNews add constraint FK3uovncidnoly7ac2a4xc9bf3i foreign key (eventNews_id) references EventNews (id);
alter table Tag_EventNews add constraint FKnmqt94tnl1jw4uou1xl7jcvyq foreign key (tags_id) references Tag (id);
alter table Tag_name add constraint FK98j8p1pa1lmkhrwyi1uf6oms9 foreign key (Tag_id) references Tag (id);
alter table Tag_YadaProduct add constraint FKsa4fp4jiklg8d1mupevgvlfr9 foreign key (products_id) references YadaProduct (id);
alter table Tag_YadaProduct add constraint FK8xaxw9kwaaniyt2c2sqyr632w foreign key (tags_id) references Tag (id);
alter table YadaArticle add constraint FKh7anw4lfm7mp9aad3aup32vea foreign key (image_id) references YadaAttachedFile (id);
alter table YadaArticle add constraint FK8pj59lffwoe4w2yk8xs0jirmv foreign key (product_id) references YadaProduct (id);
alter table YadaArticle add constraint FK26mg3pufoomtfhqvlg6osuc0h foreign key (silhouette_id) references YadaAttachedFile (id);
alter table YadaArticle_attachments add constraint FKo5i8wax1h5js7hlmqpl067cbd foreign key (attachments_id) references YadaAttachedFile (id);
alter table YadaArticle_attachments add constraint FKoj11oi1wdda3wac7ovyr9pf60 foreign key (YadaArticle_id) references YadaArticle (id);
alter table YadaArticle_color add constraint FKlvk7va9259rk9ksgxui806jj6 foreign key (YadaArticle_id) references YadaArticle (id);
alter table YadaArticle_galleryImages add constraint FKntidjedo6gwajgeymds0ujd7f foreign key (galleryImages_id) references YadaAttachedFile (id);
alter table YadaArticle_galleryImages add constraint FK946rcfq8u7ueugfdk4lpogb9y foreign key (YadaArticle_id) references YadaArticle (id);
alter table YadaArticle_name add constraint FKj9qpj48f710i50xcdxpddf7nj foreign key (YadaArticle_id) references YadaArticle (id);
alter table YadaArticle_silhouetteImages add constraint FKk6one6qlrw2xqp8wsj0jhail9 foreign key (silhouetteImages_id) references YadaAttachedFile (id);
alter table YadaArticle_silhouetteImages add constraint FKoeebgo3xu503nooo0piyv3nbi foreign key (YadaArticle_id) references YadaArticle (id);
alter table YadaAttachedFile_description add constraint FKj1954nnr3hu07yak1tyb4inc6 foreign key (YadaAttachedFile_id) references YadaAttachedFile (id);
alter table YadaAttachedFile_title add constraint FKqawwx1dakd1a91pxgappdycka foreign key (YadaAttachedFile_id) references YadaAttachedFile (id);
alter table YadaAutoLoginToken add constraint FKh92vo7me2k2s4v1x1jercpuo foreign key (yadaUserCredentials_id) references YadaUserCredentials (id);
alter table YadaJob add constraint FKbly4fv9jmbvwppy5b9x79yokq foreign key (jobStateObject_id) references YadaPersistentEnum (id);
alter table YadaJob_BeActive add constraint FKfcdajxue4qegy3sh412qcqd7 foreign key (jobsMustBeActive_id) references YadaJob (id);
alter table YadaJob_BeActive add constraint FKqhqlee0k5m0ir9s6kpw8m9y6d foreign key (YadaJob_id) references YadaJob (id);
alter table YadaJob_BeCompleted add constraint FK8o25xd851myc035dwd0xm7kpd foreign key (jobsMustComplete_id) references YadaJob (id);
alter table YadaJob_BeCompleted add constraint FKgcmntp7yy872ldenedb6nnyep foreign key (YadaJob_id) references YadaJob (id);
alter table YadaJob_BeInactive add constraint FK8yfnn9cj06lrptwbtnpnevp4h foreign key (jobsMustBeInactive_id) references YadaJob (id);
alter table YadaJob_BeInactive add constraint FKamylqhhgf9gjwsux3yfosvq52 foreign key (YadaJob_id) references YadaJob (id);
alter table YadaPersistentEnum_langToText add constraint FKewmgshpqaehgfba9sp8pluddg foreign key (YadaPersistentEnum_id) references YadaPersistentEnum (id);
alter table YadaProduct add constraint FKen9btb905njlidk3auvewqrw0 foreign key (image_id) references YadaAttachedFile (id);
alter table YadaProduct add constraint FK9u86sjqsbchj1hulrbuma7db6 foreign key (silhouette_id) references YadaAttachedFile (id);
alter table YadaProduct add constraint FKgp0773i42mer33kwvpainsprm foreign key (wireframe_id) references YadaAttachedFile (id);
alter table YadaProduct_accessories add constraint FKc0fib48o5ohspki155jt5ijad foreign key (accessories_id) references YadaProduct (id);
alter table YadaProduct_accessories add constraint FKlrtfnagonn4o4j5y1no2qdn0p foreign key (accessoryOf_id) references YadaProduct (id);
alter table YadaProduct_attachments add constraint FK4tpdxk79xwmx79p9moiehi011 foreign key (attachments_id) references YadaAttachedFile (id);
alter table YadaProduct_attachments add constraint FKmrnigiegv3tpcjg1i0y7or2gs foreign key (YadaProduct_id) references YadaProduct (id);
alter table YadaProduct_categories add constraint FKpo9g7k6ryamt1npavvwpj1wdb foreign key (categories_id) references YadaPersistentEnum (id);
alter table YadaProduct_categories add constraint FK49lk07l13fw2ka8ag9nurfawa foreign key (YadaProduct_id) references YadaProduct (id);
alter table YadaProduct_description add constraint FK9rto4a8un9vt79me4qoq2p4sv foreign key (YadaProduct_id) references YadaProduct (id);
alter table YadaProduct_galleryImages add constraint FK8scjcwfj0kqbbosdei8hlcqon foreign key (galleryImages_id) references YadaAttachedFile (id);
alter table YadaProduct_galleryImages add constraint FKhalpejij8cy1n8w23lpac5yhu foreign key (YadaProduct_id) references YadaProduct (id);
alter table YadaProduct_materials add constraint FKet1a1eniesxql0b0p7xr1y876 foreign key (YadaProduct_id) references YadaProduct (id);
alter table YadaProduct_name add constraint FK56ctdnwejlx41fs5olg56j4hg foreign key (YadaProduct_id) references YadaProduct (id);
alter table YadaProduct_subcategories add constraint FKau9b0n8mnvm1dmobeo7omcqkl foreign key (subcategories_id) references YadaPersistentEnum (id);
alter table YadaProduct_subcategories add constraint FK1nnwcuaxq0bea0hgt5nqutn3x foreign key (YadaProduct_id) references YadaProduct (id);
alter table YadaProduct_subtitle add constraint FK2u6n4iq79g3vhl0h2v1322nns foreign key (YadaProduct_id) references YadaProduct (id);
alter table YadaRegistrationRequest add constraint FKkn2yxfy3t9fjmuannqfph49d0 foreign key (trattamentoDati_id) references YadaClause (id);
alter table YadaRegistrationRequest add constraint FKq6guqxscpqqq7pl96md1y79rn foreign key (yadaUserCredentials_id) references YadaUserCredentials (id);
alter table YadaSocialCredentials add constraint FK72s54ufexgh2xk2122ihkc82l foreign key (yadaUserCredentials_id) references YadaUserCredentials (id);
alter table YadaUserCredentials_roles add constraint FK1oj60uojdn4xql004wfe2v0hp foreign key (YadaUserCredentials_id) references YadaUserCredentials (id);
alter table YadaUserProfile add constraint FKpi28ogwa7vguwb3vv1tkmpovi foreign key (avatar_id) references YadaAttachedFile (id);
alter table YadaUserProfile add constraint FKm8x7qmacvae25wmfdhnuf4e25 foreign key (userCredentials_id) references YadaUserCredentials (id);
