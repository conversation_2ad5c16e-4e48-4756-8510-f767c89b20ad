package com.danesemilano.persistence.entity;

import java.util.Locale;

import org.springframework.context.MessageSource;

import net.yadaframework.core.YadaLocalEnum;
import net.yadaframework.persistence.entity.YadaPersistentEnum;

public enum EnumSubcategory implements YadaL<PERSON>alEnum<EnumSubcategory> {
	// ATTENZIONE: aggiungere eventuali nuovi valori alla fine!
	CENTREPIECE,
	VASE,
	VASE_BOWL,
	G<PERSON><PERSON>ES_AND_PITCHER,
	CONTAINER,
	TRAY_FRUIT_BOWL,
	TRAY,
	FRUIT_BOWL,
	CANDLE_HOLDER,
	ASHTRAY,
	FLOOR_ASHTRAY,
	FLOOR_ASHTRAY_WASTEPAPER_BASKET,
	ASHTRAY_CENTREPIECE,
	PENCIL_HOLDER_PAPERCLIP_HOLDER,
	DESK_SET,
	PENCIL_HOLDER,
	PAPER_TRAY,
	PENCIL_HOLDER_PAPERTRAY,
	BOX,
	LETTER_OPENER,
	B<PERSON><PERSON><PERSON>RK,
	PERPETUAL_CALENDAR,
	PER<PERSON><PERSON><PERSON><PERSON>_WALL_CALENDAR,
	WASTEPAPER_BASKET,
	WASTEPAPER_BASKET_UMBRELLA_STAND,
	COAT_HANGER_CONTAINER_UMBRELLA_STAND,
	COAT_HANGER_COAT_HANGER_WITH_UMBRELLA_STAND,
	HANGER,
	COAT_HANGER_BARRIER_SYSTEM_TABLES_UMBRELLA_STAND,
	COAT_HANGER_ADJUSTABLE_TABLES_UMBRELLA_STAND,
	UMBRELLA_STAND_FLOOR_ASHTRAY,
	TABLE,
	DESK,
	COAT_HANGER,
	FLOOR_LAMP,
	BARRIER_SYSTEM,
	BOOK_STAND,
	BOOKCASE,
	SELF_STANDING_BOOKCASE,
	SHELF,
	TABLE_WALL_BOOK_STAND,
	CHAIR,
	MULTIPURPOSE_STORAGE_UNIT,
	MULTIPURPOSE_CART,
	LOUNGE_CHAIR_CHAIR,
	SIDE_TABLE,
	TABLE_LAMP,
	SUSPENSION_FLOOR_LAMP,
	SUSPENSION_LAMP,
	WALL_CEILING_LAMP,
	TABLE_FLOOR_LAMP,
	WALL_LAMP,
	VASE_SYSTEM_FOR_HYDROPONIC_CULTIVATION,
	FRAME,
	TWO_THREE_D_FRAME,
	SILKSCREEN_ART_PRINTS,
	FRAMED_CLOCK,
	FRAMED_MIRROR,
	PUZZLE,
	CANE,
	PITCHER,
	COAT_AND_UMBRELLA_STANDS,
	BUCKET,
	SALAD_SERVERS,
	ICE_CUBE_TRAY,
	FINGER_JEWEL,
	NOTEBOOK,
	PITCHER_VASE_CONTAINER,
	WATCHES,
	WALL_CLOCK_WITH_MAGNETIC_NUMBERS,
	PAPERWEIGHT,
	DESK_PAD,
	STOOL,
	BENCH,
	ART_EDITION,
	SCULPTURE,
	GLASS,
	DOORSTOP,
	PAPERWEIGHT_DOORSTOP,
	BIRDFEEDERWATERBATH,
	WATERBATH,
	BIRDFEEDER,
	BOWLPLATE,
	BOWL,
	PLATE
	// Aggiungere qui nuovi valori
	;
	private static String MESSAGES_PREFIX = "enum.subcategory.";
	
	private YadaPersistentEnum<EnumSubcategory> yadaPersistentEnum;
	
	public YadaPersistentEnum<EnumSubcategory> toYadaPersistentEnum() {
		return yadaPersistentEnum;
	}
	
	// TODO fix generics
	public void setYadaPersistentEnum(YadaPersistentEnum yadaPersistentEnum) {
		this.yadaPersistentEnum = yadaPersistentEnum;
	}
	
	/**
	 * Return the localized text for this enum
	 * @param messageSource
	 * @param locale
	 * @return
	 */
	public String toString(MessageSource messageSource, Locale locale) {
		return messageSource.getMessage(MESSAGES_PREFIX + name().toLowerCase(), null, locale);
	}

}
