<!DOCTYPE html>
<html th:with="activePage=History" 
	th:lang="${#locale.language}"
	xmlns:th="http://www.thymeleaf.org" 
	xmlns:yada="http://www.yadaframework.net">
    <head>
       <meta charset="UTF-8"> <!--/* This is needed so that Eclipse sets the correct character set */-->
		<th:block th:replace="~{/header :: head}"/>
    </head>
    <body class="history">
        <!-- Header/menu per fare replace dalla pagina del header -->
        <th:block th:replace="~{/header :: body}"/>

        <div class="page-containar logo-containar">
            <div class="container-fluid title-box" >

		       <th:block th:replace="/history/history-logo :: logo-fragment"></th:block>

            </div>
        </div>
        <div class="page-containar hidden-lg hidden-md visible-sm visible-xs elementVisibleSM" id="decade">
            <div class="container-fluid title-box" >
                <div  class="col-xs-10ths decade">
                    <div class="decade-wrapper">
                        <div class="decade-container"></div>
                    </div>
                </div>   
               
            </div>
        </div>
        <div class="page-containar hidden-lg hidden-md visible-sm visible-xs mob-sticky elementVisibleSM">
            <div class="container-fluid" >
                <div class="col-xs-10ths ">
                    <div class="history-box">
                        <div class="product-header">
                            <span class="fs25">2000—2009</span><br>
                        </div>
                        <p class="histor-description" th:utext="#{history.20002009}">
                            La guida artistica e manageriale dell’architetto
                            de Bevilacqua ha impresso un’accelerazione a
                            tutta l’azienda. Il suo contributo si è espresso
                            in una spinta d’innovazione che ha soprattutto
                            incluso uno sguardo consapevole nei confronti
                            delle nuove sfide tecnologiche con riferimento
                            soprattutto all’illuminotecnica. È lei che con volontà
                            ha individuato nella sostenibilità ambientale una
                            necessità e nel coinvolgimento di firme internazionali
                            quello scambio tra saperi e estetiche che
                            caratterizzerà il futuro di Danese Milano. Intanto sono
                            entrati in scuderia innovatori come James Irvine,
                            Paolo Rizzatto, Yves Behar, progettisti intellettuali
                            come Marco Ferreri, nuove voci come Francisco
                            Gomez Paz.
                        </p>
                    </div>  
                    <div class="history-box ">
                        <img th:src="@{/contents/media/history/45.jpg}" class="img-responsive content-hisory-block" alt="Danese Milano">   
                    </div> 
                    <div class="history-box col-xs-12 block-history">
                        <div class="row fill-history-box">
                            <div class="block-2x-horizontal odd-history history-block-content history-box-content">
                                 <p class="histor-description" th:utext="#{history.ina}">
                                    Carlotta de Bevilacqua<br>
                                    con un esemplare<br>
                                    della lampada Ina. Alle<br>
                                    sue spalle alcuni dei<br>
                                    ”Simboli Sinsemantici”<br>
                                    di Enzo Mari.
                                </p>
                            </div>
                            <div class="block-2x-horizontal even-history history-block-content history-box-content">
                                <p class="histor-description bottom-right-content" th:utext="#{history.prototipazione}">
                                    Dall’inizio degli anni<br>
                                    2000 il piano -1 del<br>
                                    quartier generale<br>
                                    di Danese Milano<br>
                                    è un laboratorio di<br>
                                    prototipazione e<br>
                                    sviluppo prodotto.
                                </p>
                            </div>
                        </div>
                        
                    </div>
                    <div class="history-box ">
                        <img th:src="@{/contents/media/history/46.jpg}" class="img-responsive content-hisory-block" alt="Danese Milano">   
                    </div> 
                    <div class="history-box col-xs-12 block-history">
                        <div class="row fill-history-box">
                            <div class="block-2x-horizontal odd-history history-block-content history-box-content">
                                 <p class="histor-description bottom-left-content" th:text="#{history.20002009.EnzoMari}">
                                    Enzo Mari è il riferimento<br>
                                    di Danese Milano.<br>
                                    Carlotta de Bevilacqua<br>
                                    intrattiene con il maestro<br>
                                    un rapporto speciale<br>
                                    di confronto e scambio<br>
                                    creativo.
                                </p>
                            </div>
                            <div class="block-2x-horizontal even-history history-block-content history-box-content">
                                
                            </div>
                        </div>
                        
                    </div>
                    <div class="history-box ">
                        <img th:src="@{/contents/media/history/47.jpg}" class="img-responsive content-hisory-block" alt="Danese Milano">   
                    </div>
                    <div class="history-box col-xs-12 block-history">
                        <div class="row fill-history-box">
                            <div class="block-2x-horizontal odd-history history-block-content history-box-content">
                                 
                            </div>
                            <div class="block-2x-horizontal even-history history-block-content history-box-content">
                                <p class="histor-description bottom-right-content" th:text="#{history.20002009.svedese}">
                                    Il designer svedese<br>
                                    Sami Rintala sviluppò<br>
                                    un micro interno mobile<br>
                                    per Danese Milano.<br>
                                    Qui con Carlotta de<br>
                                    Bevilacqua negli spazi di<br>
                                    Villa Danese a Milano.
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="history-box ">
                        <img th:src="@{/contents/media/history/48.jpg}" class="img-responsive content-hisory-block" alt="Danese Milano">   
                    </div>
                    <div class="history-box col-xs-12 ">
                        <div class="row">
                            <div class="block-2x-horizontal odd-history history-block-content block-2x-vertical">
                                <div class="col-sm-10ths history-box content-block-vertical">
                                    
                                </div>
                                <div class="col-sm-10ths content-block-vertical ">
                                    <p class="histor-description bottom-left-content" th:text="#{history.20002009.Francisco}">
                                        In azienda Francisco<br>
                                        Gomez Paz ha<br>
                                        mosso alcuni passi<br>
                                        fondamentali della sua<br>
                                        carriera di progettista.
                                    </p>
                                </div>
                            </div>
                            <div class="block-2x-horizontal even-history history-box-content">
                                <img th:src="@{/contents/media/history/49.jpg}" class="img-responsive content-hisory-block" alt="Danese Milano"> 
                            </div>
                        </div>
                    </div>
                     
                    <div class="history-box col-xs-12 block-history">
                        <div class="row fill-history-box">
                            <div class="block-2x-horizontal odd-history history-block-content history-box-content">
                                
                            </div>
                            <div class="block-2x-horizontal even-history history-block-content history-box-content">
                                
                            </div>
                        </div>
                        
                    </div>
                    
                    
                   <div class="col-xs-12 hidden visible-xs visible-sm no-padding-left no-padding-right elementVisibleSM">
                           <div class="product-content center-block">
                                <a class="icon-big " th:href="@{/history_2010_2016}">
                                    <span class="icon-waves"></span>
                                </a> 
                                <!-- <a class="icon-big " href="#">
                                    <span class="icon-uparrow"></span>
                                </a> -->
                        </div>
                </div>   
            </div>
        </div>
        </div>

         <div class="page-containar hidden-sm hidden-xs visible-lg visible-md elementHiddenSM">
            <div class="container-fluid" >
                <div class="history-slider-main-box history-main-box history-desk-containar" >
                    <div class="hidden-xs hidden-sm elementHiddenSM">
                        <a class="prev-scroll-content icon-big "><span class="icon-forward"></span></a>
                    </div>   
                    <div class="slider-block history-block">
                        <div class="slideshow-container">
                            <div class="">
                                <div class="" id="content">
                                    <div class="history-box-desk history-block-right history-box-desk-2x">
                                        <div class="product-header zeroPaddingLeft">
                                            <span class="fs25">2000—2009</span><br>
                                        </div>
                                        <p class="histor-description zeroPaddingLeft" th:utext="#{history.20002009}">
                                            La guida artistica e manageriale dell’architetto
                                            de Bevilacqua ha impresso un’accelerazione a
                                            tutta l’azienda. Il suo contributo si è espresso
                                            in una spinta d’innovazione che ha soprattutto
                                            incluso uno sguardo consapevole nei confronti
                                            delle nuove sfide tecnologiche con riferimento
                                            soprattutto all’illuminotecnica. È lei che con
                                            volontà ha individuato nella sostenibilità
                                            ambientale una necessità e nel coinvolgimento
                                            di firme internazionali quello scambio tra
                                            saperi e estetiche che caratterizzerà il futuro di
                                            Danese Milano. Intanto sono entrati in scuderia
                                            innovatori come James Irvine, Paolo Rizzatto,
                                            Yves Behar, progettisti intellettuali come Marco
                                            Ferreri, nuove voci come Francisco Gomez Paz.
                                        </p>
                                    </div>
                                    <div class="history-box-desk history-block-right history-box-desk-2x">
                                        <img th:src="@{/contents/media/history/45.jpg}" class="img-responsive content-hisory-block" alt="Danese Milano">   
                                    </div>
                                    <div class="history-box-desk history-block-right history-box-desk-1x">
                                        <div class="history-box-desk-v2x col-lg-10ths col-md-10ths history-block-bottom">
                                            <p class="histor-description history-text-left news-block-desciption" 
                                            	th:utext="#{history.ina}">
                                                Carlotta de Bevilacqua<br>
                                                con un esemplare<br>
                                                della lampada Ina. Alle<br>
                                                sue spalle alcuni dei<br>
                                                ”Simboli Sinsemantici”<br>
                                                di Enzo Mari.

                                            </p>    
                                        </div>
                                       <div class="history-box-desk-v2x col-lg-10ths col-md-10ths">
                                             
                                       </div>
                                    </div>
                                    <div class="history-box-desk-2x history-block-right  ">
                                       <div class="history-box-desk-v2x col-lg-10ths col-md-10ths history-block-bottom ">
                                          
                                            <div class="history-box-desk-v2x col-lg-10ths col-md-10ths history-box-desk-1x history-block-right ">
                                                     
                                            </div>
                                            <div class="history-box-desk-v2x col-lg-10ths col-md-10ths history-box-desk-1x">
                                                <p class="histor-description history-text-left" th:utext="#{history.prototipazione}">
                                                    Dall’inizio degli anni<br>
                                                    2000 il piano -1 del<br>
                                                    quartier generale<br>
                                                    di Danese Milano<br>
                                                    è un laboratorio di<br>
                                                    prototipazione e<br>
                                                    sviluppo prodotto.

                                                </p>    
                                            </div>
                                        </div> 
                                        <div class="history-box-desk-v2x col-lg-10ths col-md-10ths history-box-desk-2x ">
                                        	<img th:src="@{/contents/media/history/46.jpg}" class="img-responsive content-hisory-block" alt="Danese Milano">
                                        </div>                             
                                    </div>
                                    <div class="history-box-desk history-block-right history-box-desk-1x">
                                        <div class="history-box-desk-v2x col-lg-10ths col-md-10ths history-block-bottom">
                                               
                                        </div>
                                       <div class="history-box-desk-v2x col-lg-10ths col-md-10ths">
                                             <p class="histor-description history-text-bottom-left news-block-desciption"
                                             	th:text="#{history.20002009.EnzoMari}">
                                                Enzo Mari è il<br>
                                                riferimento di Danese<br>
                                                Milano. Carlotta de<br>
                                                Bevilacqua intrattiene<br>
                                                con il maestro un<br>
                                                rapporto speciale di<br>
                                                confronto e scambio<br>
                                                creativo
                                            </p> 
                                       </div>
                                    </div>
                                    <div class="history-box-desk history-block-right history-box-desk-3x">
                                        <img th:src="@{/contents/media/history/47.jpg}" class="img-responsive content-hisory-block" alt="Danese Milano">   
                                    </div>
                                    <div class="history-box-desk-2x history-block-right">
                                        <div class="history-box-desk-v2x col-lg-10ths col-md-10ths history-block-bottom ">
                                            <img th:src="@{/contents/media/history/48.jpg}" class="img-responsive content-hisory-block" alt="Danese Milano"> 
                                        </div>
                                        <div class="history-box-desk-v2x col-lg-10ths col-md-10ths row">
                                            <div class="history-box-desk-v2x col-lg-10ths col-md-10ths history-box-desk-1x history-block-right">
                                               
                                            </div>
                                            
                                            <div class="history-box-desk-v2x col-lg-10ths col-md-10ths history-box-desk-1x">
                                                <p class="histor-description history-text-bottom-left news-block-desciption"
                                                th:text="#{history.20002009.svedese}">
                                                    Il designer svedese<br>
                                                    Sami Rintala sviluppò<br>
                                                    un micro interno mobile<br>
                                                    per Danese Milano.<br>
                                                    Qui con Carlotta de<br>
                                                    Bevilacqua negli spazi<br>
                                                    di Villa Danese a<br>
                                                    Milano.
                                                </p>       
                                            </div>
                                        </div>
                                    </div>
                                    <div class="history-box-desk history-block-right history-box-desk-1x" >
                                        <div class="history-box-desk-v2x col-lg-10ths col-md-10ths history-block-bottom">
                                            <p class="histor-description history-text-left"
                                            	th:text="#{history.20002009.Francisco}">
                                                In azienda Francisco<br>
                                                Gomez Paz ha<br>
                                                mosso alcuni passi<br>
                                                fondamentali della sua<br>
                                                carriera di progettista.

                                            </p>    
                                        </div>
                                       <div class="history-box-desk-v2x col-lg-10ths col-md-10ths">
                                             
                                       </div>
                                    </div>
                                   
                                    <div class="history-box-desk history-block-right history-box-desk-1x">
                                        <img th:src="@{/contents/media/history/49.jpg}" class="img-responsive content-hisory-block" alt="Danese Milano">   
                                        
                                    </div>
                                    <div class="history-box-desk history-block-right history-box-desk-1x">
                                        <div class="history-box-desk-v2x col-lg-10ths col-md-10ths history-block-bottom ">
                                             
                                        </div>
                                       <div class="history-box-desk-v2x col-lg-10ths col-md-10ths">
                                              
                                       </div>
                                    </div>
                                    
                                    
                                </div>
                            </div>
                           
                        </div>


                    </div>
                    <div class="hidden-xs hidden-sm elementHiddenSM">
                        <a class="icon-big next-scroll-content"  ><span class="icon-forward"></span></a>
                    </div>
                </div>
               
            </div>
        </div> 
        <div class="page-containar visible-lg visible-md hidden-sm hidden-xs elementHiddenSM" id="decade">
            <div class="container-fluid title-box" >
                <div  class="col-xs-10ths decade  "  id="decade">
                    <div class="decade-wrapper">
                        <div class="decade-container decade-desktop"></div>
                    </div>
                </div>
               
            </div>
        </div>
        
       <th:block th:replace="~{/footer :: body}"/>

        <script type="text/javascript" th:inline="javascript">
		        var isClick = false;
		        function startTouch(dataIds) {
		            $('.decade-container').SHorizontal('rollTo', dataIds);
		            isClick = false;
		        }
		        var totalDecade =8;
		        var currentDecade =2;
		        var dataId = 3;
		        var decadeWidth=100;
		        var decadeIsDesktop= true;
		        
		        var slideWidth = 0;
		        var productSlider = 0;
		        var scrolledContent = 0;
		        
		        var locationHref = window.location.href;
		        locationHref = locationHref.substring(0, locationHref.lastIndexOf('/')) + "/";
		        decadeBox();
		         $(window).resize(function () {
		            decadeBox();
		            productBox();
		        });
		       
		       $('.next-scroll-content').click(function () {
		                if (scrolledContent < productSlider)
		                {
		
		                    var currentScroll = productSlider - scrolledContent;
		
		                    if (currentScroll > slideWidth)
		                        currentScroll = slideWidth;
		
		                    scrolledContent = scrolledContent + currentScroll;
		                    view.stop(false, true).animate(
		                            {left: "-=" + currentScroll + "px"},
		                    {duration: 600});
		//                    view.scrollLeft(currentScroll+ "px");
		                }
		                else
		                {
		                    if (currentDecade > 0)
		                    {
		                        currentDecade = currentDecade - 1;
		                        $('.decade-container').SHorizontal('rollTo', currentDecade);
		                    }
		                }
		
		            });
		
		            $('.prev-scroll-content').click(function () {
		                var currentPosition = -parseInt(view.css("left"));
		                var currentScroll = slideWidth;
		                if (currentPosition < slideWidth)
		                    currentScroll = currentPosition;
		
		                if (currentPosition > 0)
		                {
		                    scrolledContent = scrolledContent - currentScroll;
		                    view.stop(false, true).animate(
		                            {left: "+=" + currentScroll + "px"},
		                    {duration: 600});
		                }
		                else
		                {
		                    if (currentDecade < 7)
		                    {
		                        currentDecade = currentDecade + 1;
		                        $('.decade-container').SHorizontal('rollTo', currentDecade);
		                    }
		                }
		            });
		
		    productBox();
		    function productBox()
		    {
		        var productBoxSize = $('.block-history').width();
		        var sliderBoxWidth = $('.slideshow-container').width();
		        var viewSize = window.matchMedia("(max-width: 991px)");
		        if (viewSize.matches) {
		            productBoxSize = productBoxSize / 2;
		            $('.block-history').css("height", productBoxSize);
		            decadeIsDesktop = false;
		        }
		        else
		        {
		            decadeIsDesktop = true;
		        }    
		    }
		    
		    
		        function decadeBox()
		        {
		            decadeWidth = $('.decade-container').outerWidth();
		            decadeWidth = decadeWidth/2.5;
		            
		        }
		        var viewSize = window.matchMedia("(max-width: 767px)");
		        if (!viewSize.matches) 
		            decadeWidth = 200;
		                
		        var isClick =false;    
		        var pageId="0";    
		        $('.decade-container').SHorizontal({
		             items: [
		                        '<div class="internal" ontouchstart="startTouch(0);" data-id="0" data-href="history_2017"><div class="center-line"></div><div class="dot-line"><div>2017</div> <div class="divider-line"></div><div>...</div></div></div>',
		                        '<div class="internal" ontouchstart="startTouch(1);" data-id="1" data-href="history_2010_2016"><div class="center-line"></div><div class="dot-line"><div>2010</div> <div class="divider-line"></div><div>2016</div></div></div>',
		                        '<div class="internal" ontouchstart="startTouch(2);" data-id="2" data-href="history_2000_2009"><div class="center-line"></div><div class="dot-line"><div>2000</div> <div class="divider-line"></div><div>2009</div></div></div>',
		                        '<div class="internal" ontouchstart="startTouch(3);" data-id="3" data-href="history_1990_1999"><div class="center-line"></div><div class="dot-line"><div>1990</div> <div class="divider-line"></div><div>1999</div></div></div>',
		                        '<div class="internal" ontouchstart="startTouch(4);" data-id="4" data-href="history_1980_1989"><div class="center-line"></div><div class="dot-line"><div>1980</div> <div class="divider-line"></div><div>1989</div></div></div>',
		                        '<div class="internal" ontouchstart="startTouch(5);" data-id="5" data-href="history_1970_1979"><div class="center-line"></div><div class="dot-line"><div>1970</div> <div class="divider-line"></div><div>1979</div></div></div>',
		                        '<div class="internal" ontouchstart="startTouch(6);" data-id="6" data-href="history_1957_1969"><div class="center-line"></div><div class="dot-line"><div>1957</div> <div class="divider-line"></div><div>1969</div></div></div>',
		                        '<div class="internal" ontouchstart="startTouch(7);" data-id="7" data-href="history"><div class="center-line"></div><div class="dot-line"><div>Intro</div> <div class="divider-line"></div><div>&nbsp;</div></div></div>'],
		            center: '2',
		            distance: decadeWidth,
		            displayed_length: 1,
		            angle: 40,
		            rotation: 0,
		            item_height: 0,
		        }).on('horizontal_scroll', function (e, index) {
		               if(isClick == false)
		               {
		                    if(decadeIsDesktop)
		                    {
		                        if ($('.decade-desktop div.SHorizontal-item').hasClass("SHorizontal-item-selected"))
		                        {
		                            var dataHref = $('.decade-desktop div.SHorizontal-item-selected ').children(".internal").attr("data-href");
		                            $(window).attr('location',[[@{/}]] +dataHref)
		                        }
		                    }
		                    else
		                    {
		                        if ($('div.SHorizontal-item').hasClass("SHorizontal-item-selected"))
		                        {
		                            var dataHref = $('div.SHorizontal-item-selected ').children(".internal").attr("data-href");
		                            $(window).attr('location',[[@{/}]] +dataHref)
		                        }
		                    }
		                }
		                else
		                {
		                     isClick = false;
		                }
		        });
		         $('.internal').on('click',function(){
		                isClick = true;
		                var dataId =  $(this).attr("data-id");
		                $('.decade-container').SHorizontal('rollTo',dataId);     
		         });
		         
		        window.onscroll = function() {decadeSticky()};
		        var navbar = document.getElementById("decade");
		        var sticky = navbar.offsetTop;
		        function decadeSticky() {
		          if (window.pageYOffset >= sticky) {
		            navbar.classList.add("sticky");
		             $(".mob-sticky").addClass("mob-sticky-scroll");
		          } else {
		            navbar.classList.remove("sticky");
		            $(".mob-sticky").removeClass("mob-sticky-scroll");
		          }
		        }
		        
		        $("#content").smoothTouchScroll({ continuousScrolling: true });
		        $(".scrollableArea ").addClass("row");
		        slideWidth = $('#content').outerWidth();
		        productSlider = $('.scrollableArea').outerWidth();
		        view = $(".scrollableArea");
		        scrolledContent = slideWidth;
				
		        $(".history-box-desk").css("visibility", "visible");
                $(".history-block-right").css("visibility", "visible");
		</script>
	</body>
</html>