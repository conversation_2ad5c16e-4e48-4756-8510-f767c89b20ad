<header class="page__header">
  {% include 'breadcrumb' %}

  <div class="page__header-wrapper">
    <div class="container">
      <h1 class="page__title">{{ 'customer.reset_password.title' | t }}</h1>
    </div>
  </div>
</header>

<div class="page__content">
  <section class="reset-password-form">
    <div class="container container--shrink">
      {% form 'reset_customer_password' %}
      {% if form.errors %}
        <div class="alert alert--error">
          <span class="alert__title">{{ 'general.forms.errors' | t }}</span>
          {% include 'form-errors' %}
        </div>
      {% endif %}

      <div class="form__control {% if form.errors.message.password %}form__control--error{% endif %}">
        <label class="form__label" for="customer__password">{{ 'customer.reset_password.password' | t }}</label>
        <input type="password" id="customer__password" name="customer[password]" required="required">
      </div>

      <div class="form__control {% if form.errors.message.password_confirmation %}form__control--error{% endif %}">
        <label class="form__label" for="customer__password-confirmation">{{ 'customer.reset_password.password_confirmation' | t }}</label>
        <input type="password" id="customer__password-confirmation" name="customer[password_confirmation]" required="required">
      </div>

      <div class="button-wrapper">
        <button type="submit" class="button button--primary">{{ 'customer.reset_password.submit' | t }}</button>
      </div>
      {% endform %}
    </div>
  </section>
</div>