{% if section.settings.show_newsletter %}
  <section class="newsletter">
    <div class="container">
      {% assign form_posted_successfully = false %}
      {% capture newsletter_form_action %}/contact#shopify-section-{{ section.id }}{% endcapture %}

      {%- capture newsletter_form -%}
        {%- form 'customer', action: newsletter_form_action -%}
          {% if form.posted_successfully? %}
            {% assign form_posted_successfully = true %}
          {% endif %}

          <input type="hidden" name="contact[tags]" value="newsletter">
          <input class="newsletter__email" type="email" name="contact[email]" aria-label="{{ 'layout.newsletter.input_placeholder' | t }}" placeholder="{{ 'layout.newsletter.input_placeholder' | t }}">
          <input class="newsletter__submit button" type="submit" value="{{ 'layout.newsletter.submit' | t }}">
        {%- endform -%}
      {%- endcapture -%}

      {% if form_posted_successfully %}
        <h2 class="newsletter__title">{{ 'layout.newsletter.title_success' | t }}</h2>
        <h3 class="newsletter__subtitle">{{ 'layout.newsletter.subtitle_success' | t }}</h3>
      {% else %}
        {%- if section.settings.title != blank -%}
          <h2 class="newsletter__title">{{ section.settings.title }}</h2>
        {%- endif -%}

        {%- if section.settings.subtitle != blank -%}
          <h3 class="newsletter__subtitle">{{ section.settings.subtitle }}</h3>
        {%- endif -%}
      {% endif %}

      {% unless form_posted_successfully %}
        <div class="newsletter__form">
          {{ newsletter_form }}
        </div>
      {% endunless %}
    </div>
  </section>
{% endif %}

{% schema %}
{
  "name": "Newsletter",
  "class": "shopify-section__newsletter",
  "settings": [
    {
      "type": "paragraph",
      "content": "Any customers who sign up will have an account created for them in Shopify. [View customers](/admin/customers?query=&accepts_marketing=1)."
    },
    {
      "type": "paragraph",
      "content": "You can sync your customers with a newsletter service by installing their app."
    },
    {
      "type": "checkbox",
      "id": "show_newsletter",
      "label": "Show newsletter",
      "default": true
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Subscribe to our newsletter"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "Subtitle",
      "default": "And stay on top of the news!"
    }
  ]
}
{% endschema %}