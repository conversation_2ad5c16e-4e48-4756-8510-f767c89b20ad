<header class="page__header">
  {% include 'breadcrumb' %}

  <div class="page__header-wrapper">
    <div class="container">
      {% if template contains 'page' %}
        <h1 class="page__title">{{ page.title }}</h1>
      {% else %}
        <h1 class="page__title">{{ 'collections.general.title' | t }}</h1>
      {% endif %}
    </div>
  </div>
</header>

<div class="inner">
  {% if template contains 'page' %}
    {% assign collections_linklist_handle = page.content | strip_html %}
    {% assign collections_linklist = linklists[collections_linklist_handle] %}
  {% else %}
    {% assign collections_linklist = linklists.all-collections %}
  {% endif %}

  {% if template contains 'page' %}
    {% if page.template_suffix == 'collections-grid' %}
      {% assign layout_mode = 'grid' %}
    {% else %}
      {% assign layout_mode = 'collage' %}
    {% endif %}
  {% else %}
    {% assign layout_mode = section.settings.layout_mode %}
  {% endif %}

  {% if layout_mode == 'collage' %}
    <div class="list-collections list-collections--padded list-collections--collage ftg">
      <div class="ftg-items">
        {% if collections_linklist.links.size > 0 %}
          {% for link in collections_linklist.links %}
            {% if link.type != 'collection_link' %}
              {% continue %}
            {% endif %}

            {% include 'collection-collage-item', collection: link.object %}
          {% endfor %}
        {% else %}
          {% for collection in collections %}
            {% if collection.handle == 'frontpage' %}
              {% continue %}
            {% endif %}

            {% include 'collection-collage-item' %}
          {% endfor %}
        {% endif %}
      </div>
    </div>
  {% else %}
    <div class="list-collections list-collections--padded list-collections--grid grid grid--middle grid--gallery">
      {% assign grid_items_per_row = section.settings.grid_items_per_row %}

      {% if collections_linklist.links.size > 0 %}
        {% for link in collections_linklist.links %}
          {% if link.type != 'collection_link' %}
            {% continue %}
          {% endif %}

          {% include 'collection-grid-item', collection: link.object %}
        {% endfor %}
      {% else %}
        {% for collection in collections %}
          {% if collection.handle == 'frontpage' %}
            {% continue %}
          {% endif %}

          {% include 'collection-grid-item' %}
        {% endfor %}
      {% endif %}
    </div>
  {% endif %}
</div>

{% schema %}
{
  "name": "List of collections page",
  "settings": [
    {
      "type": "select",
      "id": "layout_mode",
      "label": "Layout mode",
      "options": [
        {
          "value": "collage",
          "label": "Collage"
        },
        {
          "value": "grid",
          "label": "Grid"
        }
      ]
    },
    {
      "type": "range",
      "id": "grid_items_per_row",
      "min": 2,
      "max": 6,
      "step": 1,
      "label": "Collections per row",
      "info": "This setting is only used for the grid layout mode.",
      "default": 4
    }
  ]
}
{% endschema %}