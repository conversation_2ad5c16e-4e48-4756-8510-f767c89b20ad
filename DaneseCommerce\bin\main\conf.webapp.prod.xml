<config>
	<info>
		<env>prod</env>
		<appName>DaneseCommerce</appName>
		<version>1.0</version>
		<build>${build}</build>
		<date>@@RELEASEDATE_PLACEHOLDER@@</date> <!-- Injected by gradle -->
	</info>
	<paths>
		<serverAddress>https://www.danesemilano.com/</serverAddress>
		<basePath>/srv/dncprod</basePath>
		<!-- The name is the folder name inside basePath -->
		<!-- The url can either be an absolute url like "/contents", or a full url like "http://cdn.com/artemide/contents" -->
		<contentDir name="contents" url="/contents"> <!-- La url può essere assoluta per distribuire i contenuti -->
		</contentDir>
		<!--Folder inside basePath where files are uploaded -->
		<uploadsDir>uploads</uploadsDir>
		<notificationModalView>/modalNotifyB3</notificationModalView> <!-- Not sure this is actually used -->
	</paths>

	<i18n localePathVariable="true">
		<locale>it</locale>
		<locale default="true">en</locale>
	</i18n>
	
	<maxFileUploadSizeBytes>52428800</maxFileUploadSizeBytes> <!-- 50 mega -->

	<yadaFileManager>
		<deleteUploads>true</deleteUploads>
	</yadaFileManager>

	<sources>
		<source>Led</source>
		<source>Fluo</source>
		<source>Halo</source>
	</sources>

	<size>
		<!-- Image widths for desktop and mobile - mobile is optional -->
		<product>
			<thumbnail>
				<desktop>384</desktop>
			</thumbnail>
			<silhouette>
				<desktop>768</desktop>
			</silhouette>
		</product>
		<news>
			<gallery>
				<desktop>768</desktop>
			</gallery>
			<thumbnail>
				<desktop>768</desktop>
			</thumbnail>
		</news>
		<designer>
			<thumbnail>
				<desktop>768</desktop>
			</thumbnail>
		</designer>
	</size>
	
	<email>
		<enabled>false</enabled>
		<from><EMAIL></from>
		<support>
			<!-- Addresses to which a support request is sent -->
			<to><EMAIL></to>
		</support>
		<logoImage>template.email/logo50.png</logoImage>
		<smtpserver>
			<host>smtp.EXAMPLE.com</host>
			<port>587</port>
			<protocol>smtp</protocol>
			<username><EMAIL></username>
			<password>EXAMPLE</password>
			<!--  If set to true, and a message has some valid and some invalid addresses, send the message anyway, reporting the partial failure with a SendFailedException. If set to false (the default), the message is not sent to any of the recipients if there is an invalid recipient address.  -->
			<properties>mail.smtp.sendpartial=true</properties> 
			<properties>mail.smtp.auth=true</properties>
			<properties>mail.smtp.starttls.enable=true</properties>
			<properties>mail.smtp.quitwait=false</properties>
		</smtpserver>
		<!-- Remove this list to enable email to everyone -->
		<validEmail><EMAIL></validEmail>
		<validEmail><EMAIL></validEmail>
		<validEmail><EMAIL></validEmail>
	</email>

	<database>
		<jndiname>java:comp/env/jdbc/dncdbprod</jndiname>
		<showSql>false</showSql>
		<databaseMigrationAtStartup>true</databaseMigrationAtStartup>
		<entityPackage>com.danesemilano.persistence.entity</entityPackage>
	</database>

	<setup>
		<users>
			<user>
				<name>admin</name>
				<email><EMAIL></email>
				<password>rksdun7x4</password>
				<language>it</language>
				<country>it</country>
				<timezone>Europe/Rome</timezone>
				<role>USER</role>
				<role>CMSUSER</role>
				<role>COMMERCE</role>
				<role>ADMIN</role>
			</user>
		</users>
	</setup>
	
	<security>
		<!-- Questo valore viene usato lato javascript per controllare se la sessione è scaduta -->
		<sessionTimeoutMinutes>240</sessionTimeoutMinutes><!-- ATTENZIONE: questo va tenuto allineato con web.xml altrimenti la sessione potrebbe non scadere mai -->
		<passwordLength min='5' max='128' />
		<encodePassword>true</encodePassword>
		<maxFailedAttempts>10</maxFailedAttempts>
		<failedAttemptsLockoutMinutes>10</failedAttemptsLockoutMinutes>
		<autologinExpirationHours>48</autologinExpirationHours>
		<roles>
			<!-- ATTENZIONE: l'ID e la KEY vanno stabiliti all'inizio e MAI PIU' CAMBIATI perchè vanno nel DB.
					Sono stati scelti valori non consecutivi per le ID in modo da poter eventualmente inserire valori in mezzo, 
					anche se ciò non ha effettivamente senso ma non si sa mai che si voglia un giorno ordinare in base all'ID. -->
			<role>
				<id>8</id>
				<key>ADMIN</key>
				<!-- ATTENZIONE: non usare name e description come fatti qui, ma metterli in messages.properties
				<name>Admin</name>
				<description>Gestione infrastruttura tecnica</description>
				 -->
			</role>
			<role>
				<id>5</id>
				<key>COMMERCE</key>
			</role>
			<role>
				<id>4</id>
				<key>CMSUSER</key>
				<!-- 
				<name>Accede al CMS</name>
				<description>Utente che può accedere al CMS</description>
				 -->
			</role>
			<role>
				<id>2</id>
				<key>USER</key>
				<!-- 
				<name>Utente Verificato</name>
				<description>Utente la cui email è stata verificata</description>
				 -->
			</role>
		</roles>
	</security>
	
		
	<shell>
		<convert>
			<executable>convert</executable>
			<arg>${FILENAMEIN}</arg>
			<arg>${FILENAMEOUT}</arg>
		</convert>
		<resize>
			<executable>convert</executable>
			<arg>${FILENAMEIN}</arg>
			<arg>-resize</arg>
			<arg><![CDATA[${W}x${H}]]></arg>
			<arg>${FILENAMEOUT}</arg>
		</resize>
	</shell>
	
</config>